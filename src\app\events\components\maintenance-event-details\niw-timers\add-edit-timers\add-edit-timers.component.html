<mat-dialog-title class="dialog-title-bar">
  <div class="title-wrapper">
    <span class="dialog-title-text">{{ data.type }} NIW Timer</span>
    <mat-icon class="close-icon" (click)="closeDialog()">cancel</mat-icon>
  </div>
</mat-dialog-title>

<mat-dialog-content class="niw-timer-dialog">
    <p class="timer-label">
      <span class="label">Timer Name:</span>
      <span class="timer-name">{{ timerName }}</span>
    </p>
  <div class="field-row">
    <mat-form-field appearance="outline" class="form-field">
      <mat-label>Start Date</mat-label>
      <input matInput [matDatepicker]="picker" [(ngModel)]="timerData.startDate">
      <mat-hint>MM/DD/YYYY</mat-hint>
      <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
    </mat-form-field>

    <mat-form-field appearance="outline" class="form-field">
      <mat-label>Start Time</mat-label>
      <input matInput [matTimepicker]="picker1" [(ngModel)]="timerData.startTime">
      <mat-hint>HH:MM</mat-hint>
      <mat-timepicker-toggle matIconSuffix [for]="picker1"></mat-timepicker-toggle>
      <mat-timepicker #picker1 [interval]="60"></mat-timepicker>
    </mat-form-field>
  </div>

  <div class="field-row">
    <mat-form-field appearance="outline" class="form-field">
      <mat-label>End Date</mat-label>
      <input matInput [matDatepicker]="picker2" [(ngModel)]="timerData.endDate">
      <mat-hint>MM/DD/YYYY</mat-hint>
      <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>

    <mat-form-field appearance="outline" class="form-field">
      <mat-label>End Time</mat-label>
      <input matInput [matTimepicker]="picker3" [(ngModel)]="timerData.endTime">
      <mat-hint>HH:MM</mat-hint>
      <mat-timepicker-toggle matIconSuffix [for]="picker3"></mat-timepicker-toggle>
      <mat-timepicker #picker3 [interval]="60"></mat-timepicker>
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end" class="dialog-actions">
  <button mat-raised-button class="btn-purple" (click)="saveNiwTimer()">Save</button>
</mat-dialog-actions>