.title {
    background: linear-gradient(135deg, #3f2876, #6c49b9);
    color: #ffffff;
    text-align: center;
    padding: 5px 12px;
    font-size: 17px;
    font-weight: 600;
    border-top-right-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
}

.column-settings-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20px;
  gap: 30px;
  font-family: "Segoe UI", Roboto, sans-serif;
  background-color: #f6f4fb;
  border-radius: 8px;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  box-sizing: border-box;
}

.dialog-content {
  padding: 0;
  overflow-x: hidden; /* NEW: hide any horizontal scroll */
  box-sizing: border-box;
  max-width: 100%;
}

.column-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200px;
}

.column-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #4a327d;
  text-align: center;
}

.column-list {
  background: #ffffff;
  border: 1px solid #dcd3f2;
  border-radius: 8px;
  width: 100%;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  box-shadow: 0 2px 6px rgba(108, 73, 185, 0.05);
  transition: box-shadow 0.2s;
}

.column-list:hover {
  box-shadow: 0 4px 12px rgba(108, 73, 185, 0.15);
}

.column-item {
  background-color: #ede6f9;
  border: 1px solid #c0a8f0;
  color: #6c49b9;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 14px;
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.column-item:hover {
  background-color: #d9c8f4;
  transform: scale(1.01);
}

.cdk-drag-preview {
  border-radius: 6px;
  box-shadow: 0 4px 14px rgba(108, 73, 185, 0.3);
  background-color: #ffffff;
  color: #6c49b9;
}

.vertical-divider {
  width: 2px;
  background-color: #ddd4ee;
  margin-top: 30px;
  border-radius: 1px;
}

.applyButton {
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: white !important;
  border-radius: 10px;
  min-width: 90px;
  height: 36px;
  font-size: 14px;
  padding: 6px 12px;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 10px;
  background-color: #f6f4fb;
  border-top: 1px solid #ddd4ee;
}

.closeButton {
  background: linear-gradient(135deg, #cc5200, #ff6600) !important;
  color: white !important;
  border-radius: 10px;
  min-width: 90px;
  height: 36px;
  font-size: 14px;
  padding: 6px 12px;
}

.disabled {
    background-color: lightgray !important;
}