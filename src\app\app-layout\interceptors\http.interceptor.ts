import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpH<PERSON>ler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize, switchMap, take } from 'rxjs/operators';
import { SpinnerService } from '../services/spinner.service';
import { OktaAuthStateService } from '@okta/okta-angular';

@Injectable()
export class HttpInterceptorService implements HttpInterceptor {

  constructor(
    private spinnerService: SpinnerService,
    private oktaAuthStateService: OktaAuthStateService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return this.oktaAuthStateService.authState$.pipe(
      take(1),
      switchMap(authState => {
        let modifiedRequest = request;

        if (authState?.isAuthenticated && authState.accessToken) {
          modifiedRequest = request.clone({
            setHeaders: {
              'Authorization': `Bearer ${authState.accessToken.accessToken}`
            },
            withCredentials: true   // <-- added here
          });
        } else {
          modifiedRequest = request.clone({
            withCredentials: true   // <-- added here even if no Authorization
          });
        }

        if (!modifiedRequest.url.includes("/preferences/setPreferences")) {
          this.spinnerService.showSpinner();
        }

        return next.handle(modifiedRequest).pipe(
          finalize(() => {
            if (!modifiedRequest.url.includes("/preferences/setPreferences")) {
              this.spinnerService.hideSpinner();
            }
          })
        );
      })
    );
  }
}