<div class="container">
    <div class="form-container">

        <!-- Add Event Form -->
        <div *ngIf="isAddEventSelected">
            <!-- Dynamic Form Title -->
            <p class="intakeFormTitle">{{ selectedForm.intakeFormNm }}</p>

            <!-- Radio buttons for form selection -->
            <div *ngIf="intakeFormResponse.length > 1" class="radio-container">
                <mat-radio-group [(ngModel)]="selectedFormIndex" (change)="onFormSelectionChange(selectedFormIndex)">
                    <mat-radio-button style="font-size: 13px;" *ngFor="let form of intakeFormResponse; let i = index" [value]="i">
                        {{ form.intakeFormNm }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>

            <span>Please select / fill all required fields</span>

            <!-- Display Selected Form -->
            <form [formGroup]="addEventIntakeForm" class="form-container">
                <div *ngFor="let question of selectedForm?.intakeForm?.questions" class="form-group">
                    <!-- Question Label -->
                    <label class="question-label">
                        <span *ngIf="!question.required" class="required-symbol noColor">*</span>
                        <span *ngIf="question.required" class="required-symbol">*</span>
                        {{ question.questionTxt }}
                    </label>

                    <!-- Answer Section -->
                    <div class="answer-section">
                        <!-- Multiple Choice (Checkbox) -->
                        <ng-container *ngIf="question.questionGrp === 'MULTIPLE'">
                            <mat-checkbox *ngFor="let answer of question.answers" 
                                        [value]="answer.answerTxt"
                                        (change)="onCheckboxChange($event, question.questionId)">
                                {{ answer.answerTxt }}
                            </mat-checkbox>
                        </ng-container>

                        <!-- Radio Buttons -->
                        <mat-radio-group *ngIf="question.questionGrp === 'RADIO'" [formControlName]="question.questionId">
                            <mat-radio-button *ngFor="let answer of question.answers" [value]="answer.answerTxt">
                                {{ answer.answerTxt }}
                            </mat-radio-button>
                        </mat-radio-group>

                        <!-- Text Input -->
                        <mat-form-field *ngIf="question.questionGrp === 'TEXT'" appearance="outline">
                            <mat-label>Type Answer Here</mat-label>
                            <textarea matInput [formControlName]="question.questionId" rows="3" style="resize: both;"></textarea>
                        </mat-form-field>

                        <!-- Date Input -->
                        <mat-form-field class="dateRangeFeild" *ngIf="question.questionGrp === 'DATE'" appearance="outline">
                            <mat-label>Select Date</mat-label>
                            <input matInput [matDatepicker]="datePickerRef" [formControlName]="question.questionId"
                                   (dateChange)="onDateChange(question.questionId.toString(), $event)" placeholder="MM/dd/yyyy">
                            <mat-datepicker-toggle matSuffix [for]="datePickerRef"></mat-datepicker-toggle>
                            <mat-datepicker #datePickerRef></mat-datepicker>
                        </mat-form-field>

                        <!-- Date Range Input -->
                        <div *ngIf="question.questionGrp === 'DATE_RANGE'" class="date-range-container">
                            <mat-form-field class="dateRangeFeild from-field" appearance="outline">
                                <mat-label>From</mat-label>
                                <input matInput [matDatepicker]="datePickerFromRef" [formControlName]="question.questionId + '_from'"
                                       (dateChange)="onDateChange(question.questionId + '_from', $event)" placeholder="MM/dd/yyyy">
                                <mat-datepicker-toggle matSuffix [for]="datePickerFromRef"></mat-datepicker-toggle>
                                <mat-datepicker #datePickerFromRef></mat-datepicker>
                            </mat-form-field>

                            <span class="to-text">To</span>

                            <mat-form-field class="dateRangeFeild to-field" appearance="outline">
                                <mat-label>To</mat-label>
                                <input matInput [matDatepicker]="datePickerToRef" [formControlName]="question.questionId + '_to'"
                                       (dateChange)="onDateChange(question.questionId + '_to', $event)" placeholder="MM/dd/yyyy">
                                <mat-datepicker-toggle matSuffix [for]="datePickerToRef"></mat-datepicker-toggle>
                                <mat-datepicker #datePickerToRef></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Details Form -->
        <div *ngIf="!isAddEventSelected">
            <!-- Dynamic Form Title -->
            <p class="intakeFormTitle">{{ detailsIntakeResponse.intakeFormName }}</p>

            <!-- <span>Please review the details below</span> -->

            <!-- Display Details Form -->
            <form [formGroup]="detailsEventIntakeForm" class="form-container">
                <div *ngFor="let question of detailsIntakeResponse.questions" class="form-group">
                    <!-- Question Label -->
                    <label class="question-label">
                        <span *ngIf="!question.required" class="required-symbol noColor">*</span>
                        <span *ngIf="question.required" class="required-symbol">*</span>
                        {{ question.questionTxt }}
                    </label>

                    <!-- Answer Section -->
                    <div class="answer-section">
                        <!-- Multiple Choice (Checkbox) -->
                        <ng-container *ngIf="question.questionGrp === 'MULTIPLE'">
                            <mat-checkbox *ngFor="let answer of question.answers" 
                                        [value]="answer.answerTxt"
                                        [checked]="detailsEventIntakeForm.get(question.questionId.toString())?.value?.includes(answer.answerTxt)"
                                        [disabled]="question.answered"
                                        (change)="onDetailsCheckboxChange($event, question.questionId)">
                                {{ answer.answerTxt }}
                            </mat-checkbox>
                        </ng-container>

                        <!-- Radio Buttons -->
                        <mat-radio-group *ngIf="question.questionGrp === 'RADIO'" [formControlName]="question.questionId" [disabled]="question.answered">
                            <mat-radio-button *ngFor="let answer of question.answers" [value]="answer.answerTxt" [checked]="answer.selected">
                                {{ answer.answerTxt }}
                            </mat-radio-button>
                        </mat-radio-group>

                        <!-- Text Input -->
                        <mat-form-field *ngIf="question.questionGrp === 'TEXT'" appearance="outline">
                            <mat-label>Type Answer Here</mat-label>
                            <textarea matInput  [formControlName]="question.questionId" rows="3" style="resize: both;" [disabled]="question.answered"></textarea>
                        </mat-form-field>

                        <!-- Date Input -->
                        <mat-form-field class="dateRangeFeild" *ngIf="question.questionGrp === 'DATE'" appearance="outline">
                            <mat-label>Select Date</mat-label>
                            <input matInput [matDatepicker]="datePickerRef" [formControlName]="question.questionId"
                                   (dateChange)="onDetailsDateChange(question.questionId.toString(), $event)" [disabled]="question.answered" placeholder="MM/dd/yyyy">
                            <mat-datepicker-toggle matSuffix [for]="datePickerRef"></mat-datepicker-toggle>
                            <mat-datepicker #datePickerRef></mat-datepicker>
                        </mat-form-field>

                        <!-- Date Range Input -->
                        <div *ngIf="question.questionGrp === 'DATE_RANGE'" class="date-range-container">
                            <mat-form-field class="dateRangeFeild from-field" appearance="outline">
                                <mat-label>From</mat-label>
                                <input matInput [matDatepicker]="datePickerFromRef" [formControlName]="question.questionId + '_from'"
                                       (dateChange)="onDetailsDateChange(question.questionId + '_from', $event)"
                                       [disabled]="question.answered" placeholder="MM/dd/yyyy">
                                <mat-datepicker-toggle matSuffix [for]="datePickerFromRef"></mat-datepicker-toggle>
                                <mat-datepicker #datePickerFromRef></mat-datepicker>
                            </mat-form-field>

                            <span class="to-text">To</span>

                            <mat-form-field class="dateRangeFeild to-field" appearance="outline">
                                <mat-label>To</mat-label>
                                <input matInput [matDatepicker]="datePickerToRef" [formControlName]="question.questionId + '_to'"
                                       (dateChange)="onDetailsDateChange(question.questionId + '_to', $event)"
                                       [disabled]="question.answered" placeholder="MM/dd/yyyy">
                                <mat-datepicker-toggle matSuffix [for]="datePickerToRef"></mat-datepicker-toggle>
                                <mat-datepicker #datePickerToRef></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>