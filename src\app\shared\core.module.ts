import { NgModule, importProvidersFrom, LOCALE_ID } from '@angular/core';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideZoneChangeDetection } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { OktaAuthModule, OKTA_CONFIG } from '@okta/okta-angular';
import OktaAuth from '@okta/okta-auth-js';

// Services
import { AppLayoutService } from '../app-layout/services/app-layout.service';
import { SpinnerService } from '../app-layout/services/spinner.service';
import { HttpInterceptorService } from '../app-layout/interceptors/http.interceptor';
import { ErrorInterceptorService } from '../app-layout/interceptors/error-interceptor.service';
import { UserLoginService } from '../app-layout/services/user-login.service';
import { EnvironmentService } from '../app-layout/services/environment.service';
import { MainService } from '../app-layout/services/main.service';
import { AddEventService } from '../events/services/add-event.service';
import { MaintenanceEventListService } from '../events/services/maintenance-event-list.service';
import { MaintenanceEventDetailsService } from '../events/services/maintenance-event-details.service';
import { environment } from '../../environments/environment';
import { ToastrMessageService } from '../app-layout/services/toastr-message.service';

const oktaAuth = new OktaAuth(environment.okta);

@NgModule({
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideAnimationsAsync(),
    provideHttpClient(withInterceptorsFromDi()),
    { provide: OKTA_CONFIG, useValue: { oktaAuth } },
    { provide: OktaAuth, useValue: oktaAuth },
    { provide: HTTP_INTERCEPTORS, useClass: HttpInterceptorService, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptorService, multi: true },
    importProvidersFrom(OktaAuthModule),
    AppLayoutService,
    UserLoginService,
    EnvironmentService,
    MainService,
    SpinnerService,
    AddEventService,
    MaintenanceEventListService,
    MaintenanceEventDetailsService,
    ToastrMessageService,
    { provide: LOCALE_ID, useValue: 'en-GB' }
  ],
})
export class CoreModule {}
