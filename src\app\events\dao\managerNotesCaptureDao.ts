export interface managerNotesCaptureDao {
    title: string;
    countOfObjects: number;
}

export class managerNotesCaptureHandler {
    private objectMap: Map<string, managerNotesCaptureDao>;

    constructor(initialObjects: managerNotesCaptureDao[] = []) {
        this.objectMap = new Map(initialObjects.map(obj => [obj.title, obj]));
    }

    setObjectsCount(title: string, count: number) {
        const existingObject = this.objectMap.get(title);
        if (existingObject) {
            existingObject.countOfObjects = count;
        } else {
            console.warn(`Object with title "${title}" not found.`);
        }
    }

    getObjectByTitle(title: string): managerNotesCaptureDao | undefined {
        return this.objectMap.get(title);
      }


}