export const STATUS_COLOR_MAP = new Map<string, string>([
  ['B', '#ff0000'],
  ['D', '#ff0000'],
  ['U', '#008000'],
  ['M', '#ff0000'],
  ['300', '#ffff00'],
  ['305', '#ffff00'],
  ['310', '#ffff00'],
  ['315', '#ffff00'],
  ['320', '#ffff00'],
  ['325', '#ffff00'],
  ['330', '#ffff00'],
  ['350', '#ffff00'],
  ['355', '#ffff00'],
  ['400', '#ff0000'],
  ['410', '#ff0000'],
  ['420', '#ff0000'],
  ['430', '#ff0000'],
  ['440', '#00ffff'],
  ['450', '#00ffff'],
  ['460', '#ff0000'],
  ['480', '#ff0000'],
  ['490', '#ff0000'],
  ['500', '#808080'],
  ['E', '#808080'],
  ['100', '#008000'],
  ['110', '#006400'],
  ['510', '#ff0000'],
  ['520', '#ffff00'],
  ['900', '#ffff00']
]);

// Text color map based on background for best readability
export const TEXT_COLOR_MAP = new Map<string, string>([
  ['#ff0000', '#ffffff'], // red -> white
  ['#ffff00', '#000000'], // yellow -> black
  ['#008000', '#ffffff'], // green -> white
  ['#006400', '#ffffff'], // dark green -> white
  ['#00ffff', '#000000'], // cyan -> black
  ['#808080', '#ffffff'], // grey -> white
]);

export const ROLE_AND_EVENT_MAP = new Map<number, string>([
  [1, 'MOC'],
  [2, 'LINE'],
  [3, 'TLD'],
  [1001, 'TRK'],
  [1111, 'NOTE'],
  [1100, 'DOA'],
]);