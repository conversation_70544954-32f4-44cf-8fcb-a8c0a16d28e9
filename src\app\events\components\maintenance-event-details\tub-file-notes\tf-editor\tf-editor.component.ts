import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TubFilesNotesRequestDto } from '../../../../dto/TubFileNotesRequestDto';
import { TubFileNotesResponseDto } from '../../../../dto/TubFileNotesResponseDto';
import { TubFileNotesStatusUpdateDto } from '../../../../dto/TubFileNotesStatusUpdate';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { TfnoteService } from '../../../../services/tfnote.service';

@Component({
  selector: 'app-tf-editor',
  standalone: false,
  templateUrl: './tf-editor.component.html',
  styleUrl: './tf-editor.component.scss'
})
export class TfEditorComponent {
     @Input() editorTitle: string = 'Add Tub File Notes';

    new_tub_file_note!: string;
    existing_tub_file_notes!: string[];
    // eventId!: string;
    tubfilenotesrequest !: TubFilesNotesRequestDto;
    tubfilenotesresponse !: TubFileNotesResponseDto[];
    tubfilenotesupdaterequest !: TubFileNotesStatusUpdateDto;
    notes!: TubFileNotesResponseDto[];
    sortOrder!: string;
    sortedNotes!: TubFileNotesResponseDto[];
    acn!: string;
    editorContent: string = '';
    attachedFiles: File[] = [];
    linkedDiscrepancies: string[] = [];
    selectedDisc: any;
     quillInstance: any;
    

  constructor(private service: MaintenanceEventDetailsService,
    private noteService: TfnoteService,
     public dialogRef: MatDialogRef<TfEditorComponent>,
      @Inject(MAT_DIALOG_DATA) public data: any){
    if (data && data.tfNote) {
      this.editorContent = data.tfNote; 
    }
     if (this.data?.editorTitle) {
      this.editorTitle = this.data.editorTitle;
    }
    if(data && data.linkedDiscs){
      this.data.linkedDiscs.forEach((discrepancy: any) => {
        this.linkedDiscrepancies.push(discrepancy.discType + ' / ' + discrepancy.ata+' / '+ discrepancy.number + ' ('+discrepancy.text+')');
      });
      this.linkedDiscrepancies.push('General Notes');
    }
  }
 
    quillModules = {
      toolbar: {
        container: [
          ['attachment'] 
        ],
        handlers: {
          'attachment': () => {
          }
        }
      }
    };
  
  onFileChange(event: any) {
    this.attachedFiles = Array.from(event.target.files);
  }

  onEditorCreated(quill: any) {
    this.quillInstance = quill;
  }

  onDiscChange(value: string) {
    if (this.quillInstance) {
      const range = this.quillInstance.getSelection(true);
      const insertText = `${value}:\n`;

      this.quillInstance.insertText(range.index, insertText, 'user');
      this.quillInstance.setSelection(range.index + insertText.length, 0);
      
      setTimeout(() => {
            this.quillInstance.focus();
            this.quillInstance.scrollIntoView();
          }, 0);

    }
  }

  // onDiscChange(event:any){
  //   this.editorContent=event+':'+'\n';
  // }

  submit() {
        console.log('Content:', this.editorContent);
        console.log('Files:', this.attachedFiles);
        console.log('editorTitle',this.editorTitle);

       const html = this.editorContent || '';
      const tempDiv = document.createElement('div');

      // Replace <br> and </p> with newlines
      const formattedHtml = html
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p>/gi, '\n')
        .replace(/<p[^>]*>/gi, '');

      tempDiv.innerHTML = formattedHtml;

      // Now extract plain text with line breaks preserved
      const plainText = tempDiv.textContent || tempDiv.innerText || '';

        const formData = new FormData();

    formData.append('content', this.editorContent);

        this.attachedFiles.forEach((file, index) => {
          formData.append('attachments', file, file.name);
        });
        
        this.tubfilenotesupdaterequest = new TubFileNotesStatusUpdateDto();
        this.tubfilenotesupdaterequest.mode = "TF_NOTES";
        this.tubfilenotesupdaterequest.tf_notes_data.changeType = 7;
        this.tubfilenotesupdaterequest.tf_notes_data.eventId = Number(this.data.eventId);
        this.tubfilenotesupdaterequest.tf_notes_data.empNumber = this.data.userInfoDetail.uid;
        this.tubfilenotesupdaterequest.tf_notes_data.empName = this.data.userInfoDetail.name;
        this.tubfilenotesupdaterequest.tf_notes_data.empDepartment = this.data.userInfoDetail.departmentname;
        this.tubfilenotesupdaterequest.tf_notes_data.editedFlag = "N";
        // this.tubfilenotesupdaterequest.tf_notes_data.tfNote = this.editorContent?.toLowerCase?.();
        this.tubfilenotesupdaterequest.tf_notes_data.tfNote = plainText.toLowerCase();
        this.tubfilenotesupdaterequest.tf_notes_data.noteType = 1;
        this.tubfilenotesupdaterequest.tf_notes_data.noteId = 0;
    
        // this.tubfilenotesupdaterequest.event_id = this.eventId;
        this.tubfilenotesupdaterequest.event_id=this.data.eventId;
        if(this.editorTitle=='Add Tub File Note'){
            this.tubfilenotesupdaterequest.flag = "ADD";
        }
        else if(this.editorTitle=='Edit Tub File Note'){
            this.tubfilenotesupdaterequest.flag = "EDIT";
             this.tubfilenotesupdaterequest.tf_notes_data.editedFlag = "Y";
            const timestamp = this.data.tfNoteDate;
            const date = new Date(timestamp);
            const localDateStr = date.toLocaleString('en-GB', { 
                timeZone: 'Asia/Kolkata', 
                hour12: false 
              }).replace(',', ''); 
              const [datePart, timePart] = localDateStr.split(' '); 
              const [day, month, year] = datePart.split('/');
              const formattedDate = `${year}-${month}-${day} ${timePart}`;

        this.tubfilenotesupdaterequest.tf_notes_data.tfDateTime=formattedDate;
    }

    this.service.updateTubFileNotes(this.tubfilenotesupdaterequest).subscribe(response => {
      this.noteService.notifyNoteUpdated(); 
      this.dialogRef.close(response);
    });
  }
}
