import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChangeStatusEticComponent } from './change-status-etic.component';

describe('ChangeStatusEticComponent', () => {
  let component: ChangeStatusEticComponent;
  let fixture: ComponentFixture<ChangeStatusEticComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ChangeStatusEticComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ChangeStatusEticComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
