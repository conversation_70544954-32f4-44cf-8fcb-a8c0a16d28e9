import { Component, OnInit, inject, <PERSON>Child, ElementRef, AfterViewInit, OnDestroy, Renderer2 } from '@angular/core';
import { AddEventData } from '../../../dao/addEvent-Data';
import { debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MainService } from '../../../../app-layout/services/main.service';
import { MatDialog } from '@angular/material/dialog';
import { AddEventService } from '../../../services/add-event.service';
import { AddEventConfirmationPopupComponent } from '../add-event-confirmation-popup/add-event-confirmation-popup.component';
import { AppLayoutService } from '../../../../app-layout/services/app-layout.service';
import { AddMaintenanceEventComponent } from '../add-maintenance-event.component';
import { ToastrMessageService } from '../../../../app-layout/services/toastr-message.service';

@Component({
  selector: 'app-add-event-header',
  standalone: false,
  templateUrl: './add-event-header.component.html',
  styleUrl: './add-event-header.component.scss'
})
export class AddEventHeaderComponent implements OnInit {

  myForm!: FormGroup;
  isValidData: boolean = false;
  isDisabled: boolean = true;
  sideNavClosed: boolean | null = false;
  eventType: string[] = AddEventData.events;
  passOnAcn: string = '';
  passOnEventType: string = '';

  @ViewChild('mainDiv') mainDiv!: ElementRef<HTMLDivElement>;
  @ViewChild(AddMaintenanceEventComponent) childComponent!: AddMaintenanceEventComponent;

  constructor(
    private fb: FormBuilder,
    private mainService: MainService,
    private addEventService: AddEventService,
    private appLayoutService: AppLayoutService,
    private toastrMessageService: ToastrMessageService,
    private dialog: MatDialog
  ) {
    const sideNavClosedValue = this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage();
    this.appLayoutService.setSideNavClosedFromPreferences(sideNavClosedValue != null ? sideNavClosedValue?.sideNavClosed : false);
  }

  ngOnInit(): void {
    this.mainService.deselectSelectedTabInSessionStorage();
    this.myForm = this.fb.group({
      acn: ['', Validators.required],
      eventType: ['', Validators.required],
    });

    this.appLayoutService.sideNavClosedObservable$.subscribe((isClosed) => {
      this.sideNavClosed = isClosed;
    });

    this.addEventService.addNewEvent$.subscribe((isAddNewEvent: boolean) => {
      if (isAddNewEvent) {
        this.myForm.reset();
        this.isValidData = false;
        this.isDisabled = true;
        this.passOnAcn = '';
        this.passOnEventType = '';
      }
    })

    this.myForm.valueChanges.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      filter(formValues => {
        const acnStr = formValues.acn ? formValues.acn.toString() : '';
        return (formValues.eventType && acnStr.length >= 3);
      })
    ).subscribe(formValues => {
      this.onSubmit();
    });
  }

  callChildSubmitAndReset(value: any): void {         
    if (this.childComponent) {
      value === 'save' ? this.childComponent.onSubmit() : this.childComponent.resetForm();
    } else {
      console.warn('Child component not available!');
    }
  }

  onSubmit(): void {
    if (this.myForm.valid) {
      const enteredAcn = this.myForm.value['acn'];
      this.addEventService.validateAcnExists(enteredAcn).subscribe({
        next: (acnExists) => {
          if (!acnExists) {
            this.toastrMessageService.error(`ACN ${enteredAcn} does not exist. Please enter a valid ACN.`, 'Invalid ACN');
            return;
          }

          let validateReqPayload = { ...AddEventData.addEventForm };
          validateReqPayload['acn'] = enteredAcn;
          validateReqPayload['eventType'] = this.myForm.value['eventType'];
          this.addEventService.validateAcnData(validateReqPayload).subscribe({
            next: (result) => {
              let msg = result['data']['ERROR'];
              if (msg === "" || result['data']['ADD_EVENT_FLAG'] === true) {
                this.isValidData = true;
              } else {
                const dialogRef = this.dialog.open(AddEventConfirmationPopupComponent, {
                  data: {
                    disableClose: true,
                    autoFocus: false,
                    width: '50vw',
                    maxWidth: '90vw',
                    message: result['data'],
                    event: this.myForm.value['eventType']
                  },
                  panelClass: 'custom-addEvent-confirmation-dialog-container'
                });
                dialogRef.afterClosed().subscribe(result => {
                  if (result === 'YES' || result === 'CONTINUE') {
                    this.isValidData = true;
                  } else {
                    this.isValidData = false;
                    this.myForm.reset();
                  }
                });
              }
              this.passOnAcn = enteredAcn;
              this.passOnEventType = this.myForm.value['eventType'];
            },
            error: (err) => {
              this.toastrMessageService.error('Error validating ACN data. Please try again.', 'Validation Error');
              console.error('Validation error:', err);
            }
          });
        },
        error: (err) => {
          this.toastrMessageService.error('Error checking ACN existence. Please try again.', 'ACN Check Error');
          console.error('ACN check error:', err);
        }
      });
    } else {
      console.log("Invalid Form", this.myForm);
    }
  }

  onValidForm(isValid: any) {
    this.isDisabled = !isValid;
  }

}
