{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mets-server-nextgen-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/mets-server-nextgen-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.scss", "node_modules/ngx-toastr/toastr.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ag-grid-community/styles/ag-theme-alpine.css", "node_modules/quill/dist/quill.snow.css"], "scripts": ["node_modules/@popperjs/core/dist/umd/popper.min.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "3MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "30kB", "maximumError": "30kB"}], "outputHashing": "all"}, "release": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": false, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.rel.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "prod": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": false, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "rel": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": false, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.rel.ts"}]}, "dev": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": false, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"buildTarget": "mets-server-nextgen-ui:build:production"}, "release": {"buildTarget": "mets-server-nextgen-ui:build:release"}, "development": {"buildTarget": "mets-server-nextgen-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.scss", "node_modules/ngx-toastr/toastr.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ag-grid-community/styles/ag-theme-alpine.css", "node_modules/quill/dist/quill.snow.css"], "scripts": ["node_modules/@popperjs/core/dist/umd/popper.min.js"]}}}}}, "cli": {"analytics": false}}