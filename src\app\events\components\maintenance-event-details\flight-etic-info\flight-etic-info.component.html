<div class="container">
  <div class="header-container">
    <p class="title" [ngClass]="{ 'fill-animation': animateTitle }">Flight / ETIC Info</p>
  </div>
  <div class="etic-statistics-container">
      <div class="etic-title-container">
          <h3 class="etic-title">Etic Statistics</h3>
      </div>
      <div class="etic-statistics-content">
        <div class="label-value">
          <span class="label">Initial:</span> 
          <span class="value">{{ eventFlightEticDetails!.initialEtic || "" }}</span>
        </div>
        <div class="label-value">
          <span class="label"># of ETICs after initial:</span> 
          <span class="value">{{ eventFlightEticDetails!.eticNumber || "" }}</span>
        </div>
        <div class="label-value">
          <span class="label">Past Due:</span> 
          <span class="value">{{ eventFlightEticDetails!.pastDue || "" }}</span>
        </div>
      </div>
  </div>

  <div class="actual-flight-info-container">
    <div class="actual-flight-info-title-container">
        <h3 class="actual-flight-info-title">Actual Flight Info</h3>
    </div>
    <div class="actual-flight-info-content">
      <div class="label-value">
        <span class="label">Flt In:</span> 
        <span class="value">{{ eventFlightEticDetails!.fltIn || "" }}</span>
      </div>
      <div class="label-value">
        <span class="label">Flt Out:</span> 
        <span class="value">{{ eventFlightEticDetails!.fltOut || "" }}</span>
      </div>
      <div class="label-value">
        <span class="label">Total Ground Time:</span> 
        <span class="value">{{ eventFlightEticDetails!.totalGroundTime || "" }}</span>
      </div>
    </div>
    <div class="actual-flight-info-content">
      <div class="label-value">
        <span class="label">Arrival:</span> 
        <span class="value">{{ eventFlightEticDetails!.arrival || "" }}</span>
      </div>
      <div class="label-value">
        <span class="label">Departure:</span> 
        <span class="value">{{ eventFlightEticDetails!.departure || "" }}</span>
      </div>
    </div>
  </div>

  <div class="affected-Outbound-flight-container">
      <div class="affected-Outbound-flight-title-container">
          <h3 class="affected-Outbound-flight-title">Affected Outbound Flight</h3>
      </div>
      <div class="etic-statistics-content">
        <div class="label-value">
          <span class="label">Flt No | Flt Date | Flt Leg:</span> 
          <span class="value">{{ eventFlightEticDetails!.flightNumber  || "" }}{{" | "}}{{ eventFlightEticDetails!.flightDate || "" }}{{ " | " }}{{ eventFlightEticDetails!.flightLegNumber || "" }}</span>
        </div>
        <div class="label-value">
          <span class="label">Sched Dept:</span> 
          <span class="value">{{ eventFlightEticDetails!.scheduledDeparture || "" }}</span>
        </div>
        <div class="label-value">
          <span class="label">Actual Dept:</span> 
          <span class="value">{{ eventFlightEticDetails!.actualDeparture || "" }}</span>
        </div>
      </div>
      <div class="affected-Outbound-flight-content">
          <div class="label-value">
            <span class="label">Dest:</span> 
            <span class="value">{{ eventFlightEticDetails!.destination || "" }}</span>
          </div>
          <div class="label-value">
            <span class="label">Status:</span> 
            <span class="value">{{ eventFlightEticDetails!.flightStatus || "" }}</span>
          </div>
          <div class="label-value">
              <span class="label">Type:</span> 
              <span class="value">{{ eventFlightEticDetails!.flightType || "" }}</span>
          </div>
          <div class="label-value">
            <span class="label">Total Delay:</span> 
            <span class="value">{{ eventFlightEticDetails!.totalDelay || "" }}</span>
          </div>
          <div class="label-value">
              <span class="label">Delay Codes:</span> 
              <span class="value">{{ eventFlightEticDetails!.delayCodes || "" }}</span>
          </div>
      </div>
  </div>
</div>