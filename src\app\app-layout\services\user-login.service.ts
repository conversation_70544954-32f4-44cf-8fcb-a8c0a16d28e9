import { Injectable } from '@angular/core';
import { BehaviorSubject, filter, firstValueFrom, Observable } from 'rxjs';
import { EnvironmentService } from './environment.service';
import { OktaAuthStateService } from '@okta/okta-angular';
import { HttpClient } from '@angular/common/http';
import OktaAuth from '@okta/okta-auth-js';
import { DssPermission, LoggedUserDetails } from '../interfaces/employee-details.interface';
import { SessionStorageKeys } from '../../events/constants/sessionStorageKeys';

interface OktaError {
  error: string;
  error_description: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserLoginService {

  private authenticated = new BehaviorSubject<boolean>(false);
  private authReady = new BehaviorSubject<boolean>(false);

  private retrievedUserDetails = new BehaviorSubject<any>([]);
  retrievedUserDetails$ = this.retrievedUserDetails.asObservable();
  error = '';

  constructor(
    private environmentService: EnvironmentService,
    private oktaAuthStateService: OktaAuthStateService,
    private http: HttpClient,
    private oktaAuth: OktaAuth
  ) {   
    this.initializeAuth();
  }

  async getUser(): Promise<any> {
    const user = await this.oktaAuth.getUser();
    user?.name != null && user?.name != "" ? this.retrievedUserDetails.next(user) : null;
    return user
  }

  login() {
    this.oktaAuth.signInWithRedirect();
  }

  async initializeAuth(): Promise<void> {
    //check if it's callback from okta
    if (window.location.href.includes('callback')) {
      this.handleLoginRedirect();
    } else {
      //if valid tokens exist, restore them
      await this.restoreTokensFromStorage();
    }

    setTimeout(() => {
      this.handleAuthState();
    }, 1000);
  }

  async handleLoginRedirect() {
    try {
      await this.oktaAuth.handleLoginRedirect();
    } catch (error) {
      if (error && (error as OktaError).error === 'access_denied') {
        this.error = (error as OktaError).error_description;
      }
    }
  }

  handleAuthState() {
    this.oktaAuthStateService.authState$
      .pipe(filter((state) => state !== null && typeof state.isAuthenticated === 'boolean'))
      .subscribe(async (authState) => {
        
        const isCallbackUrl = window.location.pathname.includes('authorization-code/callback');
  
        if (!authState.isAuthenticated && !this.error && !isCallbackUrl) {
          this.login();
        } else if (this.error) {
          this.authReady.next(true);
        } else {
          this.storeTokens(authState.accessToken, authState.idToken);
          await this.loadUserDetails();
          this.authenticated.next(true);
          this.authReady.next(true);
        }
      });
  }
  

  storeTokens(accessToken: any, idToken: any) {
    sessionStorage.setItem(SessionStorageKeys.OKTA_TOKEN_STORAGE, JSON.stringify({ accessToken: accessToken, idToken: idToken }));
  }

  async restoreTokensFromStorage(): Promise<void> {
    const tokenData = JSON.parse(sessionStorage.getItem(SessionStorageKeys.OKTA_TOKEN_STORAGE) || '{}');
    const accessToken = tokenData.accessToken;
    const idToken = tokenData.idToken;
    const now = Math.floor(Date.now() / 1000);

    if (accessToken && idToken && accessToken.isExpiredAt > now && idToken.isExpiredAt > now)
      this.oktaAuth.tokenManager.setTokens({ accessToken: accessToken, idToken: idToken });

    await this.oktaAuth.authStateManager.updateAuthState();
  }

  isAuthReady(): Observable<boolean> {
    return this.authReady.asObservable();
  }

  isAuthenticated(): Observable<boolean> {
    return this.authenticated.asObservable();
  }

  // New logout method
  async logout(): Promise<void> {
    try {
      // Clear tokens from sessionStorage
      sessionStorage.removeItem(SessionStorageKeys.OKTA_TOKEN_STORAGE);
      sessionStorage.removeItem(SessionStorageKeys.LOGGED_USER_DETAILS);

      // Clear tokens from Okta tokenManager
      this.oktaAuth.tokenManager.clear();

      // Reset authentication state
      this.authenticated.next(false);
      this.authReady.next(false);

      // Sign out from Okta and redirect to the Okta login page
      await this.oktaAuth.signOut();
    } catch (error) {
      console.error('Error during logout:', error);
      // Optionally handle the error (e.g., show a message to the user)
    }
  }

  async loadUserDetails() {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken');
  
      if (!accessToken) {
        console.warn('Access Token not found, skipping user info load');
        return;
      }
  
      const [oktaUser] = await Promise.all([
        //load user details from okta
        this.getUser()
        //load dss permissions
      ]);
      const loggedUserDetails: LoggedUserDetails = { 
        id: oktaUser?.employeeNumber ?? '',
        name: oktaUser?.name ?? '',
        dssPermissions: [],
        firstName:  oktaUser?.given_name ?? '',
        lastName: oktaUser?.family_name ?? ''
      };
      sessionStorage.setItem(SessionStorageKeys.LOGGED_USER_DETAILS, JSON.stringify(loggedUserDetails));
    } catch (err) {
      console.error('Failed to load user info', err);
    }
  }

  // async loadUserDetails() {
  //   try {
  //     const accessToken = await this.oktaAuth.tokenManager.get('accessToken');
  
  //     if (!accessToken) {
  //       console.warn('Access Token not found, skipping user info load');
  //       return;
  //     }
  
  //     const [oktaUser, permissions] = await Promise.all([
  //       //load user details from okta
  //       this.getUser(),
  //       //load dss permissions
  //       firstValueFrom(this.getDssPermissions() as Observable<any>)
  //     ]);
  //     const loggedUserDetails: LoggedUserDetails = { 
  //       id: oktaUser?.employeeNumber ?? '',
  //       name: oktaUser?.name ?? '',
  //       dssPermissions: permissions ?? [],
  //       firstName:  oktaUser?.given_name ?? '',
  //       lastName: oktaUser?.family_name ?? ''
  //     };
  //     sessionStorage.setItem(SessionStorageKeys.LOGGED_USER_DETAILS, JSON.stringify(loggedUserDetails));
  //   } catch (err) {
  //     console.error('Failed to load user info', err);
  //   }
  // }

  getDssPermissions(): Observable<DssPermission[]> {
    return this.http.get<DssPermission[]>(
      this.environmentService.getDssPermissions,
      this.environmentService.httpOptions
    );
  }

}
