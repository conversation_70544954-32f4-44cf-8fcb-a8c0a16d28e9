.dialog-content-container {
  display: flex;
  flex-direction: column;
  height: auto;              // Grow based on content
  max-height: 600px;         // Match dialog constraint
  overflow: hidden auto;
}

.dialog-header {
  flex-shrink: 0;
  text-align: center;
  font-weight: bold;
  font-size: 22px;
  padding: 18px;
  color: white;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  border-radius: 8px 8px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-wrapper {
  max-height: 80%; // Take 80% of dialog if needed
  overflow-y: auto;
  overflow-x: hidden;
  margin: 12px 0;
  padding: 0 12px 8px 12px;
}


.mat-table-custom {
  width: 100%;
  table-layout: fixed;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 12px;

  th, td {
    padding: 6px 8px;
    border-bottom: 1px solid #eee;
    text-align: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 1;
    background: linear-gradient(to right, lightgray, #eaeaea);
    font-weight: 600;
    color: #333;
  }

  tr:hover {
    background-color: #fafafa;
  }
}

.button-container {
  margin-top: auto;
  padding: 12px 0;
  text-align: right;
  align-self: center;
}

.closeButton {
  background-color: #ff6600 !important;
  color: white !important;
  border-radius: 10px;
  min-width: 90px;
  height: 36px;
  font-size: 14px;
  padding: 6px 12px;
}

::ng-deep .cdk-overlay-pane.custom-msn-dialog {
  height: auto !important;
  max-height: 600px !important;
}