// .container {
//     min-width: 100%;
//     margin: 0;
//     padding: 0;
// }

.container {
    position: relative;
    padding: 0 5px;
    width: 100%;
    margin: 0;
    max-width: none;
}

/* Make the whole dialog resizable */
.resizable-dialog .mat-mdc-dialog-container {
    resize: both; /* Allows horizontal & vertical resizing */
    overflow: auto; /* Ensures content is visible while resizing */
    min-width: 300px; /* Prevents too much shrinking */
    min-height: 200px;
    max-width: 90vw; /* Prevents exceeding viewport width */
    max-height: 90vh; /* Prevents exceeding viewport height */
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
}

.addTitle {
    flex: 0 0 100%; /* Occupies 80% of the space */
    margin: 0;
    text-align: left;
    padding: 6px 20px;
    color: white;
    font-size: 18px;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(135deg, rgb(145, 115, 115), rgb(175, 146, 146));
    background-size: 0% 100%;
    background-repeat: no-repeat;
    animation: fillBackground 2s ease-in-out forwards;
    opacity: 1;
}

.header-title {
    flex: 0 0 90%; /* Occupies 80% of the space */
    margin: 0;
    text-align: left;
    padding: 6px 20px;
    color: white;
    font-size: 18px;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(135deg, rgb(145, 115, 115), rgb(175, 146, 146));
    background-size: 0% 100%;
    background-repeat: no-repeat;
    opacity: 1;
}

/* This class is added dynamically for animation */
.header-title.fill-animation {
    animation: fillBackground 2s ease-in-out forwards;
}

/* Keyframes to animate background fill */
@keyframes fillBackground {
    0% {
        background-size: 0% 100%; /* Start empty */
    }
    100% {
        background-size: 100% 100%; /* Fully filled */
    }
}

/* Add this to both classes or just define once in shared class */
.fill-animation {
    animation: fillBackground 2s ease-in-out forwards;
  }
  

.table-container {
    position: relative;
    padding: 7px 0;
    margin: 0;
    width: 100%;
}

.ag-grid-table {
    border-radius: 10px !important;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.ag-theme-alpine {
    --ag-checkbox-checked-color: rgb(175, 146, 146) !important; /* Change checkmark color */
}

.ag-theme-alpine .ag-cell {
    white-space: normal !important;
    word-wrap: break-word;
    line-height: 1.4; /* Adjust for better spacing */
}  

// .button-container {
//     position: relative;
//     top: 2px;
//     display: flex;
//     justify-content: flex-end;
//     width: 100%;
//     gap: 5px;
//     padding: 5px 25px;
// }

.button-container {
    display: flex;
    flex: 0 0 10%; /* Occupies 20% of the space */
    justify-content: space-evenly;
    gap: 5px;
    padding: 5px;
    overflow: hidden; /* Ensures fill effect stays inside */
    // background: linear-gradient(135deg, #6f6b6d, #8f8b8d);
}

/* This class is added dynamically for animation */
// .button-container.fill-animation {
//     animation: fillBackground 2s ease-in-out forwards;
// }

.button-container button:disabled {
    box-shadow: none;
    background-color: lightgrey !important;
    cursor: not-allowed !important;
}
  
.button {
    background-color: rgb(175, 146, 146) !important;
    color: white !important;
    border-radius: 10px;
    min-width: 90px;
    height: 30px;
    font-size: 12px;
    padding: 4px 12px;
    margin-left: 8px;
}

.button:disabled {
    box-shadow: none;
    pointer-events: all !important;
    cursor: not-allowed !important;
}

.button:not(:disabled):hover{
    background-color: #de9c9c !important;
    color: white !important;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

::ng-deep .ag-header-cell-label {
    display: flex;
    justify-content: center;
    align-items: center;
}

::ng-deep .ag-ltr .ag-cell {
    display: flex;
    justify-content: center;
    align-items: center;
}

:host ::ng-deep .ag-root-wrapper.ag-layout-normal {
    border-radius: 10px;
}

:host ::ng-deep .ag-header-container, 
:host ::ng-deep .ag-floating-top-container, 
:host ::ng-deep .ag-floating-bottom-container, 
:host ::ng-deep .ag-sticky-top-container, 
:host ::ng-deep .ag-sticky-bottom-container {
    background-color: lightgray;
}

::ng-deep .ag-header.ag-header-allow-overflow .ag-header-row {
    overflow: visible;
    white-space: normal !important;
    word-wrap: break-word;
    text-align: center;
    line-height: normal;
    font-size: 10px;
}

::ng-deep .ag-row.linked-row {
  background-color: #E3DBF3 !important;
  border-left: 3px solid #3F2876 !important;
}