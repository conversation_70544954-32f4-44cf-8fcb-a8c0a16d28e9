import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DateFilterFn } from '@angular/material/datepicker';
import { DatePipe } from '@angular/common';
import { DateAdapter } from '@angular/material/core';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
  selector: 'app-intake-form',
  standalone: false,
  templateUrl: './intake-form.component.html',
  styleUrl: './intake-form.component.scss',
  providers: [DatePipe]
})
export class IntakeFormComponent implements OnInit, OnChanges {

  @Input() isAddEventSelected: boolean = false;
  @Output() addEventIntakeFormData = new EventEmitter<any>();
  @Output() detailEventIntakeFormData = new EventEmitter<any>();

  addEventIntakeForm!: FormGroup;
  detailsEventIntakeForm!: FormGroup;
  private initialDetailsEventIntakeFormValues: any = {};
  private preEditDetailsEventIntakeFormValues: any = {};
  private updatedDetailsEventIntakeFormResponse: any = null;

  intakeFormResponse = [
    {
      roleId: 1,
      eventId: 1001,
      dssAuthCode: 'METS190ADD',
      intakeFormNm: 'MEM Desk - Note - A/c Training Intake Form',
      intakeFormId: 1,
      intakeForm: {
        createdBy: 'Manish',
        questions: [
          {
            questionId: 1,
            questionTxt: 'Training A/C request',
            questionGrp: 'RADIO',
            required: false,
            answers: [{ answerId: 1, answerTxt: 'Yes' }, { answerId: 2, answerTxt: 'No' }, { answerId: 3, answerTxt: 'N/A' }]
          },
          {
            questionId: 2,
            questionTxt: 'Type if training being conducted',
            questionGrp: 'MULTIPLE',
            required: true,
            answers: [
              { answerId: 3, answerTxt: 'TOT' },
              { answerId: 4, answerTxt: 'Pilot' },
              { answerId: 5, answerTxt: 'Pushback' },
              { answerId: 6, answerTxt: 'Hub' },
              { answerId: 7, answerTxt: 'MOC' },
              { answerId: 8, answerTxt: 'De-ice' },
              { answerId: 9, answerTxt: 'Taxi' }
            ]
          },
          {
            questionId: 3,
            questionTxt: 'Who created request for Training a/c (enter name and contact information of requestor)',
            questionGrp: 'TEXT',
            required: true,
            answers: [{ answerId: 11, answerTxt: '' }]
          },
          {
            questionId: 4,
            questionTxt: 'Time frame of Training',
            questionGrp: 'DATE_RANGE',
            answers: [{ answerId: 0, answerTxt: '' }],
            required: true
          },
          {
            questionId: 5,
            questionTxt: 'Hold the Load',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 11, answerTxt: 'Yes' }, { answerId: 12, answerTxt: 'No' }, { answerId: 13, answerTxt: 'N/A' }]
          },
          {
            questionId: 6,
            questionTxt: 'Select Approximate end date of training',
            questionGrp: 'DATE',
            required: true,
            answers: [{ answerId: 14, answerTxt: '', selected: false }]
          }
        ]
      }
    },
    {
      roleId: 2,
      eventId: 2002,
      dssAuthCode: 'METS290ADD',
      intakeFormNm: 'MEM Desk - Note - Move Requests Intake Form',
      intakeFormId: 1,
      intakeForm: {
        createdBy: 'Manish',
        questions: [
          {
            questionId: 1,
            questionTxt: 'Move request',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 1, answerTxt: 'Yes' }, { answerId: 2, answerTxt: 'No' }, { answerId: 3, answerTxt: 'N/A' }]
          },
          {
            questionId: 2,
            questionTxt: 'Who created the request for move (name of requestor)',
            questionGrp: 'TEXT',
            required: true,
            answers: [{ answerId: 1, answerTxt: '' }]
          },
          {
            questionId: 3,
            questionTxt: 'Reason for Move',
            questionGrp: 'RADIO',
            required: true,
            answers: [
              { answerId: 1, answerTxt: 'PPG' },
              { answerId: 2, answerTxt: 'Ramp space' },
              { answerId: 3, answerTxt: 'MX request - hangar to gate' }
            ]
          },
          {
            questionId: 4,
            questionTxt: 'Special Requirements for move (Class III???)',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 1, answerTxt: 'Yes' }, { answerId: 2, answerTxt: 'No' }, { answerId: 3, answerTxt: 'Unknown' }]
          },
          {
            questionId: 5,
            questionTxt: 'Request time frame for move',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 1, answerTxt: 'after launch' }, { answerId: 2, answerTxt: 'before launch' }, { answerId: 3, answerTxt: 'asap' }, { answerId: 4, answerTxt: 'date and time selection' }]
          },
          {
            questionId: 6,
            questionTxt: 'Hold the Load',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 1, answerTxt: 'Yes' }, { answerId: 2, answerTxt: 'No' }, { answerId: 3, answerTxt: 'N/A' }]
          },
          {
            questionId: 7,
            questionTxt: 'Notification of tower necessary?',
            questionGrp: 'RADIO',
            required: true,
            answers: [{ answerId: 1, answerTxt: 'Yes' }, { answerId: 2, answerTxt: 'No' }, { answerId: 3, answerTxt: 'N/A' }]
          }
        ]
      }
    }
  ];

  detailsIntakeResponse = {
    eventId: 1001,
    intakeFormName: 'MEM Desk - Note - A/c Training Intake Form',
    intakeFormId: 1,
    questions: [
      {
        questionId: 1,
        questionTxt: 'Training A/C request',
        questionGrp: 'RADIO',
        required: false,
        answers: [
          { answerId: 1, answerTxt: 'Yes', selected: false },
          { answerId: 2, answerTxt: 'No', selected: false },
          { answerId: 3, answerTxt: 'N/A', selected: false }
        ],
        answered: false
      },
      {
        questionId: 2,
        questionTxt: 'Type if training being conducted',
        questionGrp: 'MULTIPLE',
        required: true,
        answers: [
          { answerId: 3, answerTxt: 'TOT', selected: false },
          { answerId: 4, answerTxt: 'Pilot', selected: false },
          { answerId: 5, answerTxt: 'Pushback', selected: false },
          { answerId: 6, answerTxt: 'Hub', selected: false },
          { answerId: 7, answerTxt: 'MOC', selected: false },
          { answerId: 8, answerTxt: 'De-ice', selected: false },
          { answerId: 9, answerTxt: 'Taxi', selected: false }
        ],
        answered: false
      },
      {
        questionId: 3,
        questionTxt: 'Who created request for Training a/c (enter name and contact information of requestor)',
        questionGrp: 'TEXT',
        required: true,
        answers: [
          { answerId: 11, answerTxt: '', selected: false }
        ],
        answered: false
      },
      {
        questionId: 4,
        questionTxt: 'Time frame of Training',
        questionGrp: 'DATE_RANGE',
        required: true,
        answers: [
          { answerId: 0, answerTxt: '', selected: false }
        ],
        answered: false
      },
      {
        questionId: 5,
        questionTxt: 'Hold the Load',
        questionGrp: 'RADIO',
        required: true,
        answers: [
          { answerId: 11, answerTxt: 'Yes', selected: false },
          { answerId: 12, answerTxt: 'No', selected: false },
          { answerId: 13, answerTxt: 'N/A', selected: false }
        ],
        answered: false
      },
      {
        questionId: 6,
        questionTxt: 'Select Approximate end date of training',
        questionGrp: 'DATE',
        required: true,
        answers: [
          { answerId: 14, answerTxt: '', selected: false }
        ],
        answered: false
      }
    ]
  };

  selectedFormIndex = 0;
  selectedForm = this.intakeFormResponse[this.selectedFormIndex];

  constructor(private fb: FormBuilder, private datePipe: DatePipe, private dateAdapter: DateAdapter<Date>) {
    this.dateAdapter.setLocale('en-US');
  }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isAddEventSelected'] && this.isAddEventSelected) {
      this.initializeAddEventIntakeForm();
    } else {
      this.initializedetailsEventIntakeForm();
    }
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    const parts = dateStr.split('/');
    if (parts.length !== 3) return null;
    const month = parseInt(parts[0], 10) - 1;
    const day = parseInt(parts[1], 10);
    const year = parseInt(parts[2], 10);
    const date = new Date(year, month, day);
    return isNaN(date.getTime()) ? null : date;
  }

  initializeAddEventIntakeForm() {
    const controls: { [key: string]: any } = {};
    
    this.selectedForm.intakeForm.questions.forEach(q => {
      if (q.questionGrp === 'MULTIPLE') {
        controls[q.questionId] = new FormControl([], q.required ? Validators.required : []);
      } else if (q.questionGrp === 'DATE_RANGE') {
        controls[q.questionId + '_from'] = new FormControl(null, q.required ? Validators.required : []);
        controls[q.questionId + '_to'] = new FormControl(null, q.required ? Validators.required : []);
      } else if (q.questionGrp === 'DATE') {
        controls[q.questionId] = new FormControl(null, q.required ? Validators.required : []);
      } else {
        controls[q.questionId] = new FormControl('', q.required ? Validators.required : []);
      }
    });

    this.addEventIntakeForm = this.fb.group(controls);
    this.trackAddEventIntakeFormChanges();
  }

  trackAddEventIntakeFormChanges() {
    this.addEventIntakeForm.valueChanges.subscribe(() => {
      if (this.addEventIntakeForm.valid) {
        this.onFormSubmit();
      } else {
        this.addEventIntakeFormData.emit({ intakeFormFulfilled: false, intakeFormResponse: null });
      }
    });
  }

  initializedetailsEventIntakeForm() {
    const controls: { [key: string]: any } = {};

    this.detailsIntakeResponse.questions.forEach(q => {
      if (q.questionGrp === 'MULTIPLE') {
        const selectedAnswers = q.answers.filter(a => a.selected).map(a => a.answerTxt);
        controls[q.questionId] = new FormControl({ value: selectedAnswers, disabled: q.answered }, q.required ? [Validators.required] : []);
      } else if (q.questionGrp === 'DATE_RANGE') {
        const dateRange = q.answers[0]?.answerTxt || '';
        const [fromDate, toDate] = dateRange ? dateRange.split('||').map(date => this.parseDate(date)) : [null, null];
        controls[q.questionId + '_from'] = new FormControl({ value: fromDate, disabled: q.answered }, q.required ? [Validators.required] : []);
        controls[q.questionId + '_to'] = new FormControl({ value: toDate, disabled: q.answered }, q.required ? [Validators.required] : []);
      } else if (q.questionGrp === 'RADIO') {
        const selectedAnswer = q.answers.find(a => a.selected)?.answerTxt || '';
        controls[q.questionId] = new FormControl({ value: selectedAnswer, disabled: q.answered }, q.required ? [Validators.required] : []);
      } else if (q.questionGrp === 'TEXT') {
        const selectedAnswer = q.answers.find(a => a.selected)?.answerTxt || '';
        controls[q.questionId] = new FormControl({ value: selectedAnswer, disabled: q.answered }, q.required ? [Validators.required] : []);
      } else if (q.questionGrp === 'DATE') {
        const selectedAnswer = q.answers.find(a => a.selected)?.answerTxt || '';
        controls[q.questionId] = new FormControl({ value: this.parseDate(selectedAnswer), disabled: q.answered }, q.required ? [Validators.required] : []);
      }
    });

    this.detailsEventIntakeForm = this.fb.group(controls);
    this.initialDetailsEventIntakeFormValues = this.formatFormValues(this.detailsEventIntakeForm.getRawValue());
    this.preEditDetailsEventIntakeFormValues = this.cloneRawFormValues(this.detailsEventIntakeForm.getRawValue());
    this.trackDetailEventIntakeFormChanges();
  }

  trackDetailEventIntakeFormChanges() {
    this.detailsEventIntakeForm?.valueChanges.subscribe(() => {
      if (this.detailsEventIntakeForm.valid) {
        const currentValues = this.formatFormValues(this.detailsEventIntakeForm.getRawValue());
        this.detailEventIntakeFormData.emit({ enableUpdateButton: false });

        this.detailsIntakeResponse.questions.forEach(q => {
          if (!q.answered) {
            const controlName = q.questionGrp === 'DATE_RANGE' 
              ? [q.questionId + '_from', q.questionId + '_to'] 
              : [q.questionId.toString()];
            
            controlName.forEach(name => {
              const initialValue = JSON.stringify(this.initialDetailsEventIntakeFormValues[name]);
              const currentValue = JSON.stringify(currentValues[name]);
              if (initialValue !== currentValue) {
                this.detailEventIntakeFormData.emit({
                  enableUpdateButton: true
                });
              }
            });
          }
        });
      }
    });
  }

  onDateChange(controlName: string, event: any) {
    const date = event.value ? new Date(event.value) : null;
    this.addEventIntakeForm.get(controlName)?.setValue(date);
  }

  onDetailsDateChange(controlName: string, event: any) {
    const date = event.value ? new Date(event.value) : null;
    this.detailsEventIntakeForm.get(controlName)?.setValue(date);
  }

  dateFormatFilter: DateFilterFn<Date | null> = (date: Date | null): boolean => {
    return date !== null;
  }

  onFormSelectionChange(index: number) {
    this.selectedFormIndex = index;
    this.selectedForm = this.intakeFormResponse[index];
    this.initializeAddEventIntakeForm();
  }

  onCheckboxChange(event: any, controlName: any) {
    const checkArray = this.addEventIntakeForm.get(controlName.toString()) as FormControl;

    if (!checkArray) {
      console.error(`Form control with name '${controlName}' not found.`);
      return;
    }

    let values = checkArray.value;
    if (!Array.isArray(values)) {
      values = [];
    }

    if (event.checked) {
      checkArray.setValue([...values, event.source.value]);
    } else {
      checkArray.setValue(values.filter((val: string) => val !== event.source.value));
    }
  }

  onDetailsCheckboxChange(event: any, controlName: any) {
    const control = this.detailsEventIntakeForm.get(controlName.toString()) as FormControl;

    if (!control) {
      console.error(`Form control with name '${controlName}' not found.`);
      return;
    }

    const current = Array.isArray(control.value) ? [...control.value] : [];

    if (event.checked) {
      if (!current.includes(event.source.value)) {
        control.setValue([...current, event.source.value]);
      }
    } else {
      control.setValue(current.filter((val: string) => val !== event.source.value));
    }
  }

  onFormSubmit() {
    if (this.isAddEventSelected && this.addEventIntakeForm.valid) {
      this.addEventIntakeFormData.emit({ intakeFormFulfilled: true, intakeFormResponse: this.generateAddEventResponse() });
    } else if (!this.isAddEventSelected && this.detailsEventIntakeForm.valid) {
      this.updateDetailsResponse();
      this.updateDetailsIntakeResponse();
      this.initializedetailsEventIntakeForm();
      this.detailEventIntakeFormData.emit({ enableUpdateButton: false });
    }
  }

  cancelFormUpdate() {
    const restoredValues = this.cloneRawFormValues(this.preEditDetailsEventIntakeFormValues);

    for (const key of Object.keys(restoredValues)) {
      if (Array.isArray(restoredValues[key])) {
        restoredValues[key] = [...restoredValues[key]];
      }
    }

    this.detailsEventIntakeForm.patchValue(restoredValues);
    this.updatedDetailsEventIntakeFormResponse = null;
    this.detailEventIntakeFormData.emit({ enableUpdateButton: false });
  }

  private generateAddEventResponse() {
    const formValues = this.formatFormValues(this.addEventIntakeForm.getRawValue());
    const selectedForm = this.intakeFormResponse[this.selectedFormIndex];

    return {
      eventId: 0,
      intakeFormName: selectedForm.intakeFormNm,
      intakeFormId: selectedForm.intakeFormId,
      questions: selectedForm.intakeForm.questions.map(q => {
        const newQuestion = {
          ...q,
          answered: this.isQuestionAnswered(q, formValues)
        };

        if (q.questionGrp === 'MULTIPLE') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            selected: formValues[q.questionId]?.includes(a.answerTxt) || false
          }));
        } else if (q.questionGrp === 'RADIO') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            selected: formValues[q.questionId] === a.answerTxt
          }));
        } else if (q.questionGrp === 'TEXT') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            answerTxt: formValues[q.questionId] || '',
            selected: !!formValues[q.questionId]
          }));
        } else if (q.questionGrp === 'DATE') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            answerTxt: formValues[q.questionId] || '',
            selected: !!formValues[q.questionId]
          }));
        } else if (q.questionGrp === 'DATE_RANGE') {
          const fromDate = formValues[`${q.questionId}_from`] || '';
          const toDate = formValues[`${q.questionId}_to`] || '';
          newQuestion.answers = [
            {
              answerId: q.answers[0]?.answerId || 0,
              answerTxt: fromDate && toDate ? `${fromDate}||${toDate}` : '',
              selected: !!(fromDate && toDate)
            }
          ];
        }

        return newQuestion;
      })
    };
  }

  private isQuestionAnswered(question: any, formValues: any): boolean {
    if (question.questionGrp === 'MULTIPLE') {
      return Array.isArray(formValues[question.questionId]) && formValues[question.questionId].length > 0;
    } else if (question.questionGrp === 'RADIO') {
      return !!formValues[question.questionId];
    } else if (question.questionGrp === 'TEXT') {
      return !!formValues[question.questionId];
    } else if (question.questionGrp === 'DATE') {
      return !!formValues[question.questionId];
    } else if (question.questionGrp === 'DATE_RANGE') {
      return !!formValues[`${question.questionId}_from`] && !!formValues[`${question.questionId}_to`];
    }
    return false;
  }

  private updateDetailsResponse() {
    const formValues = this.formatFormValues(this.detailsEventIntakeForm.getRawValue());
    this.updatedDetailsEventIntakeFormResponse = {
      ...this.detailsIntakeResponse,
      questions: this.detailsIntakeResponse.questions.map(q => {
        if (q.answered) return q;
        
        const newQuestion = { ...q };
        
        if (q.questionGrp === 'MULTIPLE') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            selected: formValues[q.questionId]?.includes(a.answerTxt) || false
          }));
        } else if (q.questionGrp === 'RADIO') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            selected: formValues[q.questionId] === a.answerTxt
          }));
        } else if (q.questionGrp === 'TEXT') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            answerTxt: formValues[q.questionId] || '',
            selected: !!formValues[q.questionId]
          }));
        } else if (q.questionGrp === 'DATE') {
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            answerTxt: formValues[q.questionId] || '',
            selected: !!formValues[q.questionId]
          }));
        } else if (q.questionGrp === 'DATE_RANGE') {
          const fromDate = formValues[`${q.questionId}_from`] || '';
          const toDate = formValues[`${q.questionId}_to`] || '';
          newQuestion.answers = (q.answers ?? []).map(a => ({
            ...a,
            answerTxt: fromDate && toDate ? `${fromDate}||${toDate}` : '',
            selected: !!(fromDate && toDate)
          }));
        }
        newQuestion.answered = this.hasUpdatedValues(newQuestion, this.initialDetailsEventIntakeFormValues);

        return newQuestion;
      })
    };
  }

  private updateDetailsIntakeResponse() {
    this.detailsIntakeResponse = {
      ...this.updatedDetailsEventIntakeFormResponse,
      questions: this.updatedDetailsEventIntakeFormResponse.questions.map((q: any) => ({
        ...q,
        answered: q.answered || this.hasUpdatedValues(q, this.initialDetailsEventIntakeFormValues)
      }))
    };
  }

  private hasUpdatedValues(question: any, initialValues: any): boolean {
    const formValues = this.formatFormValues(this.detailsEventIntakeForm.getRawValue());
    
    if (question.questionGrp === 'DATE_RANGE') {
      const fromKey = `${question.questionId}_from`;
      const toKey = `${question.questionId}_to`;
      return JSON.stringify(initialValues[fromKey]) !== JSON.stringify(formValues[fromKey]) ||
             JSON.stringify(initialValues[toKey]) !== JSON.stringify(formValues[toKey]);
    } else {
      const key = question.questionId.toString();
      return JSON.stringify(initialValues[key]) !== JSON.stringify(formValues[key]);
    }
  }

  private formatFormValues(formValue: any): any {
    const formatted = { ...formValue };
    Object.keys(formatted).forEach(key => {
      if (formatted[key] instanceof Date) {
        formatted[key] = this.datePipe.transform(formatted[key], 'MM/dd/yyyy') || '';
      }
    });
    return formatted;
  }

  private cloneRawFormValues(formValue: any): any {
    const clone: any = {};
    Object.keys(formValue).forEach(key => {
      const value = formValue[key];
      clone[key] = value instanceof Date ? new Date(value.getTime()) : Array.isArray(value) ? [...value] : value;
    });
    return clone;
  }
  
}