import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { SessionStorageKeys } from '../../events/constants/sessionStorageKeys';

@Injectable({
  providedIn: 'root'
})
export class AppLayoutService {

  constructor() { }

  // Initialize BehaviorSubject with a default value
  private sideNavClosed = new BehaviorSubject<boolean | null>(false);
  // Observable to expose the current user value
  sideNavClosedObservable$ = this.sideNavClosed.asObservable();

  private SideNavClosedEmitterFromPreferences = new Subject<boolean>();
  sideNavClosedEmitterFromPreferences$ = this.SideNavClosedEmitterFromPreferences.asObservable();

  // Update the user
  setSideNavToggleClossed(isclosed: boolean): void {
    this.sideNavClosed.next(isclosed);
  }

  setSideNavClosedFromPreferences(isSideNavClosed: boolean): void {
    this.SideNavClosedEmitterFromPreferences.next(isSideNavClosed);
  }

  setSideNavClosedInSessionStorage(isSideNavClosed: boolean): void {
    const storedValue = sessionStorage.getItem(SessionStorageKeys.SIDENAV_MENULIST_CLOSED);
    const storeValue = { [SessionStorageKeys.SIDENAV_CLOSED]: isSideNavClosed, [SessionStorageKeys.MENU_OPTIONS_CLOSED]: storedValue ? JSON.parse(storedValue).isOptionsClosed != null ? true : false : false };
    sessionStorage.setItem(SessionStorageKeys.SIDENAV_MENULIST_CLOSED, JSON.stringify(storeValue));
  }

  setListMenuOptionsClosedInSessionStorage(isOptionsClosed: boolean): void {
    const storedValue = sessionStorage.getItem(SessionStorageKeys.SIDENAV_MENULIST_CLOSED);
    const storeValue = { [SessionStorageKeys.SIDENAV_CLOSED]: storedValue ? JSON.parse(storedValue).sideNavClosed : false, [SessionStorageKeys.MENU_OPTIONS_CLOSED]: isOptionsClosed };
    sessionStorage.setItem(SessionStorageKeys.SIDENAV_MENULIST_CLOSED, JSON.stringify(storeValue));
  }

  getSideNavAndListScreenMenuClosedOptionsFromSessionStorage(): any {
    const storedValue = sessionStorage.getItem(SessionStorageKeys.SIDENAV_MENULIST_CLOSED);
    return storedValue ? JSON.parse(storedValue) : null;
  }
}
