import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EnvironmentService } from '../../app-layout/services/environment.service';

@Injectable({
  providedIn: 'root'
})
export class FlightStatusChckService {

  constructor(private http: HttpClient, private environmentService: EnvironmentService) { 

  }

  getFlightChecks(acn: string,userId: string): Observable<any> {
    const params = new HttpParams().set('acn',acn).set('userId', userId);
    return this.http.get<any>(`${this.environmentService.getFlightChecks}`, { params });
  }
}
