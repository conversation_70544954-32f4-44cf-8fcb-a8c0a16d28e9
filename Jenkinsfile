#!/usr/bin/env groovy
library 'reference-pipeline'
library 'AppServiceAccount'
library 'CICD-FOSS-V2'

pipeline {
    agent any
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
    }
    
    environment {
        // Set Node.js and Angular CLI environment variables
        NODE_HOME = '/path/to/your/nodejs'  // Update with correct path to Node.js
        NPM_HOME = '/path/to/your/npm'      // Update with correct path to NPM
        PATH = "${NODE_HOME}/bin:${NPM_HOME}/bin:${env.PATH}"
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out the repository...'
                git 'https://github.com/your-repository/angular-project.git'
            }
        }
        
        stage('Install Dependencies') {
            steps {
                script {
                    // Ensure Node and npm are installed
                    echo 'Installing dependencies...'
                    sh 'node -v'  // Verify Node.js is installed
                    sh 'npm -v'   // Verify NPM is installed

                    // Install Node.js dependencies (like Angular CLI, etc.)
                    sh 'npm install'
                }
            }
        }

        stage('Build') {
            steps {
                script {
                    // Verify Angular CLI is available
                    echo 'Building the Angular project...'
                    sh 'ng version'  // Ensure Angular CLI is installed
                    sh 'ng build --prod'  // Run Angular build command (production or default)
                }
            }
        }
        
        stage('Archive Build Artifacts') {
            steps {
                echo 'Archiving build artifacts...'
                archiveArtifacts allowEmptyArchive: true, artifacts: 'dist/**/*', followSymlinks: false
            }
        }
        
      ||  stage('Deploy (Optional)') {
    
            steps {
                script {
                    // You can add deployment steps here if needed, e.g., copy to a server
                    // For example, copy the build to a remote server
                    // sh 'scp -r dist/* user@your-server:/path/to/deploy/'
                }
            }
        }
    }

   ||  post {
        always {
            cleanWs()  // Clean workspace after build
        }
    }
}
