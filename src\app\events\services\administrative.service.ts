import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { EnvironmentService } from '../../app-layout/services/environment.service';

@Injectable({
  providedIn: 'root'
})
export class AdministrativeService {

 
  // private baseUrl = environment.baseUrl;
  constructor(private http: HttpClient, private environmentService: EnvironmentService) { }

  saveUserIntakeForm(userIntakeForm: any): Observable<Boolean>  {
    return this.http.post<any>(this.environmentService.createIntakeForm, userIntakeForm);
  }

  getAllUserIntakeForms(): Observable<any> {
    return this.http.get<any>(this.environmentService.getAllUserIntakeForms);
  }

  updateIntakeForm(modifiedIntakeForm: any): void {
    this.http.post<any>(this.environmentService.updateIntakeForm, modifiedIntakeForm).subscribe();
  }

  getRoleAndEventType(): Observable<any> {
    return this.http.get<any>(this.environmentService.getRoleAndEventType);
  }

  deleteUserIntakeForm(userIntakeFormId: number): Observable<any> {
    const params = new HttpParams()
      .set('userIntakeFormId', userIntakeFormId);
    return this.http.delete<any>(`${this.environmentService.deleteUserIntakeForm}`, { params });
  }

  deleteQuestion(questionId: number): Observable<any> {
    const params = new HttpParams().set('questionId', questionId);
    return this.http.delete<any>(`${this.environmentService.deleteQuestion}`, { params });
  }

  getAllQuestions(): Observable<any> {
    return this.http.get<any>(`${this.environmentService.getAllQuestions}`);
  }

  updateQuestion(question: any): void {
    this.http.post<any>(`${this.environmentService.updateQuestion}`, question).subscribe();
  }
}