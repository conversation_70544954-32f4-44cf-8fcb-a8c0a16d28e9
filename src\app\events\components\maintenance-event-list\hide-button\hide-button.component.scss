/* Overall Dialog Styling */
.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
  padding-bottom: 10px;
}

.dialog-content {
  display: inline-table;
  max-height: 450px;
  overflow-y: auto;
  padding: 0px 15px;
  overflow: hidden;
}

/* Options Container */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Adds spacing between items */
}


.option-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 0px 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
  transition: background 0.2s ease-in-out, transform 0.1s ease-in-out;
  height: 30px;
  cursor: grab;
  position: relative;
}

.option-item:hover {
  background: #eef1f6;
  transform: translateY(-2px);
}

.drag-handle {
  color: #666;
  font-size: 20px;
  margin-right: 10px;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Checkbox Styling */
mat-checkbox {
  font-size: 14px;
  flex-grow: 1;
}

/* Dialog Actions */
.dialog-actions {
    display: flex;
    justify-content: center;
    padding: 25px 10px;
    gap: 15px;
}

/* Buttons */
.action-button {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.cancel {
    font-size: 14px;
    max-height: 32px;
    border-radius: 10px;
    background-color: #ff6600;
    color: white !important;
    transition: 0.3s ease;
    padding: 8px 16px;
}

.save {
    min-width: 85px;
    max-height: 32px;
    border-radius: 10px;
    padding: 2px 10px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 500;
    transition: 0.3s ease-in-out;
    background-color: #6c49b9;
    color: white !important;
}

.save:hover {
  background-color: #3F2876 !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.cancel:hover {
  background-color: #cc5200 !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}



/* Prevent dragging when clicking the checkbox */
.checkbox {
  cursor: grab; /* Make sure the checkbox is clickable */
  pointer-events: auto; /* Allow checkbox clicks */
  z-index: 2; /* Ensure it stays on top */
}

.checkbox:active {
  cursor: grabbing; /* Make sure the checkbox is clickable */
  pointer-events: auto; /* Allow checkbox clicks */
  z-index: 2; /* Ensure it stays on top */
}

/* Make everything else in .option-item draggable */
.option-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: grab; /* Ensure entire row is draggable */
}

/* When actively dragging */
.option-item:active::before {
  cursor: grabbing;
}

/* Prevent pointer events on the hidden layer behind the checkbox */
.checkbox::before {
  pointer-events: none;
}

/* Make everything except checkbox draggable */
.option-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: grab; /* Keep grab cursor on entire row */
  z-index: 1; /* Ensure it doesn't cover checkbox */
}

/* Ensure it shows grabbing while dragging */
.cdk-drag-preview {
  cursor: grabbing !important;
}
