<div class="dialog-container">
  <div class="dialog-header"><span>Change Status/ETIC - </span><span style="color: #ff8c00;">ACN: {{ selectedRow?.acn }}</span></div>

  <!-- First Screen: Status and ETIC -->
  <div class="dialog-content" *ngIf="!showSecondScreen && !showThirdScreen">
    <!-- Static Data Display -->
    <div class="main-body">
      <h3 class="section-title">Current ETIC</h3>
      <div class="data-grid">
        <div class="data-item"><span class="label">Fleet:</span> <span class="value">{{ selectedRow?.fleetDesc || '-' }}</span></div>
        <div class="data-item"><span class="label">Station:</span> <span class="value">{{ selectedRow?.station || '-' }}</span></div>
        <div class="data-item"><span class="label">ETIC:</span> <span class="value">{{ selectedRow?.eticDateTime || '-' }}</span></div>
        <div class="data-item"><span class="label">Reason:</span> <span class="value">{{ selectedRow?.eticReasonCd || '-' }}</span></div>
        <div class="data-item"><span class="label">OST:</span> <span class="value">{{ selectedRow?.ost || '-' }}</span></div>
      </div>
    </div>

    <!-- Form Fields -->
    <form class="status-form" #statusForm="ngForm" (ngSubmit)="onNextOrSubmit()">
      <div class="form-container">
        <!-- New Status Section -->
        <div class="section-box new-status-section">
          <h3 class="section-title">Current Status</h3>
          <div class="data-item"><span class="value">{{ selectedRow?.status }}</span></div>
          
          <mat-divider class="custom-divider"></mat-divider>
          
          <h3 class="section-title">New Status</h3>
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Status</mat-label>
            <mat-select [(ngModel)]="newStatus" name="newStatus" required (ngModelChange)="onStatusChange()" #statusSelect="ngModel">
              <mat-option *ngFor="let option of options" [value]="option">{{ option }}</mat-option>
            </mat-select>
          </mat-form-field>
          <div class="error-message" *ngIf="statusSelect.invalid">
            Status is required
          </div>
        </div>

        <!-- New ETIC Section -->
        <div class="section-box new-etic-section">
          <h3 class="section-title">New ETIC</h3>
          <div class="error-container" *ngIf="eticErrors.length > 0">
            <ng-container *ngFor="let error of eticErrors; let last = last">
              <span class="error-chunk">
                <span>⚠</span>
                {{ error }}
              </span>
              <span *ngIf="!last" class="error-separator">|</span>
            </ng-container>
          </div>
          <div class="data-rows">
            <div class="data-row">
              <div class="data-item etic-type-field">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>ETIC Type</mat-label>
                  <mat-select [(ngModel)]="selectedEticType" name="eticType"
                    #eticTypeModel="ngModel"
                    required class="purple-text"
                    (selectionChange)="onEticTypeChange($event.value)">
                    <mat-option *ngFor="let etic of filteredEticTypeList" [value]="etic">{{etic}}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="data-item date-time date-field"
                   *ngIf="selectedEticType === 'FIRM' || selectedEticType === 'WA'">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Date</mat-label>
                  <input matInput [matDatepicker]="datePicker"
                    [(ngModel)]="currentDate" name="currentDate"
                    #currentDateModel="ngModel"
                    required class="purple-text"
                    [min]="minDate"
                    (dateChange)="validateDate($event)">
                  <mat-datepicker-toggle matIconSuffix [for]="datePicker"></mat-datepicker-toggle>
                  <mat-datepicker #datePicker></mat-datepicker>
                </mat-form-field>
              </div>
              <div class="data-item date-time time-field"
                   *ngIf="selectedEticType === 'FIRM' || selectedEticType === 'WA'">
                <div class="time-picker">
                  <mat-form-field appearance="outline" class="form-field time-field">
                    <mat-label>Hour</mat-label>
                    <mat-select [(ngModel)]="timeHour" name="timeHour"
                      #timeHourModel="ngModel"
                      required class="purple-text"
                      (selectionChange)="updateTimeOptions()">
                      <mat-option *ngFor="let hour of filteredHours" [value]="hour">{{ hour }}</mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="form-field time-field">
                    <mat-label>Minute</mat-label>
                    <mat-select [(ngModel)]="timeMinute" name="timeMinute"
                      #timeMinuteModel="ngModel"
                      required class="purple-text"
                      (selectionChange)="updateEticErrors()">
                      <mat-option *ngFor="let minute of filteredMinutes" [value]="minute">{{ minute }}</mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
              <div class="data-item info" style="display: none;">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Info</mat-label>
                    <input matInput [(ngModel)]="Info" name="Info" class="purple-text" maxlength="6" pattern=".{0,6}" (change)="updateEticErrors()">
                </mat-form-field>
              </div>
              <div class="data-item niw-reason-field"
                   *ngIf="selectedEticType && selectedEticType !== 'FIRM' && selectedEticType !== 'WA'">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Reason</mat-label>
                  <mat-select [(ngModel)]="selectedNiwReason" name="niwReason"
                    #niwReasonModel="ngModel"
                    required class="purple-text"
                    (selectionChange)="updateEticErrors()">
                    <mat-option *ngFor="let reason of filteredNiwReasonList" [value]="reason">{{ reason }}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <span class="plus-sign"
                    *ngIf="selectedEticType && selectedEticType !== 'FIRM' && selectedEticType !== 'WA'"
                    style="font-weight: bolder;font-size: larger;color: #6c49b9;">+</span>
              <div class="data-item time-needed-field"
                   *ngIf="selectedEticType && selectedEticType !== 'FIRM' && selectedEticType !== 'WA'">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Time</mat-label>
                  <input matInput type="number" class="time-needed-input purple-text"
                    [(ngModel)]="timeNeeded" name="timeNeeded"
                    min="0" step="1" required
                    (input)="onTimeNeededInput($event)"
                    (keydown)="preventNegativeInput($event)">
                </mat-form-field>
              </div>
              <div class="data-item units-field"
                   *ngIf="selectedEticType && selectedEticType !== 'FIRM' && selectedEticType !== 'WA'">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Units</mat-label>
                  <mat-select [(ngModel)]="selectedTimeUnit" name="timeUnits"
                    #timeUnitsModel="ngModel"
                    required class="purple-text"
                    (selectionChange)="updateEticErrors()">
                    <mat-option *ngFor="let unit of timeUnitList" [value]="unit">{{ unit }}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="data-item ost">
                <mat-checkbox [(ngModel)]="newOST" name="newOST" class="form-checkbox">OST</mat-checkbox>
              </div>
            </div>
            <div class="data-row reason-comment">
              <div class="data-item reason-field">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Reason</mat-label>
                  <mat-select [(ngModel)]="selectedReason" name="reason"
                    #reasonModel="ngModel"
                    [required]="requiresReason()"
                    (ngModelChange)="updateEticErrors()">
                    <mat-select-trigger>{{ selectedReason || 'Choose a reason' }}</mat-select-trigger>
                    <ng-container *ngFor="let reason of reasonData">
                      <mat-option [value]="reason.name" class="main-reason">{{ reason.name }}</mat-option>
                      <mat-option *ngFor="let subReason of reason.children" [value]="subReason.name" class="sub-reason">
                        {{ subReason.name }}
                      </mat-option>
                    </ng-container>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="data-item comment-field">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Reason Comments</mat-label>
                  <input matInput [(ngModel)]="comment" name="comment"
                    #commentModel="ngModel"
                    class="purple-text"
                    [required]="requiresComment()"
                    (ngModelChange)="onUppercaseChange('comment', $event)">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="comments-section">
        <div class="comment-wrapper">
          <h3 class="section-title">Current Comments</h3>
          <mat-form-field appearance="outline" class="form-field comment-field flex-grow">
            <textarea
              matInput
              [(ngModel)]="currentComment"
              name="currentComment"
              readonly
              class="purple-text"
              style="height: 100%; opacity: 0.6; pointer-events: none;"
              (ngModelChange)="onUppercaseChange('currentComment', $event)"
            ></textarea>
          </mat-form-field>
        </div>
        <mat-divider class="custom-divider vertical"></mat-divider>
        <div class="comment-wrapper">
          <h3 class="section-title">New Comments</h3>
          <mat-form-field appearance="outline" class="form-field comment-field flex-grow">
            <textarea
              matInput
              [(ngModel)]="newComment"
              name="newComment"
              required
              class="purple-text"
              style="height: 100%;"
              (ngModelChange)="onUppercaseChange('newComment', $event)"
              #newCommentInput="ngModel"
            ></textarea>
          </mat-form-field>
          <div class="mat-error-message" *ngIf="newCommentInput.invalid">
            New comments are required
          </div>
        </div>
      </div>

      <!-- Checkboxes -->
      <div class="checkbox-row">
        <div class="checkbox-container">
          <mat-checkbox [(ngModel)]="addTubFileNote" [disabled]="diableAddTubFileNotecheckbox" name="addTubFileNote">Add Tub File Note</mat-checkbox>
          <mat-checkbox [(ngModel)]="previousETICinError" name="previousETICinError">Previous ETIC Entered In Error</mat-checkbox>
        </div>
      </div>
    </form>
  </div>

  <!-- Second Screen: Tub File Notes -->
  <div class="dialog-content" *ngIf="showSecondScreen">
    <div class="main-body tub-file-body">
      <h3 class="section-title">Tub File Notes</h3>
      <div class="tub-file-content">
        <div class="tub-file-left">
          <h3 class="section-title">Enter New Tub File Note</h3>
          <div style="width: 100%; text-align: center; padding: 15px 0;">    
            <h3 class="discrepancy-helper-text">
              Select the discrepancy below if this tub file update is related to a specific discrepancy.  
              Otherwise, choose "General Notes" for non-discrepancy-related updates.
            </h3>    
            <mat-form-field appearance="fill" class="wide-dropdown" style="width: 90%; background-color: lavender;">
              <mat-label style="font-weight: bold;">Notes for discrepancy ( DiscTpye / ATA / Number / Discrepancy Text )</mat-label>
              <mat-select   [(ngModel)]="selectedDisc" (ngModelChange)="onDiscChange($event)">
                <mat-option value=""></mat-option>
                <mat-option *ngFor="let ld of linkedDiscrepancies" [value]="ld">
                  <div style="font-size: 12px; font-weight: bold;">{{ ld }}</div>
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <form class="status-form tubfile-note-form" style="width: 100%;" #tubFileForm="ngForm">
            <mat-form-field appearance="outline" class="form-field comment-field full-width half-height">
              <textarea
                matInput
                [(ngModel)]="tubFileNote"
                name="tubFileNote"
                required
                rows="10"
                #tubFileNoteInput="ngModel"
                (input)="onNoteInputChange()"
                class="styled-textarea">
              </textarea>

              <!-- Custom Placeholder -->
              <div class="custom-placeholder" *ngIf="!tubFileNote">
                Enter new tub file note here
              </div>
              <div class="error-message" *ngIf="tubFileNoteInput.touched && tubFileNoteInput.invalid">
                Tub file note is required
              </div>
            </mat-form-field>
          </form>
        </div>
        <mat-divider class="custom-divider vertical"></mat-divider>
        <div class="tub-file-right">
          <h3 class="section-title">Existing Tub File Notes</h3>
          <div class="notes-wrapper">
            <div class="sort-by-sticky">
              <mat-label class="sort-label">Sort by</mat-label>
              <mat-radio-group [(ngModel)]="sortOrder" (ngModelChange)="sortNotes()" class="sort-radio-group">
                <mat-radio-button value="asc" class="sort-radio">Oldest Notes First</mat-radio-button>
                <mat-radio-button value="desc" class="sort-radio">Newest Notes First</mat-radio-button>
              </mat-radio-group>
            </div>
            <div class="notes-scroll-container">
              <div class="existing-notes-container">
                <p *ngIf="!groupedNotes || groupedNotes.length === 0" class="no-notes">No existing tub file notes available.</p>
                <mat-card *ngFor="let group of groupedNotes" class="compact-note-card tf-note-card selectable">
                  <div class="note-content">
                    <div class="note-text-container">
                      <div *ngFor="let note of group.notes; let i = index" 
                          [ngClass]="{'note-heading': (i === 0 && (group.notes.length > 1 || note.tfNote?.toLowerCase().startsWith('posted new') || note.tfNote?.toLowerCase().startsWith('previous change'))), 'note-text': !(i === 0 && (group.notes.length > 1 || note.tfNote?.toLowerCase().startsWith('posted new') || note.tfNote?.toLowerCase().startsWith('previous change')))}">
                        {{ note.tfNote }}
                      </div>
                    </div>
                    <div class="note-meta-content">
                      <div class="meta-item">
                        <span class="material-icons meta-icon">person</span>
                        <span class="emp-name">{{ group.empName }}</span>
                      </div>
                      <div class="meta-item">
                        <span class="material-icons meta-icon">schedule</span>
                        <span class="update-time">{{ group.lastUpdateDtTm | date: 'short' }}</span>
                      </div>
                    </div>
                  </div>
                </mat-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Third Screen: NIW Timers -->
  <div class="dialog-content niw-timers-container" *ngIf="showThirdScreen">
      <h3 class="section-title">Start NIW Timer</h3>
      <div class="niw-timers-content">
        <form class="status-form niw-timer-form" #niwTimerForm="ngForm">
          <div class="niw-timer-row">
            <div class="data-item date-time date-field">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="niwDatePicker"
                  [(ngModel)]="niwStartDate" name="niwStartDate"
                  #niwStartDateModel="ngModel"
                  required class="purple-text"
                  (dateChange)="onNiwDateChange($event)">
                <mat-datepicker-toggle matIconSuffix [for]="niwDatePicker"></mat-datepicker-toggle>
                <mat-datepicker #niwDatePicker></mat-datepicker>
              </mat-form-field>
              <div class="error-message" *ngIf="niwStartDateModel.touched && niwStartDateModel.invalid">
                Start date is required
              </div>
            </div>
            <div class="data-item date-time time-field">
              <div class="time-picker">
                <mat-form-field appearance="outline" class="form-field time-field">
                  <mat-label>Hour</mat-label>
                  <mat-select [(ngModel)]="niwTimeHour" name="niwTimeHour"
                    #niwTimeHourModel="ngModel"
                    required class="purple-text"
                    (selectionChange)="updateNiwTimeOptions()">
                    <mat-option *ngFor="let hour of niwHours" [value]="hour">{{ hour }}</mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="form-field time-field">
                  <mat-label>Minute</mat-label>
                  <mat-select [(ngModel)]="niwTimeMinute" name="niwTimeMinute"
                    #niwTimeMinuteModel="ngModel"
                    required class="purple-text">
                    <mat-option *ngFor="let minute of niwMinutes" [value]="minute">{{ minute }}</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="niw-timers-table-container">
            <table mat-table [dataSource]="niwTimerOptions" class="mat-elevation-z2 niw-timers-table">
              <ng-container matColumnDef="timerName">
                <th mat-header-cell *matHeaderCellDef>NIW Timer Name</th>
                <td mat-cell *matCellDef="let timer" (click)="selectNiwTimer(timer)"
                  [ngClass]="{'selected-row': selectedNiwTimer === timer}">
                  {{ timer }}
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="niwTableColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: niwTableColumns;"></tr>
            </table>
            <div class="error-message" *ngIf="niwTimerForm.touched && !selectedNiwTimer">
              Please select an NIW timer
            </div>
          </div>
        </form>
      </div>
  </div>

  <div class="dialog-footer">
    <button *ngIf="addTubFileNote || newStatus === 'AOG'" mat-raised-button color="primary" class="action-button previous-button" 
      [disabled]="!showSecondScreen && !showThirdScreen" (click)="goToPrevious()">Previous</button>
    <button mat-raised-button color="primary" class="action-button cancel-button" (click)="goBack()">Cancel</button>
    <button mat-raised-button color="primary" class="action-button submit-button" 
      [disabled]="(!statusForm?.valid || !newComment || !isFormValid()) && !showSecondScreen && !showThirdScreen || (showSecondScreen && !tubFileForm?.valid) || (showThirdScreen && !niwTimerForm?.valid)"
      (click)="onNextOrSubmit()">
      {{ (addTubFileNote && !showSecondScreen && !showThirdScreen) || (newStatus === 'AOG' && showSecondScreen && !showThirdScreen) ? 'Next' : 'Submit' }}
    </button>
  </div>
</div>