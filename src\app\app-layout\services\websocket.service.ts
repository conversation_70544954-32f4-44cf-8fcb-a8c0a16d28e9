import { Injectable } from '@angular/core';
import { Client, Message } from '@stomp/stompjs';
import { OktaAuthStateService } from '@okta/okta-angular';
import { filter, switchMap, take, delay, retryWhen, tap } from 'rxjs/operators';
import { timer } from 'rxjs';
import SockJS from 'sockjs-client';
import { environment } from '../../../environments/environment';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class WebsocketService {
  private stompClient!: Client;
  private readonly maxRetries = 3;
  private readonly retryDelay = 5000;
  private baseUrl = environment.api.baseUrl;
  private notificationSubject = new Subject<any>();
  public notifications$ = this.notificationSubject.asObservable();

  constructor(private oktaAuthStateService: OktaAuthStateService) {
    this.connectWithRetry();
  }

  private connectWithRetry() {
    this.oktaAuthStateService.authState$
      .pipe(
        filter(state => state !== null && typeof state.isAuthenticated === 'boolean'),
        take(1),
        switchMap(authState => {

          if (authState?.isAuthenticated && authState.accessToken) {
            const token = authState.accessToken.accessToken;

            this.stompClient = new Client({
            webSocketFactory: () => new SockJS(this.baseUrl+'/websocket?access_token=' + token),
            reconnectDelay: 50000,
            debug: (str) => {}
          });

          this.stompClient.onConnect = () => {
            console.log('✅ WebSocket connected');

            this.stompClient.subscribe('/topic/notifications', (message: Message) => {
                const notification = {
                    body: message.body,
                    timestamp: new Date(),
                };
                this.notificationSubject.next(notification);
            });
          };

          this.stompClient.onStompError = (frame) => {
            console.error('❌ STOMP error', frame);
          };

          this.stompClient.onWebSocketError = (event) => {
            console.error('❌ WebSocket error', event);
          };

          this.stompClient.activate();


            return timer(0);
          } else {
            throw new Error('User not authenticated or no token available');
          }
        }),
        retryWhen(errors =>
          errors.pipe(
            tap(err => console.warn('Token not found, retrying...', err.message)),
            delay(this.retryDelay),
            take(this.maxRetries),
            switchMap((_, index) => {
              if (index === this.maxRetries - 1) {
                throw new Error('Max retries reached: Token not found');
              }
              return this.oktaAuthStateService.authState$;
            })
          )
        )
      )
      .subscribe({
        error: (err) => console.error('WebSocket connection error:', err),
      });
  }

  sendMessage(destination: string, body: any) {
    if (this.stompClient?.connected) {
      this.stompClient.publish({
        destination,
        body: JSON.stringify(body),
      });
    } else {
      console.warn('WebSocket not connected');
    }
  }
}