<div class="niw-timer-dialog">
  <mat-dialog-title class="dialog-title">
    <div class="title-wrapper">
      <span class="dialog-title-text">{{ data.type }} NIW Timer</span>
      <mat-icon class="close-icon" (click)="closeDialog()">cancel</mat-icon>
    </div>
  </mat-dialog-title>

  <mat-dialog-content class="dialog-content">
    <p class="timer-label">
      <span class="label">Timer Name:</span>
      <span class="timer-name">{{ timerName }}</span>
    </p>

    <div class="table-wrapper">
      <table mat-table [dataSource]="dataSource" class="mat-table-custom">

        <ng-container matColumnDef="Start">
          <th mat-header-cell *matHeaderCellDef>Start Time</th>
          <td mat-cell *matCellDef="let element">{{ element.start | uppercase }}</td>
        </ng-container>

        <ng-container matColumnDef="Stop">
          <th mat-header-cell *matHeaderCellDef>Stop Time</th>
          <td mat-cell *matCellDef="let element">{{ element.stop | uppercase }}</td>
        </ng-container>

        <ng-container matColumnDef="Duration">
          <th mat-header-cell *matHeaderCellDef>Duration</th>
          <td mat-cell *matCellDef="let element">{{ element.duration }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="selectRow(row)"
            (dblclick)="openEditDialog(row)"
            [class.selected]="selectedRow === row">
        </tr>
      </table>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="center" class="dialog-actions">
    <button mat-raised-button class="btn-purple" (click)="openAddDialog()">Add</button>
    <button mat-raised-button 
            class="btn-orange" 
            [disabled]="!selectedRow"
            [class.disabled]="!selectedRow"
            (click)="deleteTimer()">Delete</button>
  </mat-dialog-actions>
</div>