<div class="container" [class.sidenav-opened]="isSideNavClosed">
  <div class="options-container" [class.hidden]="!isCardVisible">
    <!-- Mat-Card (Dropdown) -->
    <mat-card class="dropdown-card">
      <mat-card-content class="mat-card-content">
        <div class="dropdown-container">

          <!-- <mat-form-field class="select-field">
            <mat-label>Events</mat-label>
            <mat-select [formControl]="eventsControl" (selectionChange)="filterTableData()" [(value)]="selectedEvents" multiple>
              <mat-option *ngFor="let event of events" [value]="event">{{ event }}</mat-option>
            </mat-select>
          </mat-form-field> -->
          
          <!-- <mat-form-field class="select-field">
            <mat-label>Fleets</mat-label>
            <mat-select [formControl]="fleetControl" (selectionChange)="filterTableData()" [(value)]="selectedFleets" multiple>
              <mat-option *ngFor="let fleet of fleets" [value]="fleet">{{ fleet }}</mat-option>
            </mat-select>
          </mat-form-field> -->
                    
          <mat-form-field class="select-field">
            <mat-label class="menuLabel">Events</mat-label>
            <mat-select [formControl]="eventsControl" (selectionChange)="onEventsSelectionChange()" [value]="selectedEvents" [(ngModel)]="selectedEvents" multiple>
              
              <!-- Custom 'Select All' option -->
              <div class="custom-mat-option" (click)="toggleSelectAll($event)" [class.disabled]="events.length === 0">
                <mat-pseudo-checkbox
                  class="mat-mdc-option-pseudo-checkbox"
                  [state]="isAllSelected() ? 'checked' : isSomeSelected() ? 'indeterminate' : 'unchecked'"
                ></mat-pseudo-checkbox>
                <span class="option-label">Select All</span>
              </div>

              <!-- Regular options -->
              <mat-option *ngFor="let event of events" [value]="event">
                {{ event }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="select-field">
            <mat-label class="menuLabel">Fleets</mat-label>
            <mat-select [formControl]="fleetControl" (selectionChange)="onFleetsSelectionChange()" [(value)]="selectedFleets" [(ngModel)]="selectedFleets" multiple>
              
              <!-- Custom Select All for Fleets -->
              <div class="custom-mat-option" (click)="toggleFleetSelectAll($event)" [class.disabled]="fleets.length === 0">
                <mat-pseudo-checkbox
                  class="mat-mdc-option-pseudo-checkbox"
                  [state]="isFleetAllSelected() ? 'checked' : isFleetSomeSelected() ? 'indeterminate' : 'unchecked'"
                ></mat-pseudo-checkbox>
                <span class="option-label">Select All</span>
              </div>

              <!-- Regular fleet options -->
              <mat-option *ngFor="let fleet of fleets" [value]="fleet">
                {{ fleet }}
              </mat-option>
            </mat-select>
          </mat-form-field>
  
          <mat-form-field class="select-field">
            <mat-label class="menuLabel">Regions</mat-label>
            <mat-select [formControl]="regionControl" [(value)]="selectedRegion" (selectionChange)="getStationsListFromRegion(false)">
              <mat-option [value]="null">-- None --</mat-option>
              <mat-option *ngFor="let region of regions" [value]="region">{{ region }}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="select-field">
            <mat-label class="menuLabel">Stations</mat-label>
            <mat-select [formControl]="stationControl" [(value)]="selectedstation" (selectionChange)="filterTableData()">
              <mat-option [value]="null">-- None --</mat-option>
              <mat-option *ngFor="let station of stations" [value]="station">{{ station }}</mat-option>
            </mat-select>
          </mat-form-field>
  
          <mat-checkbox [(ngModel)]="powerPlantChecked" (change)="filterTableData()" class="checkbox">PowerPlant</mat-checkbox>
  
          <div class="button-group">
            <button mat-raised-button class="displayButton" (click)="displayMaintenanceEventList()">Display</button>
            <button mat-raised-button class="resetButton" (click)="reset()">Reset</button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Toggle Button -->
    <button mat-fab color="primary" [ngClass]="{'cardClosedButtonState': !isCardVisible}" class="toggle-btn" (click)="toggleCard(null, true)">
      <mat-icon>{{ isCardVisible ? 'expand_less' : 'expand_more' }}</mat-icon>
    </button>
  </div>   

  <!-- Event Summary Section (Shifts Up/Down) -->
  <!-- <div class="events-summary" [class.shift-up]="!isCardVisible">
    <div class="events-count">
      <span>Number of events:</span>
      <span class="count">{{ filteredEventListTableData.length + pinnedTopRowData.length }}</span>
    </div>
    <div class="actions-buttons">
      <button mat-raised-button class="addEventButton" (click) = loadAddEvent()>Add Event</button>
      <button mat-raised-button [matMenuTriggerFor]="actionsMenu" 
              [disabled]="!isRowSelected" [ngClass]="{'diableButton': !isRowSelected, 'actionsButton': isRowSelected}">
        Actions
      </button>

      <mat-menu class="actionMenuCard" #actionsMenu="matMenu">
        <button mat-menu-item [disabled]="isChangeStatusEticDisabled" (click)="onChangeStatus()">Change Status/ETIC</button>
        <button mat-menu-item [disabled]="isConvertEventDisabled" (click)="onConvertEvent()">Convert Event</button>
        <button mat-menu-item [disabled]="isCloseEventDisabled" (click)="onCloseEvent()">Close Event - A/C UP</button>
        <button mat-menu-item [disabled]="isCancelEventDisabled" (click)="onCancelEvent()">Cancel Event</button>
        <button mat-menu-item [disabled]="isReviewAlertDiabled" (click)="onReviewAlert()">Review Alert</button>
      </mat-menu>

      <button mat-raised-button [disabled]="!isRowSelected" [ngClass]="{'diableButton': !isRowSelected, 'detailsButton': isRowSelected}"
              (click)="openMaintenanceEventDetails()">
        Details
      </button>
    </div>
  </div> -->

  <div class="events-summary" [class.shift-up]="!isCardVisible">
    <div class="events-count-container">
      <div class="events-count">
        <span class="label">Number of events:</span>
        <span class="count">{{ filteredEventListTableData.length + pinnedTopRowData.length }}</span>
      </div>

      <!-- Separator -->
      <div class="vertical-separator"></div>

      <!-- Add ACN input and button -->
      <div class="add-acn">
        <input type="text" #acnInputRef placeholder="ACNs (comma-separated)" [(ngModel)]="acnInput" (keydown.enter)="onAddAcn(acnInput.trim()); acnInputRef.blur()" (keypress)="allowOnlyNumbers($event)"/>
        <button mat-raised-button [disabled]="acnInput.length === 0" (click)="onAddAcn(acnInput.trim())">Add ACN</button>
      </div>
      <!-- <div class="add-acn">
        <button mat-raised-button (click)="openAddAcnDialog()">Add ACN</button>
      </div> -->
    </div>

    <div class="actions-buttons">
      <button mat-raised-button class="addEventButton" (click)="loadAddEvent()">Add Event</button>
      <button mat-raised-button [matMenuTriggerFor]="actionsMenu"
              [disabled]="!isRowSelected" [ngClass]="{'diableButton': !isRowSelected, 'actionsButton': isRowSelected}">
        Actions
      </button>

      <mat-menu class="actionMenuCard" #actionsMenu="matMenu">
        <button mat-menu-item [disabled]="isChangeStatusEticDisabled" (click)="onChangeStatus()">Change Status/ETIC</button>
        <button mat-menu-item [disabled]="isConvertEventDisabled" (click)="onConvertEvent()">Convert Event</button>
        <button mat-menu-item [disabled]="isCloseEventDisabled" (click)="onCloseEvent()">Close Event - A/C UP</button>
        <button mat-menu-item [disabled]="isCancelEventDisabled" (click)="onCancelEvent()">Cancel Event</button>
        <button mat-menu-item [disabled]="isReviewAlertDiabled" (click)="onReviewAlert()">Review Alert</button>
      </mat-menu>

      <button mat-raised-button [disabled]="!isRowSelected" [ngClass]="{'diableButton': !isRowSelected, 'detailsButton': isRowSelected}"
              (click)="openMaintenanceEventDetails()">
        Details
      </button>
    </div>
  </div>

  <!-- AG Grid Table -->
  <div class="table-container" [class.shift-up-table]="!isCardVisible" [style.height.px]="gridHeight" (contextmenu)="disableRightClick($event)">
    <ag-grid-angular
      #agGrid
      [style.height.px]="gridHeight"
      id="agGridTable"
      class="ag-theme-alpine ag-grid-table"
      [rowData]="filteredEventListTableData"
      [suppressValidationStyling]="true"
      [columnDefs]="columnDefs"
      [gridOptions]="gridOptions"
      [domLayout]="'normal'"
      rowSelection="single"
      (selectionChanged)="onRowSelected($event)"
      (rowDoubleClicked)="onRowDoubleClick($event)"
      (rowClicked)="onRowClicked($event)"
      (gridReady)="onGridReady($event)"
      [rowDragManaged]="true"
      (columnMoved)="onColumnReorder($event)"
      [modules]="modules">
    </ag-grid-angular>
  </div>

  <!-- Custom Context Menu -->
  <div *ngIf="isContextMenuVisible" class="custom-context-menu" [style.top.px]="contextMenuPosition.y" [style.left.px]="contextMenuPosition.x">
    <button mat-menu-item [disabled]="isChangeStatusEticDisabled" (click)="onChangeStatus()">Change Status/ETIC</button>
    <button mat-menu-item [disabled]="isConvertEventDisabled" (click)="onConvertEvent()">Convert Event</button>
    <button mat-menu-item [disabled]="isCloseEventDisabled" (click)="onCloseEvent()">Close Event - A/C UP</button>
    <button mat-menu-item [disabled]="isCancelEventDisabled" (click)="onCancelEvent()">Cancel Event</button>
    <button mat-menu-item [disabled]="isReviewAlertDiabled" (click)="onReviewAlert()">Review Alert</button>
  </div>
</div>