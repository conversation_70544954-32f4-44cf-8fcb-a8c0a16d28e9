.container {
    max-width: 99%;
    background: white;
    margin: 5px 0.5%;
    padding: 5px 20px;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}  

.row {
    display: grid;
    gap: 10px;
    padding: 5px;
}

.first-row {
    // grid-template-columns: repeat(5, 1fr) 0.5fr;
    grid-template-columns: 12% repeat(4, 1fr) 7%;
}

.second-row {
    // grid-template-columns: 15% repeat(4, 1fr) 6%;
    grid-template-columns: 12% repeat(4, 1fr) 7%;
}

.third-row {
    grid-template-columns: repeat(3, 1fr);
}

.label-value {
    display: flex;
    align-items: center;
    justify-content: center;
    background: whitesmoke;
    padding: 8px;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.label {
    font-size: 13px;
    font-weight: bold;
    margin-right: 10px;
    opacity: 0.8;
}

.value {
    color: rgb(175, 146, 146);
    font-size: 14px;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
}

.ost-field {
    // min-width: 60px;
    // max-width: 90px;
}

.label-value:hover {
    cursor: default !important;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

/* Tooltip for long text */
.label-value.expandable:hover::after {
    content: attr(data-fulltext);
    position: absolute;
    background: white;
    border: 1px solid #ccc;
    padding: 5px;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: block;
    z-index: 999;
}

/* Popover Styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease-in-out;
  }