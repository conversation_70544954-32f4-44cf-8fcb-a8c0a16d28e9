// Interface for each aircraft event object
export interface MaintenanceEventListResponseDao {
  eventID: number;
  type: string;
  startDateTime: string;
  endDateTime: string | null;
  acn: string;
  fleetDesc: string;
  station: string | null;
  status: string;
  eticDateTime: string | null;
  eticText: string | null;
  origComment: string | null;
  curComment: string | null;
  lastUpdateDateTime: string;
  lastUpdatedBy: string;
  createdDateTime: string;
  createdBy: string;
  eventOnwerGroupId: string;
  changeRequestLastUpdateDtTime: string;
  requestStatus: string;
  newStatus: string;
  newEticText: string | null;
  newEticComment: string | null;
  newEticDateTime: string | null;
  changeType: string;
  lastUpdated: string;
  errorText: string | null;
  currentDuration: string | null;
  gate: string | null;
  acOwnerGroupId: string;
  owner: string;
  managerNote: string | null;
  isAddEventMessage: boolean;
  flightDepartureDetails: string | null;
  flightDetails: string | null;
  durationData: string;
  doaAlert: boolean;
  isBold: boolean;
  isEventActive: boolean;
  isPowerPlantEvent: boolean;
  changeTypeForClient: string | null;
  doaFlightNumber: string | null;
  doaFlightDate: string | null;
  doaFlightLegNumber: string | null;
  _isTubFileAlert: boolean;
  resMgrId: string | null;
  memDeskContact: string | null;
  ost: string | null;
  newOST: string | null;
  eticReasonCd: string | null;
  newEticReasonCd: string | null;
  eticRsnComments: string | null;
  newEticRsnComments: string | null;
  eticRsnComment: string | null;
  newEticRsnComment: string | null;
  doaflightLegNumber: string | null;
  doaflightNumber: string | null;
  doaflightDate: string | null;
  doaalert: boolean;
  acownerGroupId: string;
  eticData: string | null;

  // Computed or extra fields if needed:
  iseticDateTimeInNext30Min?: boolean;
  iseticDateTimeInPast?: boolean;
  isUserAddedAcn?: boolean;
}