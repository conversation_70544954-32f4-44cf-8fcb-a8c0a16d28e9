<div class="dialog-container">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <span>Close Event - </span>
    <span style="color: #ff8c00;">&nbsp;ACN: {{ selectedEvent?.acn }}</span>
    <button mat-icon-button (click)="onCancel()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Dialog Content with Stepper -->
  <div class="dialog-content">
    <div class="stepper-container">
      <mat-horizontal-stepper #stepper [selectedIndex]="activeStep" (selectionChange)="onStepSelectionChange($event)">
        <!-- Step 1: End Date/Time -->
        <mat-step label="End Date/Time">
          <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[0] }"></ng-container>
          <ng-template #badge let-valid="valid">
            <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
          </ng-template>
        </mat-step>

        <!-- Step 2: Discrepancies -->
        <mat-step label="Discrepancies" *ngIf="closeEventForm.get('linkDiscrepancy')?.value">
          <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[1] }"></ng-container>
          <ng-template #badge let-valid="valid">
            <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
          </ng-template>
        </mat-step>

        <!-- Step 3: Tub File Notes -->
        <mat-step label="Tub File Notes" *ngIf="closeEventForm.get('addTubFileNote')?.value">
          <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[2] }"></ng-container>
          <ng-template #badge let-valid="valid">
            <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
          </ng-template>
        </mat-step>

        <!-- Step 4: Reporting Categories -->
        <mat-step label="Reporting Categories">
          <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[3] }"></ng-container>
          <ng-template #badge let-valid="valid">
            <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
          </ng-template>
        </mat-step>

        <!-- Step 5: Stop NIW Timers -->
        <mat-step label="Stop NIW Timers" *ngIf="showStopNiwTimersSection">
          <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[4] }"></ng-container>
          <ng-template #badge let-valid="valid">
            <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
          </ng-template>
        </mat-step>
      </mat-horizontal-stepper>

      <!-- Scrollable Content Container -->
      <div class="content-container" #scrollContainer (scroll)="onScroll()">
        <form [formGroup]="closeEventForm" class="form-container">

          <!-- Step 1: End Date/Time -->
          <div #sectionRef class="step-section">
            <p class="step-title">Step 1: End Date/Time</p>
            <div class="form-card">
              <div class="step-header">
                <h3>Event Details & End Date/Time</h3>
                <p>Review event information and specify the end date and time for this event</p>
              </div>

              <!-- Event Information Section -->
              <div class="form-section">
                <h4 class="section-subtitle">Event Information</h4>
                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>ACN</mat-label>
                    <input matInput [value]="selectedEvent?.acn || 'N/A'" readonly class="purple-text">
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>Type</mat-label>
                    <input matInput [value]="selectedEvent?.type || 'N/A'" readonly class="purple-text">
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>Station</mat-label>
                    <input matInput [value]="selectedEvent?.station || 'N/A'" readonly class="purple-text">
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>Status</mat-label>
                    <input matInput [value]="selectedEvent?.status || 'N/A'" readonly class="purple-text">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field full-width">
                    <mat-label>Start Date/Time</mat-label>
                    <input matInput [value]="selectedEvent?.startDateTime || 'N/A'" readonly class="purple-text">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field full-width">
                    <mat-label>Comment</mat-label>
                    <textarea matInput [value]="selectedEvent?.curComment || selectedEvent?.origComment || 'No comment available'" readonly rows="2" class="purple-text"></textarea>
                  </mat-form-field>
                </div>
              </div>

              <!-- End Date/Time Section -->
              <div class="form-section">
                <h4 class="section-subtitle">End Date & Time</h4>
                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>End Date</mat-label>
                    <input matInput [matDatepicker]="endDatePicker" formControlName="endDate" class="purple-text">
                    <mat-datepicker-toggle matIconSuffix [for]="endDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #endDatePicker></mat-datepicker>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>End Time</mat-label>
                    <input matInput type="time" formControlName="endTime" class="purple-text">
                  </mat-form-field>
                </div>

                <!-- Checkboxes Row -->
                <div class="form-row checkbox-row">
                  <mat-checkbox formControlName="addTubFileNote" class="form-checkbox">
                    Add Tub File Note
                  </mat-checkbox>

                  <mat-checkbox formControlName="linkDiscrepancy" class="form-checkbox">
                    Link Discrepancy
                  </mat-checkbox>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Discrepancies -->
          <div #sectionRef class="step-section" *ngIf="closeEventForm.get('linkDiscrepancy')?.value">
            <p class="step-title">Step 2: Discrepancies</p>
            <div class="form-card">
              <div class="step-header">
                <h3>Discrepancies</h3>
                <p>Select and manage discrepancies related to this event</p>
              </div>

              <div class="form-section">
                <app-discrepancies [isAddEventSelected]="false" [detailsViewObj]="detailViewData" (detailsDiscrepanciesData)="updateDiscrepanciesButtons($event)"></app-discrepancies>
              </div>
              <div class="discrepancies-button-container">
                <button [disabled]="!isDiscrepanciesUpdateEnabled"
                        mat-raised-button
                        class="discrepancies-button"
                        (click)="updateDiscrepancies()">
                  <mat-icon>update</mat-icon>Update
                </button>
                <button [disabled]="!isDiscrepanciesViewEnabled"
                        mat-raised-button
                        class="discrepancies-button"
                        (click)="viewDiscrepancies()">
                  <mat-icon>visibility</mat-icon>View
                </button>
              </div>
            </div>
          </div>

          <!-- Step 3: Tub File Notes -->
          <div #sectionRef class="step-section" *ngIf="closeEventForm.get('addTubFileNote')?.value">
            <p class="step-title">Step 3: Tub File Notes</p>
            <div class="form-card">
              <div class="step-header">
                <h3>Tub File Notes</h3>
                <p>Add notes for the tub file documentation</p>
              </div>

              <div class="form-section">
                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field full-width">
                    <mat-label>Tub File Notes</mat-label>
                    <textarea matInput rows="6" formControlName="tubFileNotes" placeholder="Enter tub file notes..." class="purple-text"></textarea>
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 4: Reporting Categories -->
          <div #sectionRef class="step-section">
            <p class="step-title">Step 4: Reporting Categories</p>
            <div class="form-card">
              <div class="step-header">
                <h3>Reporting Categories</h3>
                <p>Select applicable reporting categories</p>
              </div>

              <div class="form-section">
                <app-reporting-categories
                  [detailsViewObj]="detailViewData" (formModelChange)="onFormModelUpdate($event)" (forwardRepCategories)="onRecdRepCats($event)">
                </app-reporting-categories>
              </div>
            </div>
          </div>

          <!-- Step 5: Stop NIW Timers -->
          <div #sectionRef class="step-section" *ngIf="showStopNiwTimersSection">
            <p class="step-title">Step 5: Stop NIW Timers</p>
            <div class="form-card">
              <div class="step-header">
                <h3>Stop NIW Timers</h3>
                <p>Stop all Not In Work timers for this event</p>
              </div>

              <div class="form-section">
                <!-- Active NIW Timers Display -->
                <div class="form-row" *ngIf="hasActiveNiwTimers">
                  <div class="active-timers-info">
                    <h4 class="section-subtitle">Active NIW Timers</h4>
                    <div class="timer-list">
                      <div *ngFor="let timer of activeNiwTimers; let i = index" class="timer-item">
                        <mat-form-field appearance="outline" class="form-field">
                          <mat-label>Timer Name</mat-label>
                          <input matInput [value]="timer.timerName || 'Unknown Timer'" readonly class="purple-text">
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="form-field">
                          <mat-label>Start Date</mat-label>
                          <input matInput [value]="getFormattedStartDate(timer.timerStartDtTm)" readonly class="purple-text">
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="form-field">
                          <mat-label>Start Time</mat-label>
                          <input matInput [value]="getFormattedStartTime(timer.timerStartDtTm)" readonly class="purple-text">
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="form-field">
                          <mat-label>End Date</mat-label>
                          <input matInput [matDatepicker]="niwEndDatePicker" formControlName="niwEndDate" class="purple-text">
                          <mat-datepicker-toggle matIconSuffix [for]="niwEndDatePicker"></mat-datepicker-toggle>
                          <mat-datepicker #niwEndDatePicker></mat-datepicker>
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="form-field">
                          <mat-label>End Time</mat-label>
                          <input matInput type="time" formControlName="niwEndTime" class="purple-text">
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions">
    <button mat-button (click)="onCancel()" class="cancel-button">
      Cancel
    </button>
    <button mat-raised-button color="primary" (click)="onSave()"
            [disabled]="!isCloseEventButtonEnabled" class="save-button">
      Close Event
    </button>
  </div>
</div>
