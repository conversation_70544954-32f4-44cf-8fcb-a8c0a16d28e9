import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

interface DialogData {
  isSuccess: boolean;
  message: string;
}

@Component({
  selector: 'app-success-error-dialog',
  standalone: false,
  templateUrl: './success-error-dialog.component.html',
  styleUrl: './success-error-dialog.component.scss'
})
export class SuccessErrorDialogComponent {

  isSuccess: boolean;
  message: string;
  formattedMessage: SafeHtml;

  constructor(
    public dialogRef: MatDialogRef<SuccessErrorDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private sanitizer: DomSanitizer
  ) {
    this.isSuccess = data.isSuccess;
    this.message = data.message;
    // Convert \n to <br> for line breaks
    this.formattedMessage = this.sanitizer.bypassSecurityTrustHtml(
      this.message.replace(/\n/g, '<br>')
    );
  }

  ngOnInit() {
    if (this.isSuccess) {
      // Auto-close after 3 seconds for success
      setTimeout(() => {
        this.dialogRef.close();
      }, 3000);
    }
  }

  onClose(): void {
    this.dialogRef.close();
  }

}
