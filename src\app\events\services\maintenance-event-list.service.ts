import { HttpClient, HttpParams} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { RetrievalDto } from '../dto/retrievalDto';
import { TubFilesNotesRequestDto } from '../dto/TubFileNotesRequestDto';
import { MaintenanceEventListResponseDao } from '../dao/maintenence-event-listDao';
import { TubFileNotesStatusUpdateDto } from '../dto/TubFileNotesStatusUpdate';
import { RequestDAODto } from '../dto/requestDOAForm';
import { updateDOAFormDto } from '../dto/updateDOAFormRequest';
import { EnvironmentService } from '../../app-layout/services/environment.service';
import { ScreenPreference } from '../components/maintenance-event-list/maintenance-event-list-interfaces';
import { SelectedValues, SessionStorageKeys } from '../constants/sessionStorageKeys';
import { ChangeStatusRequestDTO } from '../dto/changeStatusRequestDto';
import { UserAddedAcn } from '../constants/userAddedAcn';

@Injectable({
  providedIn: 'root'
})
export class MaintenanceEventListService {

  private maintenanceEventListResponse = new BehaviorSubject<MaintenanceEventListResponseDao[]>({} as MaintenanceEventListResponseDao[]);
  maintenanceEventListResponse$ = this.maintenanceEventListResponse.asObservable();

  private selectedEventListOptionsUpdated: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  selectedEventListOptionsUpdated$ = this.selectedEventListOptionsUpdated.asObservable();

  private stationsList = new BehaviorSubject<any[]>([]);
  stationsList$ = this.stationsList.asObservable();

  private removeAcnFromEventListTable = new BehaviorSubject<string>("");
  removeAcnFromEventListTable$ = this.removeAcnFromEventListTable.asObservable();

  constructor(private http: HttpClient, private environmentService: EnvironmentService) { }

  getEventListData() {
    return this.maintenanceEventListResponse.getValue();
  }

  updateListDataToMainScreen(data: MaintenanceEventListResponseDao[]) {
    this.maintenanceEventListResponse.next(data);
  }

  refreshEventListTableData(acn: string) {
    this.removeAcnFromEventListTable.next(acn);
  }

  updatedSelectedEventListOptions(updated: boolean) {
    this.selectedEventListOptionsUpdated.next(updated);
  }

  updateStationsList(stations: any[]) {
    this.stationsList.next(stations);
  }

  /** Get Event Values from Json File */
  getEventValues() : Observable<any> {
    return this.http.get<any>(this.environmentService.getEventJson);
  }

  /** Get Fleet Values from Json File */
  getFeeltValues() : Observable<any> {
    return this.http.get<any>(this.environmentService.getAcnCache);
  }

  getMaintenanceEventListTableHeaders() : Observable<any> {
    return this.http.get<any>(this.environmentService.getMaintenanceEventListTableHeaders);
  }

  saveUserPreferences(screenPreference: ScreenPreference): Observable<any> {
    const userDetails = sessionStorage.getItem(SessionStorageKeys.LOGGED_USER_DETAILS);
    if (userDetails != null) {
      const parsedUserDetails = JSON.parse(userDetails);
      let params = new HttpParams().set('empNbr', parsedUserDetails.id).set('empName', parsedUserDetails.name.trim());
      return this.http.post<ScreenPreference>(this.environmentService.saveUserPreferences, screenPreference, { params });
      } else {
      throw new Error('User details not found in session storage');
    }
  }

  getMaintenanceEventList(): Observable<any> {
    return this.http.get<any>(this.environmentService.getMaintenanceEventList);
  }

  getRegionsList(): Observable<any> {
    return this.http.get<any>(this.environmentService.getRegionsList);
  }

  getStationsListFromRegion(selectedRegion: any): Observable<any> {
    const url = `${this.environmentService.getStationsListFromRegion}${selectedRegion}`;
    return this.http.get<any>(url);
  }

  getDOAformDetails(acn: number) : Observable<any>{
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    const url = `${this.environmentService.getDoaFormDetails}${acn}`;
    return this.http.get<any>(url, { params });
  }

  updateDOAformDetails(doaFormUpdateDto: updateDOAFormDto) : Observable<any>{
    return this.http.post<updateDOAFormDto>(this.environmentService.updateDoaFormDetails, doaFormUpdateDto);
  }

  closeEvent(closeEventData: any) : Observable<any>{
    return this.http.post<any>(this.environmentService.closeEvent, closeEventData);
  }

  changeStatusOrEticRequest(changeStatusRequestDTO: ChangeStatusRequestDTO): Observable<any> {
    return this.http.post<ChangeStatusRequestDTO>(this.environmentService.getChangeStatusEtic, changeStatusRequestDTO);
  }

  /** Save Selected Values */
  saveSelectedValuesInStorage(selectedEvents: any[], selectedFleets: any[], selectedRegion: any, selectedStation: any, powerPlantchecked: boolean): void {
    this.setItem(SessionStorageKeys.SELECTED_EVENTS, selectedEvents);
    this.setItem(SessionStorageKeys.SELECTED_FLEETS, selectedFleets);
    this.setItem(SessionStorageKeys.SELECTED_REGIONS, selectedRegion === null ? "" : selectedRegion);
    this.setItem(SessionStorageKeys.SELECTED_STATIONS, selectedStation === null ? "" : selectedStation);
    this.setItem(SessionStorageKeys.POWER_PLANT_CHECKED, powerPlantchecked);
    this.selectedEventListOptionsUpdated.next(true);
  }

  /** Retrieve Selected Values */
  getSelectedValuesFromStorage(): SelectedValues {
    return {
      selectedEvents: this.getItem(SessionStorageKeys.SELECTED_EVENTS) || [],
      selectedFleets: this.getItem(SessionStorageKeys.SELECTED_FLEETS) || [],
      selectedRegion: this.getItem(SessionStorageKeys.SELECTED_REGIONS) || "",
      selectedStation: this.getItem(SessionStorageKeys.SELECTED_STATIONS) || "",
      powerPlantChecked: this.getItem(SessionStorageKeys.POWER_PLANT_CHECKED) || false
    };
  }

  /** Save Event List Table Columns Headers */
  saveEventListTableColumnsInStorage(columnDefs: any[]): void {
    this.setItem(SessionStorageKeys.EVENT_LIST_COLUMNS, columnDefs);
  }

  /** Retrieve Event List Table Columns Headers */
  getEventListTableColumnsFromStorage(): any[] | null {
    return this.getItem(SessionStorageKeys.EVENT_LIST_COLUMNS);
  }

  /** Save Maintenance List Customization Settings */
  saveMaintenanceListSettingsOptionsInStorage(hideColumns: any): void {
    this.setItem(SessionStorageKeys.EVENT_LIST_SETTINGS, hideColumns);
  }

  /** Retrieve Maintenance List Customization Settings */
  getMaintenanceListSettingsOptionsFromStorage(): Observable<any> | null {
    return this.getItem(SessionStorageKeys.EVENT_LIST_SETTINGS);
  }

  getEmployeeIdFromStorage() {
    const userDetails = this.getItem<{ id: string }>(SessionStorageKeys.LOGGED_USER_DETAILS);
    if (userDetails && userDetails?.id) {
      return parseInt(userDetails.id);
    } else {
      throw new Error('User details not found in session storage or missing id');
    }
  }

  setUserAddedEventListAcnDatainStorage(useraddedAcn: { acn: any; eventId: any; eventType: any }): void {
    const existingList = this.getItem<any[]>(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN) || [];

    // Create the object you want to add
    const addedEventList = { acn: useraddedAcn.acn, eventId: useraddedAcn.eventId, eventType: useraddedAcn.eventType };

    // Check if this combination already exists in the list
    const isAlreadyPresent = existingList.some(
      (item) => item.acn === addedEventList.acn && item.eventId === addedEventList.eventId && item.eventType === addedEventList.eventType
    );

    if (!isAlreadyPresent) {
      const updatedList = [...existingList, addedEventList];
      this.setItem(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN, updatedList);
    }
  }

  getUserAddedEventListFromStorage(): UserAddedAcn[] {
    const userAddedEventList = this.getItem<any[]>(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN) || [];
    // If items are stored as strings, parse each; otherwise, return as is
    if (userAddedEventList.length > 0 && typeof userAddedEventList[0] === 'string') {
      return userAddedEventList.map(item => JSON.parse(item)) as UserAddedAcn[];
    }
    return userAddedEventList as UserAddedAcn[];
  }

  removeUserAddedEventListFromStorage(data: UserAddedAcn): void {
    const existingUserAddedList = this.getUserAddedEventListFromStorage();
    const foundUserAdddedAcnInExistingList = existingUserAddedList.find(row => row.acn.trim() === data.acn.trim() && row.eventType.trim() === data.eventType.trim() && data.eventId.trim() === row.eventId.trim());
    if (foundUserAdddedAcnInExistingList) {
      existingUserAddedList.splice(existingUserAddedList.indexOf(foundUserAdddedAcnInExistingList), 1);
    }
    this.setItem(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN, existingUserAddedList);

    const acnTabData = this.getItem<any[]>(SessionStorageKeys.ACN_DETAILS_TAB_DATA) || [];
    const foundTab = acnTabData.find(tab => tab.name.toLowerCase() === `acn - ${data.acn.toLowerCase()}`);
    if (foundTab) {
      acnTabData.splice(acnTabData.indexOf(foundTab), 1);
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, acnTabData);
    }
  }

  /** Generic method to set sessionStorage items */
  private setItem(key: string, value: any): void {
    if (value !== null && value !== undefined) {
      sessionStorage.setItem(key, JSON.stringify(value));
    }
  }

  /** Generic method to get sessionStorage items */
  private getItem<T>(key: string): T | null {
    const storedValue = sessionStorage.getItem(key);
    if (!storedValue) return null;
    try {
      return JSON.parse(storedValue) as T;
    } catch (error) {
      console.error(`Error parsing sessionStorage key: ${key}`, error);
      return null;
    }
  }
  
}