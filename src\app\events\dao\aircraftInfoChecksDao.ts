import { ServiceWarningDays } from "./serviceWarningDaysDao";

export class AircraftInfoChecks {
    serviceWarningDays: ServiceWarningDays;
    emrStatus: string;
    fob: string;
    statusCode: string;
    constructor(data: Partial<AircraftInfoChecks> = {}) {
        this.serviceWarningDays = data.serviceWarningDays || new ServiceWarningDays();
        this.emrStatus = data.emrStatus || '';
        this.fob = data.fob || '';
        this.statusCode = data.statusCode || '';
    }

}