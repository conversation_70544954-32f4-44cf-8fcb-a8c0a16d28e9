import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { RetrievalDto } from '../../../../dto/retrievalDto';

import { CdkDrag } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-discrepancies-detail-dialog',
  templateUrl: './discrepancies-detail-dialog.component.html',
  styleUrl: './discrepancies-detail-dialog.component.scss',
  standalone: false
})
export class DiscrepanciesDetailDialogComponent implements OnInit {

  rowData: any;
  discrepancyDetails: any;

  constructor(
      public dialogRef: MatDialogRef<DiscrepanciesDetailDialogComponent>,
      private maintenanceEventDetailsService: MaintenanceEventDetailsService,
      private cdRef: ChangeDetectorRef,
      @Inject(MAT_DIALOG_DATA) public data: { rowData: any, acn: number, eventId: any }
    ) {
      this.rowData = data.rowData;
      this.maintenanceEventDetailsService.getDiscrepancyDetailInfo(data.acn, data.rowData.ata, data.rowData.number).subscribe({
        next: (response: any) => {
          this.discrepancyDetails = response;
          this.cdRef.detectChanges();
        }, error: (error) => {
          console.error('Error fetching maintenance discrepancies details header:', error);
        }
      });
    }
    
  ngOnInit() {}

  close(): void {
    this.dialogRef.close();
  }

}
