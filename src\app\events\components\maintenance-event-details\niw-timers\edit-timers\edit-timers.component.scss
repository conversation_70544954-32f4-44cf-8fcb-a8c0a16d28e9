/* Styling for the dialog container to support animations */
.custom-msn-dialog {
  overflow: hidden; /* Prevents content from overflowing during animation */
  transform-origin: center; /* Ensures scaling happens from the center */
}

/* Animation for dialog opening */
.mat-dialog-container {
  animation: dialogOpen 0.3s ease-out forwards;
}

/* Animation for dialog closing */
.mat-dialog-container.ng-animating {
  animation: dialogClose 0.3s ease-in forwards;
}

/* Keyframes for dialog opening: fade in and scale up */
@keyframes dialogOpen {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Keyframes for dialog closing: fade out and slide down */
@keyframes dialogClose {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(50px);
  }
}

/* Backdrop animation for the dialog overlay */
.cdk-overlay-backdrop {
  animation: backdropFadeIn 0.3s ease-out forwards;
}

/* Keyframes for backdrop fade-in */
@keyframes backdropFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0.5; /* Matches default Material dialog backdrop opacity */
  }
}

/* Dialog container styling */
.custom-msn-dialog .mat-dialog-container {
  border-radius: 12px; /* Rounded corners for a modern look */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2); /* Subtle shadow for depth */
  background-color: #fff; /* White background */
}

/* Dialog title styling */
.niw-timer-dialog .dialog-title {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: #ffffff;
  padding: 10px 25px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.niw-timer-dialog .title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.niw-timer-dialog .dialog-title-text {
  font-size: 1.25rem;
  font-weight: 600;
  flex-grow: 1;
  text-align: center;
}

.niw-timer-dialog .close-icon {
  color: red; /* White cancel icon */
  font-size: 24px;
  cursor: pointer;
  border-radius: 50%; /* Circular shape */
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 32px; /* Center icon vertically */
  transition: background-color 0.2s ease; /* Smooth hover transition */
}

.niw-timer-dialog .close-icon:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Dialog content styling */
.niw-timer-dialog .dialog-content {
  padding: 1.5rem;
  font-family: 'Segoe UI', sans-serif;
}

.niw-timer-dialog .timer-label {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  font-weight: bold;
}

.niw-timer-dialog .timer-label .label {
  font-size: 15px;
  color: #6c49b9;
}

.niw-timer-dialog .timer-label .timer-name {
  font-size: 18px;
  color: #ff6600;
  margin-left: 0.5rem;
}

.niw-timer-dialog .table-wrapper {
  max-height: 40vh;
  overflow: auto;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06), 0 8px 20px rgba(0, 0, 0, 0.12);
}

.niw-timer-dialog .mat-table-custom {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-radius: 10px;
  font-size: 11px;
}

.niw-timer-dialog .mat-table-custom th,
.niw-timer-dialog .mat-table-custom td {
  padding: 6px 8px;
  border-bottom: 1px solid #eee;
  text-align: center;
  word-wrap: break-word;
}

.niw-timer-dialog .mat-table-custom th {
  position: sticky;
  top: 0;
  z-index: 2;
  background: linear-gradient(to right, #e0e0e0, #f0f0f0);
  font-weight: 600;
  color: #333;
  font-size: 12px;
}

.niw-timer-dialog .mat-table-custom tr:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.niw-timer-dialog .mat-table-custom .selected {
  background-color: #FAE4D6 !important;
}

/* Dialog actions styling */
.niw-timer-dialog .dialog-actions {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.niw-timer-dialog .dialog-actions button {
  min-width: 18%;
  font-weight: 600;
  border-radius: 10px;
  padding: 0.5rem 1rem;
  color: white;
  transition: background 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.niw-timer-dialog .btn-purple {
  background-color: #6c49b9;
}

.niw-timer-dialog .btn-purple:hover {
  background-color: #3F2876;
}

.niw-timer-dialog .btn-orange {
  background-color: #ff6600;
}

.niw-timer-dialog .btn-orange:hover {
  background-color: #e65c00;
}

.niw-timer-dialog .btn-orange.disabled {
  background-color: lightgray;
  cursor: not-allowed;
}

/* Smooth transitions for dialog buttons */
.custom-msn-dialog button {
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.custom-msn-dialog button:hover {
  transform: translateY(-2px); /* Slight lift effect on hover */
}