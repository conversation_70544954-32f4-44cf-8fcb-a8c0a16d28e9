import { <PERSON><PERSON>iew<PERSON>nit, ChangeDetector<PERSON><PERSON>, <PERSON>mponent, ElementRef, HostListener, NgZone, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { CellContextMenuEvent, ClientSideRowModelModule, ColDef, GridApi, GridO<PERSON>s, RowDoubleClickedEvent } from 'ag-grid-community';
import { Module } from 'ag-grid-community';
import { AppLayoutService } from '../../../app-layout/services/app-layout.service';
import { filter, Subject, take, takeUntil } from 'rxjs';
import { CustomMaintenanceEventListHeaderComponent } from './custom-maintenance-event-list-header/custom-maintenance-event-list-header.component';
import { MaintenanceEventListService } from '../../services/maintenance-event-list.service';
import { CustomMaintenanceEventListStatusComponent } from './custom-maintenance-event-list-status/custom-maintenance-event-list-status.component';
import { MatMenuTrigger } from '@angular/material/menu';
import { FormControl } from '@angular/forms';
import { CustomMaintenanceEventListEticComponent } from './custom-maintenance-event-list-etic/custom-maintenance-event-list-etic.component';
import { CustomMaintenanceEventListAlertsComponent } from './custom-maintenance-event-list-alerts/custom-maintenance-event-list-alerts.component';
import { MainComponent } from '../../../app-layout/components/main/main.component';
import { ScreenPreference, UserPreferences } from './maintenance-event-list-interfaces';
import { CustomiseSettingsIconComponent } from './customise-settings-icon/customise-settings-icon.component';
import { HideButtonComponent } from './hide-button/hide-button.component';
import { MatDialog } from '@angular/material/dialog';
import { animate, style, transition, trigger } from '@angular/animations';
import { Router } from '@angular/router';
import { MainService } from '../../../app-layout/services/main.service';
import { AppComponentService } from '../../../app-component.service';
import { MaintenanceEventListResponseDao } from '../../dao/maintenence-event-listDao';
import { ToastrMessageService } from '../../../app-layout/services/toastr-message.service';
import { MatPseudoCheckboxState } from '@angular/material/core';
import { ChangeStatusETICComponent } from './maintenance-event-list-actions/change-status-etic/change-status-etic.component';
import { PendingConfirmationComponent } from './maintenance-event-list-actions/change-status-etic/pending-confirmation/pending-confirmation.component';
import { MaintenanceEventDetailsService } from '../../services/maintenance-event-details.service';
import { TubFileNotesResponseDto } from '../../dto/TubFileNotesResponseDto';
import { SuccessErrorDialogComponent } from './maintenance-event-list-actions/change-status-etic/success-error-dialog/success-error-dialog.component';
import { UserAddedAcn } from '../../constants/userAddedAcn';
import { CloseEventComponent } from './close-event/close-event.component';
import { AcnCacheResponseDao } from '../../dao/fleetsDao';

@Component({
  selector: 'app-maintenance-event-list',
  templateUrl: './maintenance-event-list.component.html',
  styleUrl: './maintenance-event-list.component.scss',
  standalone: false,
  animations: [
    trigger('fadeSlide', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('0.5s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('0.5s ease-in', style({ opacity: 0, transform: 'translateY(-20px)' }))
      ])
    ])
  ]
})
export class MaintenanceEventListComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild(MatMenuTrigger) actionsMenuTrigger!: MatMenuTrigger;
  @ViewChild(MatMenuTrigger) contextMenuTrigger!: MatMenuTrigger;
  @ViewChild('agGrid') agGrid!: ElementRef;

  acnInput: string = '';
  isContextMenuVisible = false;
  contextMenuPosition = { x: 0, y: 0 };

  defaultEvents = ['AOG', 'DWN', 'HMX', 'DOA', 'TRK', 'UP'];
  eventsControl = new FormControl();
  fleetControl = new FormControl();
  regionControl = new FormControl();
  stationControl = new FormControl();
  hideMenuOptions = ['ACN', 'Fleet', 'Sta', 'Status', 'Fit Out/Dt', 'Comment', 'Start', 'Dept', 'ETIC', 'OST', 'Duration', 'Alerts'];

  events: any[] = [];
  fleets: any[] = [];
  regions = [];
  stations = [];
  filteredRegions = [];
  filteredStations = [];
  eventSelectAllState: MatPseudoCheckboxState = 'unchecked'; 
  fleetSelectAllState: MatPseudoCheckboxState = 'unchecked';
  selectedEvents: (string | null)[] = [];
  selectedFleets: (string | null)[]= [];
  stationRegions: string[] = [];
  selectedRegion: string | null = null;
  selectedstation: string | null = null;

  powerPlantChecked: boolean = false;
  isRowSelected: boolean = false;
  isChangeStatusEticDisabled: boolean = false;
  isConvertEventDisabled: boolean = false;
  isCloseEventDisabled: boolean = false;
  isCancelEventDisabled: boolean = false;
  isReviewAlertDiabled: boolean = false;
  showContextMenu: boolean = false;
  isListResponseReceived: boolean = false;
  isCardVisible: boolean = true;
  isSideNavClosed: boolean = false;

  public pinnedTopRowData: MaintenanceEventListResponseDao[] = [];
  rowData: MaintenanceEventListResponseDao[] = [];
  filteredEventListTableData: MaintenanceEventListResponseDao[] = [];
  selectedRow: MaintenanceEventListResponseDao | null = null;
  tempresult: any;
  columnDefs: ColDef[] = [];
  storedDefaultColumnDefs: any;
  tableMissingColumns: ColDef[] = [];
  gridHeight: number = 37;
  fleetPreferenceValues: any[] = [];
  eventPreferenceValues: any[] = [];
  screenPref: UserPreferences[] = [];
  screenPreference: ScreenPreference = {};

  gridApi!: GridApi;
  gridColumnApi: any;
  gridOptions: GridOptions = {
    rowBuffer: 0, // Disable row buffering to ensure all rows are processed
    rowHeight: 30, // Ens
    suppressColumnVirtualisation: false, // Enable column virtualization if needed
    suppressScrollOnNewData: false, // Allow scrolling to new data
    suppressAnimationFrame: false, // Enable animations for smoother updates
    animateRows: true,
    headerHeight: 40,
    enableBrowserTooltips: true,
    suppressRowClickSelection: false,
    onSortChanged: this.onSortChanged.bind(this),
    suppressContextMenu: true,
    onCellContextMenu: (event) => this.onRightClick(event),
    rowStyle: {
      fontSize: '13px',
      padding: '0px',
      margin: '0px',
      border: 'none',
    },
    context: {
      onSettingsClick: this.openHideOptionsDialog.bind(this)
    },
    // Ensure all rows are rendered
    ensureDomOrder: true,
    // Increase pagination or disable it to handle large datasets
    pagination: false, // Disable pagination to show all rows
  };

  defaultColDef: ColDef = {
    headerClass: 'center-header',
    headerComponentParams: { menuIcon: true },
    cellStyle: {
      textAlign: 'center',
      verticalAlign: 'middle',
      padding: '0px',  // Remove cell padding
      border: 'none',  // Remove cell borders
    },
    sortable: true,
    filter: true,
    editable: false,
    resizable: true,
  };

  frameworkComponents = {
    customMaintenanceEventListHeader: CustomMaintenanceEventListHeaderComponent,
    customMaintenanceEventListStatus: CustomMaintenanceEventListStatusComponent,
    customMaintenanceEventListEtic: CustomMaintenanceEventListEticComponent,
    customMaintenanceEventListAlerts: CustomMaintenanceEventListAlertsComponent,
    customiseSettingsIconComponent: CustomiseSettingsIconComponent
  };

  modules: Module[] = [ClientSideRowModelModule];

  private timeInterval: any;
  private destroy$ = new Subject<void>();
  isClosed = false;

  constructor(private appLayoutService: AppLayoutService,
    private maintenanceEventListService: MaintenanceEventListService,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private cdRef: ChangeDetectorRef,
    private mainComponent: MainComponent,
    private ngZone: NgZone,
    public dialog: MatDialog,
    private mainService: MainService,
    private router: Router,
    private appComponentService: AppComponentService,
    private toastrMessageService: ToastrMessageService
  ) { }

  ngOnInit(): void {
    this.appLayoutService.sideNavClosedEmitterFromPreferences$.subscribe((isClosed: boolean) => {
      this.isSideNavClosed = isClosed;
      this.setColumnDefs(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.sideNavClosed || false);
      this.cdRef.detectChanges();
    });
    this.maintenanceEventListService.removeAcnFromEventListTable$.subscribe((acn: string) => {
      if (acn != "") {
        this.filteredEventListTableData = this.filteredEventListTableData.filter((item: any) => item.acn !== acn);
        this.gridApi.redrawRows();
        this.gridApi.refreshCells();
        this.cdRef.detectChanges();
      }
    });
    this.toggleCard(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.isOptionsClosed || null, false)
    this.appComponentService.preferences$.subscribe((preferences) => {
      preferences != null ? this.loadUserPreferences() : null;
    });
    // this.setEventPreferences();
    // this.setFleetPreferences();
    document.getElementById("agGridTable")?.addEventListener("contextmenu", (event) => {
      event.preventDefault(); // Prevent the default browser right-click menu
    });
    this.appLayoutService.sideNavClosedObservable$
    .pipe(takeUntil(this.destroy$))
    .subscribe((isClosed: any) => {
      this.isSideNavClosed = isClosed;
      this.setColumnDefs(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.sideNavClosed || false);
      // this.calculateGridHeight();
      this.adjustGridSizeSmoothly();
    });
    window.addEventListener('delete-acn', (event: any) => {
      this.handleDeleteUserAddedAcn(event.detail);
      console.log('Delete ACN event received:', event);
    });
    this.mainService.deselectSelectedTabInSessionStorage();
    this.cdRef.detectChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    document.removeEventListener('click', (event: MouseEvent) => this.closeContextMenu(event));
    this.maintenanceEventListService.saveSelectedValuesInStorage(this.selectedEvents, this.selectedFleets, this.selectedRegion, this.selectedstation, this.powerPlantChecked);
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
    // this.fleetPreferenceValues = this.fleetControl.getRawValue();
    // this.eventPreferenceValues = this.eventsControl.getRawValue();
    // this.maintenanceEventListService.saveSelectedValuesInStorage(this.eventPreferenceValues, this.fleetPreferenceValues);
  }

  // In your component's TypeScript file
  ngAfterViewInit() {
    const gridContainer = document.querySelector('.ag-theme-alpine');
    if (gridContainer) {
      gridContainer.addEventListener('contextmenu', (event) => {
        event.preventDefault(); // Prevent the default context menu
      });
    }
    setTimeout(() => {
      if (this.agGrid?.nativeElement) {
        this.agGrid.nativeElement.addEventListener("contextmenu", (event: MouseEvent) => {
          event.preventDefault();
        });
      }
    });
    // Apply it globally as well
    window.addEventListener("contextmenu", (event: MouseEvent) => {
      event.preventDefault();
    });
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.calculateGridHeight(); // Recalculate grid height on window resize
    this.setColumnDefs(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.sideNavClosed || false);
    this.resizeGridColumns();
  }

  @HostListener('document:click', ['$event'])
  closeContextMenu(event: MouseEvent) {
    if (!(event.target as HTMLElement).closest('.custom-context-menu')) {
      this.isContextMenuVisible = false;
    }
  }

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: Event) {
    const columnState = this.gridApi.getColumnState();
    const optionsFromSessionStorage = this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage() === null ? 
          { sideNavClosed: false, isOptionsClosed: false } : this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage();
    optionsFromSessionStorage.sideNavClosed === null ? optionsFromSessionStorage.sideNavClosed = false : null;
    optionsFromSessionStorage.isOptionsClosed === null ? optionsFromSessionStorage.isOptionsClosed = false : null;
          let headerNames: any[] = [];
    if (columnState) {
      headerNames = columnState.map((col: any) => col.colId);
    } else {
      console.warn('No column state available from grid API.');
    }
    this.fleetPreferenceValues = this.selectedFleets;
    this.eventPreferenceValues = this.selectedEvents;
    this.screenPref.push({ preferenceName: "Table", preferenceValues: headerNames });
    this.screenPref.push({ preferenceName: "Fleet", preferenceValues: this.fleetPreferenceValues });
    this.screenPref.push({ preferenceName: "Event", preferenceValues: this.eventPreferenceValues });
    this.screenPref.push({ preferenceName: "Region", preferenceValues: [this.selectedRegion || ''] });
    this.screenPref.push({ preferenceName: "Station", preferenceValues: [this.selectedstation || ''] });
    this.screenPref.push({ preferenceName: "PowerPlantChecked", preferenceValues: [String(this.powerPlantChecked)] })
    this.screenPref.push({ preferenceName: "sideNavClosed", preferenceValues: [optionsFromSessionStorage.sideNavClosed] });
    this.screenPref.push({ preferenceName: "isOptionsClosed", preferenceValues: [optionsFromSessionStorage.isOptionsClosed] });
    this.screenPreference.screenName = "Home";
    this.screenPreference.preferenceInfo = this.screenPref;
    this.maintenanceEventListService.saveUserPreferences(this.screenPreference).subscribe({
      next: (data) => {
      },
      error: (error) => {
      }
    });
  }

  reset() {
    this.toastrMessageService.error('Resetting the grid is not implemented yet.');
  }

  loadUserPreferences(): void {
    this.appComponentService.preferences$.pipe(
      filter(preferences => preferences !== null),
      take(1)
    ).subscribe((preferences) => {
      const sideNavAndOptionPreferencesFromStorage = this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage();
      this.tempresult = preferences;
      this.setEventPreferences();
      this.setFleetPreferences();
      this.getRegionsList(true);
      this.setPowerPlantChecked();
      this.displayMaintenanceEventList();
      this.setColumnDefs(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.sideNavClosed || false);
      setTimeout(() => { this.reorderRowData(); }, 0);
      this.filterTableData();
      this.calculateGridHeight();
      this.getUserPreferences((result) => {
        this.toggleCard(sideNavAndOptionPreferencesFromStorage === null ? result && result.length > 0 ? JSON.parse(result) : false : sideNavAndOptionPreferencesFromStorage.isOptionsClosed, false);
      }, "isOptionsClosed");
      this.getUserPreferences((result) => {
        this.appLayoutService.setSideNavClosedFromPreferences(sideNavAndOptionPreferencesFromStorage === null ? result && result.length > 0 ? JSON.parse(result) : false : sideNavAndOptionPreferencesFromStorage.sideNavClosed);
      }, "sideNavClosed");
      if (sideNavAndOptionPreferencesFromStorage === null) {
        this.appLayoutService.setSideNavClosedInSessionStorage(JSON.parse(this.tempresult?.preferenceInfo.find((pref: any) => pref.preferenceName === "sideNavClosed").preferenceValues[0]) || false);
        this.appLayoutService.setListMenuOptionsClosedInSessionStorage(JSON.parse(this.tempresult?.preferenceInfo.find((pref: any) => pref.preferenceName === "isOptionsClosed").preferenceValues[0]) || false);
      }
    });
  }

  // loadUserPreferences() {
  //   this.mainComponentService.getUserPreferences().subscribe(
  //     (data: any) => {
  //       this.tempresult = data.screenName;
  //       this.setFleetPreferences();
  //       this.setEventPreferences();
  //     },
  //     (error) => {
  //       console.error('Error fetching preferences:', error);
  //     }
  //   );

  // }

  setFleetPreferences(): void {
    this.maintenanceEventListService.getFeeltValues().subscribe({
      next: (data: AcnCacheResponseDao) => {
        const uniqueFleetCodes = Array.from(new Set(data.ACN_CACHE_DETAIL.map(item => item.fleetCode))).sort();
        const fleetValuesFromSession = this.maintenanceEventListService.getSelectedValuesFromStorage().selectedFleets;
        if (fleetValuesFromSession!==null && fleetValuesFromSession.length > 0) {
          // this.gridApi.applyColumnState({ state: storedColumnState });
          this.fleets = uniqueFleetCodes;
          this.fleetControl.setValue(fleetValuesFromSession);
          this.fleetControl.patchValue(fleetValuesFromSession);
          this.selectedFleets = [...fleetValuesFromSession];
        } else {
          this.getUserPreferences((result) => {
            if (result) {
              // let set = new Set(result);
              this.fleets = uniqueFleetCodes;
              // let userPreferredFleets = [];
              // this.fleets.forEach((fleet) => {
              //   if (set.has(fleet.name)) {
              //     userPreferredFleets.push(fleet.name);
              //   }
              this.fleetControl.setValue(result);
              this.fleetControl.patchValue(result);
              this.selectedFleets = [...result];
              // });
            } else {
              this.fleets = uniqueFleetCodes;
              this.fleetControl.setValue(uniqueFleetCodes);
              this.fleetControl.patchValue(uniqueFleetCodes);
              this.selectedFleets = [...uniqueFleetCodes];
            }
            this.cdRef.detectChanges();
          }, "Fleet");
        }
      }, error: (error) => {
        console.error('Error fetching fleet data:', error);
      }
    });
  }

  setEventPreferences() {
    this.maintenanceEventListService.getEventValues().subscribe({
      next: (data: any[]) => {
        const eventValuesFromSession = this.maintenanceEventListService.getSelectedValuesFromStorage().selectedEvents;
        if (eventValuesFromSession!==null && eventValuesFromSession.length > 0) {
          // this.gridApi.applyColumnState({ state: storedColumnState });
          this.events = data;
          this.eventsControl.setValue(eventValuesFromSession);
          this.eventsControl.patchValue(eventValuesFromSession);
          this.selectedEvents = [...eventValuesFromSession];
        } else {
          this.getUserPreferences((result) => {
            if (result) {
              // let set = new Set(result);
              // let userPreferredEvents = [];
              this.events = data;
              // this.events.forEach((event) => {
              //   if (set.has(event.name)) {
              //     userPreferredEvents.push(event.name);
              //   } 
              this.eventsControl.setValue(result);
              this.eventsControl.patchValue(result);
              this.selectedEvents = [...result];
              // });
            } else {
              this.events = data;
              this.eventsControl.setValue(this.defaultEvents);
              this.eventsControl.patchValue(this.defaultEvents);
              this.selectedEvents = [...this.defaultEvents];
            }
            this.cdRef.detectChanges();
          }, "Event");
        }
      },
      error: (error) => {
        console.error('Error fetching fleet data:', error);
      }
    });
  }

  setPowerPlantChecked() {
    this.getUserPreferences((result) => {
      if (result) {
        this.powerPlantChecked = result[0] === 'true' ? true : false;
      } else {
        this.powerPlantChecked = false;
      }
    }, "PowerPlantChecked");
  }

  getUserPreferences(callback: (result: any) => void, preferenceType: string): any {
    let finalResult: any;
    switch (preferenceType.toLowerCase()) {
      case "Table".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "Table".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "Fleet".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "Fleet".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "Event".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "Event".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "Region".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "Region".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "Station".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "Station".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "PowerPlantChecked".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "PowerPlantChecked".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "sideNavClosed".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "sideNavClosed".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
      case "isOptionsClosed".toLowerCase(): {
        try {
          finalResult = this.tempresult?.preferenceInfo.find((name: any) => name.preferenceName.toLowerCase() === "isOptionsClosed".toLowerCase());
          callback(finalResult?.preferenceValues);
        } catch (error) {
          console.error('Error fetching preferences:', error);
          callback(null);
        }
        break;
      }
    }
  }

  toggleCard(isClose: any, isSaveInStorage: boolean) {
    this.isCardVisible = isClose != null ? !isClose : !this.isCardVisible;
    setTimeout(() => {
      this.ngZone.run(() => {
        this.calculateGridHeight();
        // this.gridHeight = this.gridHeight + (!this.isCardVisible ? 113 : -113);
        this.cdRef.detectChanges();
      });
    }, this.isCardVisible ? 350 : 130);
    isSaveInStorage ? this.appLayoutService.setListMenuOptionsClosedInSessionStorage(isClose != null ? isClose : !this.isCardVisible) : null;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // setTimeout(() => { this.applyColumnReorder(this.maintenanceEventListService.getMaintenanceListCustomizeOptionsFromSessionStorage()); }, 500);
    params.api.sizeColumnsToFit();
  }

  onColumnReorder(event: any) {
    const currentColumnState = this.gridApi.getColumnState();
    let updatedHideSettingsColumnState: any[] = [];
    this.maintenanceEventListService.saveEventListTableColumnsInStorage(currentColumnState);
    const currentSettingsColumnState = this.maintenanceEventListService.getMaintenanceListSettingsOptionsFromStorage();
    if (currentSettingsColumnState != null) {
      const dataMap: Map<any, any> = new Map();
      currentSettingsColumnState.forEach((item: any) => {
        dataMap.set(item.displayName, item);
      });
      currentColumnState.forEach((item: any) => {
        if (dataMap.has(item.colId)) {
          item.colId != "settings" ? updatedHideSettingsColumnState.push(dataMap.get(item.colId)) : null;
        }
      });
    } else {
      currentColumnState.forEach((item: any) => {
        item.colId != "settings" ? updatedHideSettingsColumnState.push({ name: this.getEventListTableColumnName(item.colId), selected: !item.hide, displayName: item.colId }) : null;
      });
    }
    this.maintenanceEventListService.saveMaintenanceListSettingsOptionsInStorage(updatedHideSettingsColumnState);
  }

  onRightClick(event: CellContextMenuEvent) {
    if (!event.event) return; // Exit if the event is null or undefined

    event.event.preventDefault(); // Disable default right-click menu

    // Select the row
    const rowNode = event.node;
    if (rowNode) {
      this.gridApi.getSelectedNodes().forEach(node => node.setSelected(false));
      rowNode.setSelected(true, true);

      this.selectedRow = rowNode.data;
      // Define computed properties for the disabled states
      this.isChangeStatusEticDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? true : false;
      this.isConvertEventDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? false : true;
      this.isCloseEventDisabled = (this.selectedRow?.status == 'UP' || this.selectedRow?.status == 'DOA') ? true : false;
      this.isCancelEventDisabled = (this.selectedRow?.status == 'UP') ? true : false;
      this.isReviewAlertDiabled = (this.selectedRow?.requestStatus == "C") ? false : true;

      // Get mouse position
      const mouseEvent = event.event as MouseEvent;
      let clickX = mouseEvent.clientX;
      let clickY = mouseEvent.clientY;

      // Ensure menu stays within the screen bounds
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      const menuWidth = 200;  // Approximate menu width
      const menuHeight = 270; // Approximate menu height

      if (clickX + menuWidth > screenWidth) {
        clickX = screenWidth - menuWidth - 10;
      }

      if (clickY + menuHeight > screenHeight) {
        clickY = screenHeight - 180 - 110;
      }

      // Show the custom menu
      this.contextMenuPosition = { x: clickX, y: clickY };
      this.isContextMenuVisible = true;
    }
  }

  disableRightClick(event: MouseEvent) {
    event.preventDefault();
  }

  onRowClicked(event: any) {
    if (event.node.rowPinned) {
      const rowNode = this.gridApi.getRowNode(event.node.id);
      if (rowNode) {
        rowNode.setSelected(true);
        this.cdRef.detectChanges();
      }
    }
  }

  onRowDoubleClick(event: RowDoubleClickedEvent) {
    const detailTabsSessionStorageData = this.mainService.getAcnTabDataFromSessionStorage();
    if (detailTabsSessionStorageData) {
      const foundExistingTabData = detailTabsSessionStorageData.findIndex((tab: any) => tab.name.includes(event.data.acn));
      if (foundExistingTabData != -1) {
        this.mainService.setSelectedTabDataToSessionStorage(detailTabsSessionStorageData[foundExistingTabData]);
        this.mainService.triggerUpdateDetailsTabs(true);
        this.mainService.refreshDetailsAcnTabData(true);
        this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: event.data.acn } });
      } else {
        const ids = detailTabsSessionStorageData.map((item: any) => item.id);
        this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${event.data.acn}`, eventId: event.data.eventID, eventType: event.data.type, selected: true, userAdded: false });
        this.mainService.setSelectedTabDataToSessionStorage({ name: `ACN - ${event.data.acn}`, eventId: event.data.eventID, eventType: event.data.type, selected: true, userAdded: false });
        this.mainService.triggerUpdateDetailsTabs(true);
        this.mainService.refreshDetailsAcnTabData(true);
        this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: event.data.acn } });
      }
    } else {
      this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${event.data.acn}`, eventId: event.data.eventID, eventType: event.data.type, selected: true, userAdded: false });
      this.mainService.setSelectedTabDataToSessionStorage({ name: `ACN - ${event.data.acn}`, eventId: event.data.eventID, eventType: event.data.type, selected: true, userAdded: false });
      this.mainService.triggerUpdateDetailsTabs(true);
      this.mainService.refreshDetailsAcnTabData(true);
      this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: event.data.acn } });
    }
  }

  onRowSelected(event: any) {
    this.selectedRow = event.api.getSelectedRows()[0];
    this.selectedRow != undefined && this.selectedRow != null ? this.isRowSelected = true : this.isRowSelected = false;
    this.isChangeStatusEticDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? true : false;
    this.isConvertEventDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? false : true;
    this.isCloseEventDisabled = (this.selectedRow?.status == 'UP' || this.selectedRow?.status == 'DOA') ? true : false;
    this.isCancelEventDisabled = (this.selectedRow?.status == 'UP') ? true : false;
    this.isReviewAlertDiabled = (this.selectedRow?.requestStatus == "C") ? false : true;
  }

  // selectDefaultFormOptions() {
  //   this.selectedEvents = ['AOG', 'DWN', 'HMX', 'DOA', 'TRK', 'UP'];
  //   this.selectedFleets = ['A300-600', 'A310', 'B727-200', 'B747-200', 'MD10-10', 'MD10-30', 'MD11', 'B757-200', 'B767', 'B777'];
  // }

  getTooltipText(selectedValues: any): string {
    return selectedValues != null ? selectedValues.length > 0 ? selectedValues.join(', ') : '' : '';
  }

  onChangeStatus() {
    if (this.selectedRow != null || this.selectedRow != undefined) {
      this.maintenanceEventDetailsService.getTubfileNotes(this.selectedRow?.eventID).subscribe((response: TubFileNotesResponseDto[]) => {
        if (this.selectedRow?.requestStatus === 'S') {
          const dialogRef = this.dialog.open(PendingConfirmationComponent, {
            data: { selectedRow: this.selectedRow, tubfilenote: response },
            disableClose: true,
            autoFocus: false,
            width: '50%',
            maxWidth: '90vw',
            panelClass: 'custom-change-staus-dialog'
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result.modify) {
              const dialogRef = this.dialog.open(ChangeStatusETICComponent, {
                data: { selectedRow: this.selectedRow, alreadyExistsChangeRequest: true, existingTubfilenotes: response },
                disableClose: true,
                autoFocus: false,
                width: '90%',
                maxWidth: '90vw',
                height: '90%',
                panelClass: 'custom-change-staus-dialog'
              });

              dialogRef.afterClosed().subscribe((response) => {
                const dialogRef = this.dialog.open(SuccessErrorDialogComponent, {
                  data: {
                    isSuccess: response.isSuccess,
                    message: response.message,
                  },
                  width: '50vw', // Set dialog width to 50% of viewport
                  maxWidth: '600px', // Upper limit for larger screens
                  minWidth: '300px', // Minimum width for smaller screens
                  panelClass: 'no-padding-dialog', // Custom class for no padding marked
                  disableClose: true // Prevent closing by clicking outside
                });
              });
            }
            this.cdRef.detectChanges();
          });
        } else {
          const dialogRef = this.dialog.open(ChangeStatusETICComponent, {
            data: { selectedRow: this.selectedRow, alreadyExistsChangeRequest: false, existingTubfilenotes: response },
            disableClose: true,
            autoFocus: false,
            width: '90%',
            maxWidth: '90vw',
            height: '90%',
            panelClass: 'custom-change-staus-dialog'
          });

          dialogRef.afterClosed().subscribe((response: any) => {
            const dialogRef = this.dialog.open(SuccessErrorDialogComponent, {
              data: {
                isSuccess: response.isSuccess,
                message: response.message,
              },
              width: '50vw', // Set dialog width to 50% of viewport
              maxWidth: '600px', // Upper limit for larger screens
              minWidth: '300px', // Minimum width for smaller screens
              panelClass: 'no-padding-dialog', // Custom class for no padding marked
              disableClose: true // Prevent closing by clicking outside
            });
          });
        }
      });
    }

    this.isContextMenuVisible = false;
  }

  onConvertEvent() {
    this.isContextMenuVisible = false;
  }

  onCloseEvent() {
    this.isContextMenuVisible = false;

    const dialogRef = this.dialog.open(CloseEventComponent, {
      width: '80vw',
      height: '70vh',
      minWidth: '1000px',
      minHeight: '700px',
      maxWidth: '95vw',
      maxHeight: '95vh',
      disableClose: true,
      panelClass: 'close-event-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: false,
      data: {
        selectedEvent: this.selectedRow
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Close event dialog result:', result);
        if(result.eventData['ERROR']) {
          this.toastrMessageService.error(`Failed to close Event ${result.eventData.eventID}`);
        } else {
          this.toastrMessageService.success(`Event ${result.eventData.eventID} is closed successfully.`);
        }
      }
    });
  }

  onCancelEvent() {
    this.isContextMenuVisible = false;
  }

  onReviewAlert() {
    this.isContextMenuVisible = false;
  }

  adjustGridSizeSmoothly() {
    this.ngZone.runOutsideAngular(() => {
      requestAnimationFrame(() => {
        if (this.gridApi) {
          this.resizeGridColumns();
        }
      });
    });
  }

  resizeGridColumns() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
      // this.gridApi.setColumnDefs(this.columnDefs);
    }
  }

  setColumnDefs(isSideNavClosed: boolean) {
    let columnWidths = { flightDepartureDetails: 8, flightDetails: 8, curComment: 14, eticDateTime: 9, status: 6, owner: 6, ost: 5, station: 5, fleetDesc: 7, acn: 5, durationData: 8, startDateTime: 8, alerts: 11, settings: 0 };
    interface ColumnDefinition { field: keyof typeof columnWidths; headerComponent?: any; cellRenderer?: any; flex?: number;[key: string]: any; }

    if (isSideNavClosed) {
      columnWidths = { flightDepartureDetails: 7, flightDetails: 6.5, curComment: 23, eticDateTime: 8, status: 6, owner: 6, ost: 4, station: 4, fleetDesc: 6,
         acn: 4.5, durationData: 7, startDateTime: 7, alerts: 11, settings: 0 };
    }

    const columnStateFromSessionStorage = this.maintenanceEventListService.getEventListTableColumnsFromStorage();

    if (this.columnDefs.length === 0) {
      this.maintenanceEventListService.getMaintenanceEventListTableHeaders().subscribe({
        next: (data: ColumnDefinition[]) => {
          const dataMap: Map<any, any> = new Map();
          data.forEach((column: any) => column.filter = true);
          data.forEach((column) => {
            column.headerComponent = column.field !== "settings" ? CustomMaintenanceEventListHeaderComponent : CustomiseSettingsIconComponent;
            
            if (column.field === "status") {
              column.cellRenderer = CustomMaintenanceEventListStatusComponent;
            } else if (column.field === "eticDateTime") {
              column.cellRenderer = CustomMaintenanceEventListEticComponent;
            } else if (column.field === "alerts") {
              column.cellRenderer = CustomMaintenanceEventListAlertsComponent;
            }
            if (column.field === "ost") {
              column.cellRenderer = (params: any) => {
                const ost = params.data?.ost;
                const newOST = params.data?.newOST;

                if (ost === 'N' && newOST === 'N') {
                  return '';
                }
                if (ost === 'N' && newOST === 'Y') {
                  return `<span style="color: red; font-weight: bold;">Y</span>`;
                }
                return params.value != null ? params.value : '';
              };
            }
            if (column.field === "settings") {
              column['pinned'] = 'right';
              column['suppressMovable'] = true;
              column['lockPosition'] = true;
              column['cellRenderer'] = (params: any) => {
                if (params.data?.isUserAddedAcn) {
                  return `<span class="delete-icon" onclick="window.dispatchEvent(new CustomEvent('delete-acn', { detail: { acn: '${params.data.acn}', eventId: '${params.data.eventID}', eventType: '${params.data.type} } }))">
                            &#128465;
                          </span>`;
                }
                return '';
              };
            }
            column.flex = column.field === "settings" ? 0 : columnWidths[column.field as keyof typeof columnWidths] || 1; 
          });
          if (columnStateFromSessionStorage === null) {
            this.getUserPreferences((result) => {
              if (result) {
                data.forEach((column) => { dataMap.set(column.field, column) });
                var tempColumnDefs: any = [];
                result.forEach((column: string) => {
                  tempColumnDefs.push(dataMap.get(column));
                });
                this.columnDefs = tempColumnDefs;
              } else {
                this.columnDefs = data;
              }
              this.tableMissingColumns = data.filter((column) => !this.columnDefs.includes(column));
              if (this.tableMissingColumns.length > 0) {
                this.columnDefs.push(...this.tableMissingColumns);
              }
              this.cdRef.detectChanges();
            }, "Table");
          } else {
            const colIds = columnStateFromSessionStorage.map((col: any) => col.colId);
            data.forEach((column) => { dataMap.set(column.field, column) });
            var tempColumnDefs: any = [];
            colIds.forEach((column: string) => {
              tempColumnDefs.push(dataMap.get(column));
            });
            this.columnDefs = tempColumnDefs;
            this.tableMissingColumns = data.filter((column) => !this.columnDefs.includes(column));
            if (this.tableMissingColumns.length > 0) {
              this.columnDefs.push(...this.tableMissingColumns);
            }
          }
          this.storedDefaultColumnDefs = [...this.columnDefs];
          setTimeout(() => {
            this.cdRef.detectChanges();
            this.gridApi.redrawRows();
          }, 0);
        },
        error: (error) => {
          console.error("Error fetching column definitions:", error); // Handle errors if needed
        }
      });
    } else {
      this.columnDefs = [];
      const dataMap: Map<any, any> = new Map();
      this.storedDefaultColumnDefs.forEach((column: any) => {
        column.headerComponent = column.field !== "settings" ? CustomMaintenanceEventListHeaderComponent : CustomiseSettingsIconComponent;
      
        if (column.field === "status") {
          column.cellRenderer = CustomMaintenanceEventListStatusComponent;
        } else if (column.field === "eticDateTime") {
          column.cellRenderer = CustomMaintenanceEventListEticComponent;
        } else if (column.field === "alerts") {
          column.cellRenderer = CustomMaintenanceEventListAlertsComponent;
        }

        if (column.field === "ost") {
          column.cellRenderer = (params: any) => {
            const ost = params.data?.ost;
            const newOST = params.data?.newOST;

            if (ost === 'N' && newOST === 'N') {
              return '';
            }
            if (ost === 'N' && newOST === 'Y') {
              return `<span style="color: red; font-weight: bold;">Y</span>`;
            }
            return params.value != null ? params.value : '';
          };
        }

        if (column.field === "settings") {
          column['pinned'] = 'right';
          column['suppressMovable'] = true;
          column['lockPosition'] = true;
          column['cellRenderer'] = (params: any) => {
            if (params.data?.isUserAddedAcn) {
              return `<span class="delete-icon" onclick="window.dispatchEvent(new CustomEvent('delete-acn', { detail: { acn: '${params.data.acn}', eventId: '${params.data.eventID}', eventType: '${params.data.type}' } }))">
                        &#128465;
                      </span>`;
            }
            return '';
          };
        }
      
        column.flex = column.field === "settings" ? 0 : columnWidths[column.field as keyof typeof columnWidths] || 1;
      });
      if (columnStateFromSessionStorage === null) {
        this.getUserPreferences((result) => {
          if (result) {
            this.storedDefaultColumnDefs.forEach((column: any) => { dataMap.set(column.field, column) });
            var tempColumnDefs: any = [];
            result.forEach((column: string) => {
              tempColumnDefs.push(dataMap.get(column));
            });
            this.columnDefs = tempColumnDefs;
          } else {
            this.columnDefs = this.storedDefaultColumnDefs;
          }
          this.tableMissingColumns = this.storedDefaultColumnDefs.filter((column: any) => !this.columnDefs.includes(column));
          if (this.tableMissingColumns.length > 0) {
            this.columnDefs.push(...this.tableMissingColumns);
          }
          this.cdRef.detectChanges();
        }, "Table");
      } else {
        const colIds = columnStateFromSessionStorage.map((col: any) => col.colId);
        this.storedDefaultColumnDefs.forEach((column: any) => { dataMap.set(column.field, column) });
        var tempColumnDefs: any = [];
        colIds.forEach((column: string) => {
          tempColumnDefs.push(dataMap.get(column));
        });
        this.columnDefs = tempColumnDefs;
        this.tableMissingColumns = this.storedDefaultColumnDefs.filter((column: any) => !this.columnDefs.includes(column));
        if (this.tableMissingColumns.length > 0) {
          this.columnDefs.push(...this.tableMissingColumns);
        }
      }
      this.storedDefaultColumnDefs = [...this.columnDefs];
      setTimeout(() => {
        this.gridApi.setGridOption('columnDefs', this.columnDefs);
        this.cdRef.detectChanges();
        this.gridApi.redrawRows();
      }, 100);
    }
  }

  getStationsListFromRegion(isInitialLoad: boolean) {
    if (!this.selectedRegion) {
      !isInitialLoad ? this.selectedstation = null : null;
      this.stations = [];
      this.stationRegions = [];
      this.maintenanceEventListService.updateStationsList([]);
      this.filterTableData();
      
      return;
    }

    this.maintenanceEventListService.getStationsListFromRegion(this.selectedRegion).subscribe({
      next: (response) => {
        this.maintenanceEventListService.updateStationsList(response);
        this.stations = response;
        this.stationRegions = response;
        !isInitialLoad ? this.selectedstation = null : null;
        this.filterTableData();
      },
      error: (error) => {
        console.error('Error fetching maintenance event list:', error);
      }
    });
  }

  filterTableData() {
    if (!this.isListResponseReceived) return;

    this.maintenanceEventListService.saveSelectedValuesInStorage(
      this.selectedEvents, this.selectedFleets, this.selectedRegion, this.selectedstation, this.powerPlantChecked
    );

    let selectedEvents = [...this.selectedEvents];
    let selectedFleets = [...this.selectedFleets];

    if (selectedEvents.includes("DWN")) selectedEvents.push("HMD");
    if (selectedEvents.includes("NOS")) selectedEvents.push("NAS");
    if (selectedEvents.includes("HMX")) selectedEvents.push("HMO");
    if (selectedEvents.includes("Notes")) selectedEvents.push(null);

    const matchesEvent = (row: MaintenanceEventListResponseDao) => selectedEvents.includes(row.status);
    const matchesFleet = (row: MaintenanceEventListResponseDao) =>
      selectedFleets.some(fleet => fleet != null && row.fleetDesc?.toLowerCase().includes(fleet.toLowerCase()));
    const matchesPowerPlant = (row: MaintenanceEventListResponseDao) => row.isPowerPlantEvent === this.powerPlantChecked;
    const matchesStation = (row: MaintenanceEventListResponseDao) => 
      !this.selectedRegion || (
        this.stationRegions.includes(row.station ?? '') &&
        (!this.selectedstation || this.selectedstation === row.station)
      );

    // Filter the full rowData to ensure all rows are considered
    this.filteredEventListTableData = this.rowData.filter((row: MaintenanceEventListResponseDao) =>
      matchesEvent(row) &&
      matchesFleet(row) &&
      matchesPowerPlant(row) &&
      matchesStation(row)
    );

    const userAddedAcns = this.maintenanceEventListService.getUserAddedEventListFromStorage();
    if (userAddedAcns?.length) {
      userAddedAcns.forEach((userAddedAcn: UserAddedAcn) => {
        if (this.filteredEventListTableData.some(row => (row.acn === userAddedAcn.acn && JSON.stringify(row.eventID) === userAddedAcn.eventId && row.type === userAddedAcn.eventType))) return;
        const match = this.rowData.find(row => (row.acn === userAddedAcn.acn && JSON.stringify(row.eventID) === userAddedAcn.eventId && row.type === userAddedAcn.eventType));
        match ? match.isUserAddedAcn = true : null;
        match
          ? this.filteredEventListTableData.push(match)
          : this.maintenanceEventListService.removeUserAddedEventListFromStorage(userAddedAcn);
      });
    }

    // Update grid with new data
    if (this.gridApi) {
      this.gridApi.setGridOption('rowData', this.filteredEventListTableData);
      this.gridApi.refreshCells();
    }

    setTimeout(() => {
      this.reorderRowData();
      this.ngZone.run(() => {
        this.cdRef.detectChanges(); // Trigger change detection
      });
    }, 0);
  }

  calculateGridHeight(): void {
    const headerHeight = 60; // Adjust based on your header size
    const footerHeight = 60; // Adjust based on your footer size
    const padding = 20; // Optional padding
    const menulistHeight = this.isCardVisible ? 0 : -113;

    this.ngZone.run(() => {
      this.gridHeight = window.innerHeight - headerHeight - footerHeight - padding - 49 - menulistHeight;
      setTimeout(() => this.cdRef.detectChanges(), 0);
    });
  }

  openActionsMenu(event: MouseEvent) {
    // Prevent the default menu opening behavior
    event.preventDefault();

    // Define computed properties for the disabled states
    this.isChangeStatusEticDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? true : false;
    this.isConvertEventDisabled = (this.selectedRow?.status == 'TRK' || this.selectedRow?.status == 'DOA' || this.selectedRow?.status == null) ? false : true;
    this.isCloseEventDisabled = (this.selectedRow?.status == 'UP' || this.selectedRow?.status == 'DOA') ? true : false;
    this.isCancelEventDisabled = (this.selectedRow?.status == 'UP') ? true : false;
    this.isReviewAlertDiabled = (this.selectedRow?.requestStatus == "C") ? false : true;

    // Open the menu programmatically
    this.actionsMenuTrigger.openMenu();

    // Wait for the menu to render, then position it
    setTimeout(() => {
      const overlay = document.querySelector('.cdk-overlay-pane') as HTMLElement;
      if (overlay) {
        // Calculate the center of the screen
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const menuWidth = overlay.offsetWidth;
        const menuHeight = overlay.offsetHeight;

        const left = 70;

        // Position the menu overlay
        overlay.style.position = 'fixed';
        overlay.style.right = `${left}px`;
        overlay.style.transform = 'none'; // Reset Angular Material's transform
      }
    }, 0);
  }

  displayMaintenanceEventList() {
    this.maintenanceEventListService.getMaintenanceEventList().subscribe({
      next: (response: MaintenanceEventListResponseDao[]) => {
        response.forEach((row: any) => {
          row.eticDateTime = row.eticDateTime == null ? "" : row.eticDateTime == "null" ? "" : row.eticDateTime;
          row.flightDepartureDetails == null ? row.flightDepartureDetails = "" : null;
        });
        this.ngZone.run(() => {
          this.isListResponseReceived = true;
          response = this.checkEticDateTime(response);
          this.rowData = response;
          this.setColumnDefs(this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage()?.sideNavClosed || false);
          this.filterTableData();
          this.resizeGridColumns();
          this.onColumnReorder(null);
          this.maintenanceEventListService.updateListDataToMainScreen(response);
          this.maintenanceEventListService.updatedSelectedEventListOptions(true);
          this.startEticDateTimeValidation();
          this.cdRef.detectChanges();
        });
      }, error: (error) => {
        this.isListResponseReceived = false;
        console.error('Error fetching maintenance event list:', error);
      }
    });
  }

  checkEticDateTime(rowData: MaintenanceEventListResponseDao[]): MaintenanceEventListResponseDao[] {
    rowData.forEach((row: any) => {

      if (row?.eticDateTime != null && row?.eticDateTime != "null" && row?.eticDateTime.includes("/")) {
        const [time, dateStr] = row.eticDateTime.split('/');
    
        // Extracting time
        const hours = parseInt(time.substring(0, 2), 10);
        const minutes = parseInt(time.substring(2, 4), 10);
        
        // Extracting date
        const month = parseInt(dateStr.substring(0, 2), 10);
        const day = parseInt(dateStr.substring(2, 4), 10);
        const year = parseInt(dateStr.substring(4, 6), 10);
    
        const eticDate = new Date(year+2000, month-1, day, hours, minutes, 0);

        // Manually getting zulu time
        const zuluDateTimeString = new Date().toISOString();
        const zuluyear = parseInt(zuluDateTimeString.substring(0, 4), 10); 
        const zulumonth = parseInt(zuluDateTimeString.substring(5, 7), 10) - 1;
        const zuluday = parseInt(zuluDateTimeString.substring(8, 10), 10);
        const zuluhours = parseInt(zuluDateTimeString.substring(11, 13), 10);
        const zuluminutes = parseInt(zuluDateTimeString.substring(14, 16), 10);
        
        const zuluDate = new Date(zuluyear, zulumonth, zuluday, zuluhours, zuluminutes, 0);
        
        const timeDifference = (eticDate.getTime() - zuluDate.getTime()) / (1000 * 60); 
    
        row.iseticDateTimeInNext30Min = timeDifference > 0 && timeDifference <= 30;
        row.iseticDateTimeInPast = timeDifference < 0;
      } else {
        row.iseticDateTimeInNext30Min = row.iseticDateTimeInPast = false;
      }
    });
    
    return rowData;
  }
  
  reorderRowData() {
    if (this.gridApi) {
      // Get current row data
      let rowData = [...this.filteredEventListTableData];

      // Reorder: Move rows with requestStatus === "C" to the top
      rowData.sort((a, b) => {
        return a.requestStatus === "C" ? -1 : b.requestStatus === "C" ? 1 : 0;
      });

      // Update filteredEventListTableData and grid
      this.filteredEventListTableData = rowData;
      this.gridApi.setGridOption('rowData', this.filteredEventListTableData);
      this.gridApi.refreshCells();
      this.ngZone.run(() => {
        this.cdRef.detectChanges();
      });
    }
  }

  getRegionsList(isLoadFromStorage: boolean) {
    this.maintenanceEventListService.getRegionsList().subscribe({
      next: (response) => {
        this.filteredRegions = this.regions = response;
        this.getUserPreferences((result) => {
          if (result) {
            result.length > 0 ? this.selectedRegion = result[0] != "" ? result[0] : null : this.selectedRegion = null;
            this.regionControl.setValue(this.selectedRegion);
            this.regionControl.patchValue(this.selectedRegion);
            this.getStationsListFromRegion(isLoadFromStorage);
          } else {
            this.selectedRegion = null;
            this.regionControl.setValue(this.selectedRegion);
            this.regionControl.patchValue(this.selectedRegion);
          }
          this.cdRef.detectChanges();
        }, "Region");
        this.getUserPreferences((result) => {
          if (result) {
            result.length > 0 ? this.selectedstation = result[0] != "" ? result[0] : null : this.selectedstation = null;
            this.stationControl.setValue(this.selectedstation);
            this.stationControl.patchValue(this.selectedstation);
          } else {
            this.selectedstation = null;
          }
        }, "Station");
        this.cdRef.detectChanges();
      }, error: (error) => {
        console.error('Error fetching maintenance event list:', error);
      }
    })
  }

  onAddAcn(acnInput: string) {
    let notFoundAcns: string[] = [];
    let foundAcns: string[] = [];
    let addedAcns: string[] = [];
    acnInput.split(',').forEach((acn: string) => {
      const foundExistingAcninRowData = this.filteredEventListTableData.findIndex(row => row.acn === acn);
      if (foundExistingAcninRowData === -1) {
        notFoundAcns.push(acn);
      } else {
        foundAcns.push(acn);
      }
    });
    notFoundAcns.sort((a, b) => a.localeCompare(b));
    foundAcns.sort((a, b) => a.localeCompare(b));
    if (notFoundAcns.length > 0) {
      this.maintenanceEventListService.getMaintenanceEventList().subscribe({
        next: (response: MaintenanceEventListResponseDao[]) => {
          response = this.checkEticDateTime(response);
          this.rowData = response;
          notFoundAcns.forEach((acn: string) => {
            const foundAcnIndex = response.findIndex(row => row.acn === acn);
            if (foundAcnIndex != -1) {
              addedAcns.push(acn);
              this.maintenanceEventListService.setUserAddedEventListAcnDatainStorage({acn:acn, eventType: response[foundAcnIndex].type, eventId: response[foundAcnIndex].eventID.toString()});
              this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${acn}`, eventId: response[foundAcnIndex].eventID.toString(), eventType: response[foundAcnIndex].type, selected: false, userAdded: true });
              const newRow = response[foundAcnIndex];
              newRow.isUserAddedAcn = true;
              this.filteredEventListTableData = [...this.filteredEventListTableData, newRow];
              this.gridApi.redrawRows();
              this.gridApi.refreshCells();
              this.cdRef.detectChanges();
              this.mainService.refreshDetailsAcnTabData(true);
              this.startEticDateTimeValidation();
            } else {
              this.toastrMessageService.warning(`ACN ${acn} doesn't have any active event.`);
              this.mainService.deleteUserAddedEventListAcnInStorage(acn, response[foundAcnIndex].eventID.toString(), response[foundAcnIndex].type);
              this.mainService.refreshDetailsAcnTabData(true);
            }
          });
          if (addedAcns.length > 0) {
            this.toastrMessageService.success(`ACN(s) ${addedAcns.join(', ')} added successfully`);
          }
          if (foundAcns.length > 0) {
            this.toastrMessageService.warning(`ACN(s) ${foundAcns.join(', ')} already exists in the table`);
          }
        }
      });
    } else if (foundAcns.length > 0) {
      this.toastrMessageService.warning(`ACN(s) ${foundAcns.join(', ')} already exists in the table`);
    }
    this.mainService.refreshDetailsAcnTabData(true);
    this.acnInput = "";
  }

  // Allow only digits (0–9)
  allowOnlyNumbers(event: KeyboardEvent): boolean {
    const charCode = event.which ? event.which : event.keyCode;

    // Allow digits (0–9) and comma (,)
    if ((charCode >= 48 && charCode <= 57) || charCode === 44) {
      return true;
    }

    event.preventDefault();
    return false;
  }

  openHideOptionsDialog() {
    const dialogRef = this.dialog.open(HideButtonComponent, {
      width: '400px',
      data: { options: this.columnDefs.filter(fieldval => fieldval.field !== "settings").map(columnId => columnId.headerComponentParams.displayName) }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.applyColumnReorder(result);
      }
    });
  }

  applyColumnReorder(result: any) {
    if (result != null) {
      this.maintenanceEventListService.saveMaintenanceListSettingsOptionsInStorage(result);
      // Get current column state
      const columnState = this.gridApi.getColumnState();

      // Create updated column state based on result
      const updatedColumnState = result
        .map((column: any) => {
          let columnDef = columnState.find((col: any) => col.colId.toLowerCase() === column.displayName.toLowerCase());

          if (columnDef) {
            return { ...columnDef, hide: !column.selected }; // Copy and update
          }
          return null; // Handle missing columns safely
        })
        .filter((col: any) => col !== null); // Remove null values

      // Apply updated column state
      if (updatedColumnState.length > 0) {
        this.gridApi.applyColumnState({ state: updatedColumnState, applyOrder: true });
        this.maintenanceEventListService.saveEventListTableColumnsInStorage(this.gridApi.getColumnState());
      }
    }
  }

  openMaintenanceEventDetails() {
    const selectedRowData = this.gridApi.getSelectedRows()[0];
    this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: selectedRowData.acn } });
  }

  onSortChanged() {
    setTimeout(() => { this.reorderRowData(); }, 100);
    this.cdRef.detectChanges();
  }

  loadAddEvent() {
    this.router.navigate(['add-event']);
  }

  onReset() {

  }

  getEventListTableColumnName(name: string): string {
    let displayName = "";
    switch (name) {
      case "acn":
        displayName = "ACN";
        break;
      case "fleetDesc":
        displayName = "Fleet";
        break;
      case "station":
        displayName = "Sta";
        break;
      case "status":
        displayName = "Status";
        break;
      case "owner":
        displayName = "Owner";
        break;
      case "flightDetails":
        displayName = "Flt Out/Dt";
        break;
      case "curComment":
        displayName = "Comment";
        break;
      case "startDateTime":
        displayName = "Start";
        break;
      case "flightDepartureDetails":
        displayName = "Dept";
        break;
      case "eticDateTime":
        displayName = "ETIC";
        break;
      case "ost":
        displayName = "OST";
        break;
      case "durationData":
        displayName = "Duration";
        break;
      case "alerts":
        displayName = "Alerts";
        break;
    }
    return displayName;
  }

  isAllSelected(): boolean {
    const selected: string[] = (this.eventsControl.value ?? []).filter((v: string | null | undefined): v is string => v !== undefined && v !== null);
    return selected.length === this.events.length;
  }

  isSomeSelected(): boolean {
    const selected: string[] = (this.eventsControl.value ?? []).filter((v: string | null | undefined): v is string => v !== undefined && v !== null);
    return selected.length > 0 && selected.length < this.events.length;
  }

  toggleSelectAll(event: MouseEvent): void {
    event.stopPropagation();

    const currentSelection = (this.eventsControl.value ?? []).filter((v: string | null | undefined): v is string => v !== null && v !== undefined);
    if (currentSelection.length === this.events.length) {
      this.eventsControl.setValue([]);
      this.selectedEvents = [];
    } else {
      this.eventsControl.setValue([...this.events]);
      this.selectedEvents = [...this.events];
    }
    this.filterTableData();
  }

  onEventsSelectionChange(): void {
    this.updateEventsSelectAllState();
    this.filterTableData();
  }

  onFleetsSelectionChange(): void {
    this.updateFleetsSelectAllState();
    this.filterTableData();
  }

  updateEventsSelectAllState(): void {
    const selected: string[] = (this.eventsControl.value ?? []).filter((v: string | null | undefined): v is string => v !== undefined && v !== null);
    if (selected.length === this.events.length) {
      this.eventSelectAllState = 'checked';
    } else if (selected.length === 0) {
      this.eventSelectAllState = 'unchecked';
    } else {
      this.eventSelectAllState = 'indeterminate';
    }
  }

  updateFleetsSelectAllState(): void {
    const selected: string[] = (this.fleetControl.value ?? []).filter((v: string | null | undefined): v is string => v !== undefined && v !== null);
    if (selected.length === this.fleets.length) {
      this.fleetSelectAllState = 'checked';
    } else if (selected.length === 0) {
      this.fleetSelectAllState = 'unchecked';
    } else {
      this.fleetSelectAllState = 'indeterminate';
    }
  }

  // Check if all fleets selected
  isFleetAllSelected(): boolean {
    const selected = (this.fleetControl.value ?? []).filter((v: string) => !!v);
    return selected.length === this.fleets.length && this.fleets.length > 0;
  }

  // Check if some fleets selected but not all
  isFleetSomeSelected(): boolean {
    const selected = (this.fleetControl.value ?? []).filter((v: string) => !!v);
    return selected.length > 0 && selected.length < this.fleets.length;
  }

  // Toggle select all fleets
  toggleFleetSelectAll(event: MouseEvent): void {
    event.stopPropagation();
    const selected = (this.fleetControl.value ?? []).filter((v: string) => !!v);

    if (selected.length === this.fleets.length) {
      this.selectedFleets = [];
      this.fleetControl.setValue([]);
    } else {
      this.selectedFleets = [...this.fleets];
      this.fleetControl.setValue([...this.fleets]);
    }

    this.filterTableData();
  }

  handleDeleteUserAddedAcn(event: any) {
    const acn = event.acn;
    const eventId = event.eventId;
    const eventType = event.eventType;

    const foundRowIndex = this.filteredEventListTableData.findIndex(row =>
      row.acn.trim() === acn.trim() &&
      row.type.trim() === eventType.trim() &&
      JSON.stringify(row.eventID).trim() === eventId.trim()
    );

    if (foundRowIndex !== -1) {
      this.filteredEventListTableData.splice(foundRowIndex, 1);

      this.maintenanceEventListService.removeUserAddedEventListFromStorage({ acn, eventId, eventType });
      this.mainService.refreshDetailsAcnTabData(true);

      this.filteredEventListTableData = [...this.filteredEventListTableData];
      setTimeout(() => this.cdRef.detectChanges(), 0);
    }
  }

  startEticDateTimeValidation(): void {
    const now = new Date();
    const msUntilNextUtcMinute = 60000 - (now.getUTCSeconds() * 1000 + now.getUTCMilliseconds());

    setTimeout(() => {
      this.updateEticDateTimeValidation();
      this.timeInterval = setInterval(() => {
        this.updateEticDateTimeValidation();
      }, 60000);
    }, msUntilNextUtcMinute);
  }

  updateEticDateTimeValidation() {
    if (this.filteredEventListTableData.length > 0) {
      this.checkEticDateTime(this.filteredEventListTableData);
      this.gridApi.setGridOption('rowData', this.filteredEventListTableData);
      this.gridApi.refreshCells();
      this.ngZone.run(() => {
        this.cdRef.detectChanges();
      });
    }
  }

}