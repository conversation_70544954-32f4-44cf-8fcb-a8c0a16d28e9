import { Component, Inject } from '@angular/core';
import { UserIntakeForm } from '../../../dto/UserIntakeFormDto';
import { UserRole } from '../../../dao/userRoleDao';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { EventType } from '../../../dao/eventTypeDao';
import { MaintenanceEventListService } from '../../../services/maintenance-event-list.service';

@Component({
  selector: 'app-user-intake-form-dialog',
  standalone: false,
  templateUrl: './user-intake-form-dialog.component.html',
  styleUrl: './user-intake-form-dialog.component.scss'
})
export class UserIntakeFormDialogComponent {

  userRoles: Array<UserRole> = [];
  eventTypes: Array<EventType> = [];
  roleList: Array<string> = [];
  eventList: Array<string> = [];
  filteredEvents: Array<string> = [];
  filteredRoles: Array<string> = [];
  role: string = '';
  event: string = '';
  dssCode = '';
  formName = '';
  userRoleMap: Map<string, number> = new Map();
  eventTypeMap: Map<string, number> = new Map();
  userIntakeForm: UserIntakeForm = new UserIntakeForm();
  showAddOption: boolean = false;
  showAddOptionForRole: boolean = false;
  newEventValue: string = '';
  newRoleValue: string = '';
  submitButtonDisabled: boolean = true;
  fleets: any[] = [];
  fleet: string = '';
  filteredFleets: any[] = [];

  constructor(
    public dialogRef: MatDialogRef<UserIntakeFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { userRoleEventType: any },
    private maintenanceEventListService: MaintenanceEventListService
  ) { }

  ngOnInit() {
    this.fetchUserRoleAndEventType();
    this.fetchFleetvalues();
  }

  fetchUserRoleAndEventType() {
    this.data.userRoleEventType.userRoleList.forEach((role: UserRole) => {
      this.userRoleMap.set(role.roleName, role.roleId);
      this.roleList.push(role.roleName);
      // this.userRoles.push(new UserRole(role));
    });
    this.data.userRoleEventType.eventTypeList.forEach((event: EventType) => {
      this.eventTypeMap.set(event.eventType, event.eventId);
      this.eventList.push(event.eventType);
      // this.eventTypes.push(new EventType(event));
    });
    this.filteredEvents = [...this.eventList];
    this.filteredRoles = [...this.roleList];
  }

  fetchFleetvalues() {
    this.maintenanceEventListService.getFeeltValues().subscribe({
      next: (data: any[]) => {
        this.fleets = data;
        this.filteredFleets = [...this.fleets];
        console.log('Fleet values fetched:', this.fleets);
      },
      error: (err) => {
        console.error('Error fetching fleets:', err);
      }
    });
  }

  checkFormValidity() {
    this.submitButtonDisabled = !(
      this.formName.trim() &&
      this.role.trim() &&
      this.event.trim()
    );
  }

  onInputChange(event: Event) {
    const inputValue = (event.target as HTMLInputElement).value;
    this.newEventValue = inputValue;

    this.filteredEvents = this.eventList.filter(e =>
      e.toLowerCase().includes(inputValue.toLowerCase())
    );

    this.showAddOption = inputValue.length >= 3 && !this.eventList.some(e =>
      e.toLowerCase() === inputValue.toLowerCase()
    );

  }

  onInputChangeForRole(event: Event) {
    const roleValue = (event.target as HTMLInputElement).value;
    this.newRoleValue = roleValue;

    this.filteredRoles = this.roleList.filter(r =>
      r.toLowerCase().includes(roleValue.toLowerCase()));

    this.showAddOptionForRole = roleValue.length >= 3 && !this.roleList.some(r =>
      r.toLowerCase() === roleValue.toLowerCase()
    );
  }

  onInputChangeForFleet(event: Event) {
    const fleetValue = (event.target as HTMLInputElement).value;
    this.filteredFleets = this.fleets.filter(r =>
      r.toLowerCase().includes(fleetValue.toLowerCase()));
  }

  onRoleChange(selectedValue: string) {
    this.role = selectedValue;
    this.checkFormValidity();
  }

  onEventChange(selectedValue: string) {
    this.event = selectedValue
    this.checkFormValidity();;
  }

  onFleetChange(selectedValue: string) {
    this.fleet = selectedValue;
    this.checkFormValidity();
  }

  addNewEvent() {
    if (this.newEventValue && !this.eventList.includes(this.newEventValue)) {
      this.eventList.push(this.newEventValue);
      this.filteredEvents = [...this.eventList];
      const newEventId = this.eventTypes.length + 1;
      this.eventTypeMap.set(this.newEventValue, newEventId);
      this.eventTypes.push(new EventType({ eventType: this.newEventValue, eventId: newEventId }));

      this.event = this.newEventValue;
      this.showAddOption = false;
      console.log('New event added:', this.newEventValue);
    }
  }

  addNewRole() {
    if (this.newRoleValue && !this.roleList.includes(this.newRoleValue)) {
      this.roleList.push(this.newRoleValue);
      this.filteredRoles = [...this.roleList];
      const newRoleId = this.userRoles.length + 1;
      this.userRoleMap.set(this.newRoleValue, newRoleId);
      this.userRoles.push(new UserRole({ roleName: this.newRoleValue, roleId: newRoleId }));

      this.role = this.newRoleValue;
      this.showAddOptionForRole = false;
      console.log('New role added:', this.newRoleValue);
    }
  }


  onFormNameChange() {
    this.checkFormValidity();
    console.log(`${this.formName} on Dss Code Change method`);
  }

  onCancel() {
    this.dialogRef.close();
  }

  onSubmit() {
    this.userIntakeForm.roleId = this.userRoleMap.get(this.role) ?? 0;
    this.userIntakeForm.eventId = this.eventTypeMap.get(this.event) ?? 0;
    //Actually this is not dss code, we are storing fleet code
    this.userIntakeForm.dssAuthCode = this.fleet;
    this.userIntakeForm.intakeFormNm = this.formName;
    console.log('Form submitted');
    this.dialogRef.close(this.userIntakeForm);
  }

}
