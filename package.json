{"name": "mets-server-nextgen-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.5", "@angular/cdk": "^19.2.5", "@angular/common": "^19.2.5", "@angular/compiler": "^19.2.5", "@angular/core": "^19.2.5", "@angular/forms": "^19.2.5", "@angular/material": "^19.2.5", "@angular/platform-browser": "^19.2.5", "@angular/platform-browser-dynamic": "^19.2.5", "@angular/router": "^19.2.5", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@okta/okta-angular": "^6.5.1", "@okta/okta-auth-js": "^7.11.1", "@popperjs/core": "^2.11.8", "@stomp/stompjs": "^7.1.1", "ag-grid-angular": "^33.2.1", "ag-grid-community": "^33.2.1", "bootstrap": "^5.3.3", "mets-server-nextgen-ui": "file:", "moment": "^2.30.1", "ngx-quill": "^27.0.2", "ngx-toastr": "^19.0.0", "quill": "^2.0.3", "rxjs": "~7.8.0", "sockjs-client": "^1.6.1", "sweetalert2": "^11.22.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.5", "@angular/cli": "^19.2.5", "@angular/compiler-cli": "^19.2.5", "@types/jasmine": "~5.1.0", "@types/sockjs-client": "^1.5.4", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "rollup": "^3.29.4", "typescript": "~5.7.3"}}