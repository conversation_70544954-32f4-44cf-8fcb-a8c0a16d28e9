<div class="dialog-container">
    <div class="dialog-header">MSN Info</div>

    <div class="dialog-content">
        <div class="table-accordion-wrapper">

            <div class="msn-table-wrapper">
                <table mat-table [dataSource]="msnDataSource" class="mat-elevation-z8 mat-table-custom msn-table-custom">
                    <ng-container matColumnDef="MSN">
                        <th mat-header-cell *matHeaderCellDef>MSN</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['msn']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['msn'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="ATA">
                        <th mat-header-cell *matHeaderCellDef>ATA</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['ata']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['ata'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Status Code">
                        <th mat-header-cell *matHeaderCellDef>Status Code</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['statusCode']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['statusCode'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Follow Up">
                        <th mat-header-cell *matHeaderCellDef>Follow Up</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['followUpCode']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['followUpCode'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="MPN">
                        <th mat-header-cell *matHeaderCellDef>MPN</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['manufPartNbr']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['manufPartNbr'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="CPN">
                        <th mat-header-cell *matHeaderCellDef>CPN</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['coPartNbr']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['coPartNbr'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="CPN Qty">
                        <th mat-header-cell *matHeaderCellDef>CPN Qty</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['cpnQty']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['cpnQty'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="CPN Description">
                        <th mat-header-cell *matHeaderCellDef>CPN Description</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['cpnDescription']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['cpnDescription'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Disc Num">
                        <th mat-header-cell *matHeaderCellDef>Disc Num</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['discNum']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['discNum'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Ship To Station">
                        <th mat-header-cell *matHeaderCellDef>Ship To Station</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['shiptoSta']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['shiptoSta'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Ship To Department">
                        <th mat-header-cell *matHeaderCellDef>Ship To Department</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['shiptoDept']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['shiptoDept'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Plan Method Indicator">
                        <th mat-header-cell *matHeaderCellDef>Plan Method Indicator</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['planMetInd']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['planMetInd'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Date/Time Part Need By">
                        <th mat-header-cell *matHeaderCellDef>Date/Time Part Need By</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['datetimePartNeedBy']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['datetimePartNeedBy'] }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Requested By">
                        <th mat-header-cell *matHeaderCellDef>Requested By</th>
                        <td mat-cell *matCellDef="let element" [matTooltip]="element['requestedBy']" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedRow === element}">
                            {{ element['requestedBy'] }}
                        </td>
                    </ng-container>

                    <!-- Settings Column -->
                    <ng-container matColumnDef="ACTIONS">
                        <th mat-header-cell *matHeaderCellDef class="settings-column">
                            <div class="settings-header-wrapper">
                            <button mat-icon-button (click)="openColumnSettingsDialog('MSN_TABLE')" matTooltip="Customize columns">
                                <mat-icon>settings</mat-icon>
                            </button>
                            </div>
                        </th>
                        <td mat-cell *matCellDef="let element"></td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayMsnTableColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayMsnTableColumns;"></tr>

                </table>
            </div>

            <!-- <div class="accordion-container">
                <mat-accordion multi="true">
                    <mat-expansion-panel class="gradient-expansion-panel" #shippingPanel>
                        <mat-expansion-panel-header class="gradient-expansion-header">
                            <mat-panel-title>MSN Shipping Details</mat-panel-title>
                            <mat-icon class="expansion-icon" *ngIf="!shippingPanel?.expanded">expand_more</mat-icon>
                            <mat-icon class="expansion-icon" *ngIf="shippingPanel?.expanded">expand_less</mat-icon>
                        </mat-expansion-panel-header>
                        <ng-container *ngIf="selectedRow; else noShippingData">
                            <table mat-table [dataSource]="shippingDataSource" class="mat-elevation-z8 mat-table-custom">
                                <ng-container matColumnDef="date">
                                    <th mat-header-cell *matHeaderCellDef>Date</th>
                                    <td mat-cell *matCellDef="let element">{{ element.convertedDate }}</td>
                                </ng-container>
                                <ng-container matColumnDef="waybill">
                                    <th mat-header-cell *matHeaderCellDef>Waybill</th>
                                    <td mat-cell *matCellDef="let element">{{ element.waybill }}</td>
                                </ng-container>
                                <ng-container matColumnDef="ict">
                                    <th mat-header-cell *matHeaderCellDef>ICT</th>
                                    <td mat-cell *matCellDef="let element">{{ element.ict }}</td>
                                </ng-container>
                                <ng-container matColumnDef="isn">
                                    <th mat-header-cell *matHeaderCellDef>ISN</th>
                                    <td mat-cell *matCellDef="let element">{{ element.isn }}</td>
                                </ng-container>
                                <ng-container matColumnDef="type">
                                    <th mat-header-cell *matHeaderCellDef>Type</th>
                                    <td mat-cell *matCellDef="let element">{{ element.type }}</td>
                                </ng-container>
                                <ng-container matColumnDef="shippingType">
                                    <th mat-header-cell *matHeaderCellDef>Shipping Type</th>
                                    <td mat-cell *matCellDef="let element">{{ element.shippingType }}</td>
                                </ng-container>
                                <ng-container matColumnDef="location">
                                    <th mat-header-cell *matHeaderCellDef>Location</th>
                                    <td mat-cell *matCellDef="let element">{{ element.location }}</td>
                                </ng-container>
                                <ng-container matColumnDef="quantity">
                                    <th mat-header-cell *matHeaderCellDef>Quantity</th>
                                    <td mat-cell *matCellDef="let element">{{ element.quantity }}</td>
                                </ng-container>
                                <ng-container matColumnDef="airline">
                                    <th mat-header-cell *matHeaderCellDef>Airline</th>
                                    <td mat-cell *matCellDef="let element">{{ element.airline }}</td>
                                </ng-container>
                                <ng-container matColumnDef="flight">
                                    <th mat-header-cell *matHeaderCellDef>Flight</th>
                                    <td mat-cell *matCellDef="let element">{{ element.flight }}</td>
                                </ng-container>
                                <ng-container matColumnDef="etd">
                                    <th mat-header-cell *matHeaderCellDef>ETD</th>
                                    <td mat-cell *matCellDef="let element">{{ element.etd }}</td>
                                </ng-container>
                                <ng-container matColumnDef="eta">
                                    <th mat-header-cell *matHeaderCellDef>ETA</th>
                                    <td mat-cell *matCellDef="let element">{{ element.eta }}</td>
                                </ng-container>
                                <ng-container matColumnDef="foisEta">
                                    <th mat-header-cell *matHeaderCellDef>FOIS ETA</th>
                                    <td mat-cell *matCellDef="let element">{{ element.foisEta }}</td>
                                </ng-container>
                                <ng-container matColumnDef="fromStation">
                                    <th mat-header-cell *matHeaderCellDef>From Station</th>
                                    <td mat-cell *matCellDef="let element">{{ element.fromStation }}</td>
                                </ng-container>
                                <ng-container matColumnDef="fromDept">
                                    <th mat-header-cell *matHeaderCellDef>From Dept</th>
                                    <td mat-cell *matCellDef="let element">{{ element.fromDept }}</td>
                                </ng-container>
                                <ng-container matColumnDef="toStation">
                                    <th mat-header-cell *matHeaderCellDef>To Station</th>
                                    <td mat-cell *matCellDef="let element">{{ element.toStation }}</td>
                                </ng-container>
                                <ng-container matColumnDef="toDept">
                                    <th mat-header-cell *matHeaderCellDef>To Dept</th>
                                    <td mat-cell *matCellDef="let element">{{ element.toDept }}</td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="shippingColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: shippingColumns;"></tr>
                            </table>
                        </ng-container>
                        <ng-template #noShippingData>
                            <div class="no-data-message">
                                Please select any MSN on above table to get Shipping Info
                            </div>
                        </ng-template>
                    </mat-expansion-panel>
                    
                    <mat-expansion-panel class="gradient-expansion-panel" #commentsPanel>
                        <mat-expansion-panel-header class="gradient-expansion-header">
                            <mat-panel-title>Comments</mat-panel-title>
                            <mat-icon class="expansion-icon" *ngIf="!commentsPanel?.expanded">expand_more</mat-icon>
                            <mat-icon class="expansion-icon" *ngIf="commentsPanel?.expanded">expand_less</mat-icon>
                        </mat-expansion-panel-header>
                        <ng-container *ngIf="showComments; else noCommentsData">
                            <table mat-table [dataSource]="commentsDataSource" class="mat-elevation-z8 mat-table-custom custom-comments-table">
                                <ng-container matColumnDef="createDate">
                                    <th mat-header-cell *matHeaderCellDef>Create Date</th>
                                    <td mat-cell *matCellDef="let element">{{ element.date }}</td>
                                </ng-container>
                                <ng-container matColumnDef="initial">
                                    <th mat-header-cell *matHeaderCellDef>Initial</th>
                                    <td mat-cell *matCellDef="let element">{{ element.waybill }}</td>
                                </ng-container>
                                <ng-container matColumnDef="comments">
                                    <th mat-header-cell *matHeaderCellDef>Comments</th>
                                    <td mat-cell *matCellDef="let element">{{ element.ict }}</td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="commentsColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: commentsColumns;"></tr>
                            </table>
                        </ng-container>
                        <ng-template #noCommentsData>
                            <div class="no-data-message">
                                Please select any MSN on above table to get comments Info
                            </div>
                        </ng-template>
                    </mat-expansion-panel>
                </mat-accordion>
            </div> -->

            <!-- Mat Tabs Section -->
            <mat-tab-group class="custom-tab-group" mat-stretch-tabs>
                <mat-tab label="Shipping Info">
                    <div class="tab-content shipping-info-tab">
                        <ng-container *ngIf="selectedRow; else noShippingData">
                            <table mat-table [dataSource]="shippingDataSource" class="mat-elevation-z8 mat-table-custom">

                                <ng-container matColumnDef="DATE">
                                    <th mat-header-cell *matHeaderCellDef>DATE</th>
                                    <td mat-cell *matCellDef="let element">{{ element.convertedDate }}</td>
                                </ng-container>

                                <ng-container matColumnDef="WAYBILL">
                                    <th mat-header-cell *matHeaderCellDef>WAYBILL</th>
                                    <td mat-cell *matCellDef="let element">{{ element.waybill }}</td>
                                </ng-container>

                                <ng-container matColumnDef="ICT">
                                    <th mat-header-cell *matHeaderCellDef>ICT</th>
                                    <td mat-cell *matCellDef="let element">{{ element.ict }}</td>
                                </ng-container>

                                <ng-container matColumnDef="ISN">
                                    <th mat-header-cell *matHeaderCellDef>ISN</th>
                                    <td mat-cell *matCellDef="let element">{{ element.isn }}</td>
                                </ng-container>

                                <ng-container matColumnDef="TYPE">
                                    <th mat-header-cell *matHeaderCellDef>TYPE</th>
                                    <td mat-cell *matCellDef="let element">{{ element.type }}</td>
                                </ng-container>

                                <ng-container matColumnDef="SHIPPING TYPE">
                                    <th mat-header-cell *matHeaderCellDef>SHIPPING TYPE</th>
                                    <td mat-cell *matCellDef="let element">{{ element.shippingType }}</td>
                                </ng-container>

                                <ng-container matColumnDef="LOCATION">
                                    <th mat-header-cell *matHeaderCellDef>LOCATION</th>
                                    <td mat-cell *matCellDef="let element">{{ element.location }}</td>
                                </ng-container>

                                <ng-container matColumnDef="QUANTITY">
                                    <th mat-header-cell *matHeaderCellDef>QUANTITY</th>
                                    <td mat-cell *matCellDef="let element">{{ element.quantity }}</td>
                                </ng-container>

                                <ng-container matColumnDef="AIRLINE">
                                    <th mat-header-cell *matHeaderCellDef>AIRLINE</th>
                                    <td mat-cell *matCellDef="let element">{{ element.airline }}</td>
                                </ng-container>

                                <ng-container matColumnDef="FLIGHT">
                                    <th mat-header-cell *matHeaderCellDef>FLIGHT</th>
                                    <td mat-cell *matCellDef="let element">{{ element.flight }}</td>
                                </ng-container>

                                <ng-container matColumnDef="ETD">
                                    <th mat-header-cell *matHeaderCellDef>ETD</th>
                                    <td mat-cell *matCellDef="let element">{{ element.etd }}</td>
                                </ng-container>

                                <ng-container matColumnDef="ETA">
                                    <th mat-header-cell *matHeaderCellDef>ETA</th>
                                    <td mat-cell *matCellDef="let element">{{ element.eta }}</td>
                                </ng-container>

                                <ng-container matColumnDef="FOIS ETA">
                                    <th mat-header-cell *matHeaderCellDef>FOIS ETA</th>
                                    <td mat-cell *matCellDef="let element">{{ element.foisEta }}</td>
                                </ng-container>

                                <ng-container matColumnDef="FROM STATION">
                                    <th mat-header-cell *matHeaderCellDef>FROM STATION</th>
                                    <td mat-cell *matCellDef="let element">{{ element.fromStation }}</td>
                                </ng-container>

                                <ng-container matColumnDef="FROM DEPT">
                                    <th mat-header-cell *matHeaderCellDef>FROM DEPT</th>
                                    <td mat-cell *matCellDef="let element">{{ element.fromDept }}</td>
                                </ng-container>

                                <ng-container matColumnDef="TO STATION">
                                    <th mat-header-cell *matHeaderCellDef>TO STATION</th>
                                    <td mat-cell *matCellDef="let element">{{ element.toStation }}</td>
                                </ng-container>

                                <ng-container matColumnDef="TO DEPT">
                                    <th mat-header-cell *matHeaderCellDef>TO DEPT</th>
                                    <td mat-cell *matCellDef="let element">{{ element.toDept }}</td>
                                </ng-container>

                                <ng-container matColumnDef="ACTIONS">
                                    <th mat-header-cell *matHeaderCellDef class="settings-column">
                                    <div class="settings-header-wrapper">
                                        <button mat-icon-button (click)="openColumnSettingsDialog('SHIPPING_TABLE')" matTooltip="Customize columns">
                                        <mat-icon>settings</mat-icon>
                                        </button>
                                    </div>
                                    </th>
                                    <td mat-cell *matCellDef="let element"></td>
                                </ng-container>

                                <tr mat-header-row *matHeaderRowDef="displayShippingTableColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayShippingTableColumns;"></tr>
                            </table>

                            <!-- If selectedRow is there but no data, show message below header -->
                            <div class="no-data-message" *ngIf="shippingDataSource.data.length === 0">
                                No shipping information found for selected MSN.
                            </div>
                        </ng-container>
                        <ng-template #noShippingData>
                            <div class="no-data-message">
                                Select an MSN row from the table above to view shipping details.
                            </div>
                        </ng-template>
                    </div>
                </mat-tab>
                <mat-tab label="Comments">
                    <div class="tab-content comments-tab">
                        <ng-container *ngIf="selectedRow ; else noCommentsData">
                            <table mat-table [dataSource]="commentsDataSource" class="mat-elevation-z8 mat-table-custom custom-comments-table">
                                <ng-container matColumnDef="CREATE DATE">
                                    <th mat-header-cell *matHeaderCellDef>Create Date</th>
                                    <td mat-cell *matCellDef="let element">{{ element.date }}</td>
                                </ng-container>
                                <ng-container matColumnDef="INITIAL">
                                    <th mat-header-cell *matHeaderCellDef>Initial</th>
                                    <td mat-cell *matCellDef="let element">{{ element.waybill }}</td>
                                </ng-container>
                                <ng-container matColumnDef="COMMENTS">
                                    <th mat-header-cell *matHeaderCellDef>Comments</th>
                                    <td mat-cell *matCellDef="let element">{{ element.ict }}</td>
                                </ng-container>
                                 <!-- Settings Column -->
                                <ng-container matColumnDef="ACTIONS">
                                    <th mat-header-cell *matHeaderCellDef class="settings-column">
                                        <div class="settings-header-wrapper">
                                        <button mat-icon-button (click)="openColumnSettingsDialog('COMMENTS_TABLE')" matTooltip="Customize columns">
                                            <mat-icon>settings</mat-icon>
                                        </button>
                                        </div>
                                    </th>
                                    <td mat-cell *matCellDef="let element"></td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayCommentsTableColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayCommentsTableColumns;"></tr>
                            </table>
                            <!-- If selectedRow is there but no data, show message below header -->
                            <div class="no-data-message" *ngIf="commentsDataSource.data.length === 0">
                                No comments information found for selected MSN.
                            </div>
                        </ng-container>
                        <ng-template #noCommentsData>
                            <div class="no-data-message">
                                Select an MSN row from the table above to view comment details.
                            </div>
                        </ng-template>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
        <div class="dialog-footer">
            <button mat-raised-button class="closeButton" (click)="onCancel()">Back</button>
        </div>
    </div>
</div>