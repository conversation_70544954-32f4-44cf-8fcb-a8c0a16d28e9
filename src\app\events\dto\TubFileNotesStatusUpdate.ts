export class TubFileNotesStatusUpdateDto {
    mode: string;
    acn: string;
    group_id: string;
    event_id: string;
    access_level: string;
    timer_id: string;
    flag: string;
    request_type: string;
    user_id: string;
    token_id: string;
    discrepancy_filter: string;
    discrepancy_from_date: string;
    discrepancy_to_date: string;
    discrepancy_SPAN: string;
    comment_updated: boolean;
    event_active: boolean;
    tf_notes_data: TfNotesData;
    constructor() {
        this.mode = "NULL";
        this.acn = "NULL";
        this.group_id = "NULL";
        this.event_id = "NULL";
        this.access_level = "NULL";
        this.timer_id = "NULL";
        this.flag = "NULL";
        this.request_type = "NULL";
        this.user_id = "NULL";
        this.token_id = "NULL";
        this.discrepancy_filter = "NULL";
        this.discrepancy_from_date = "NULL";
        this.discrepancy_to_date = "NULL";
        this.discrepancy_SPAN = "NULL";
        this.comment_updated = false;
        this.event_active = false;
        this.tf_notes_data = new TfNotesData();
    }
}

class NiwTimerData {
    eventId: number;
    timerId: string;
    timerStartDate: string;
    timerStopDate: string;
    creationDateTime: string;
    lastUpdatedDateTime: string;

    constructor() {
        this.eventId = 0;
        this.timerId = "NULL";
        this.timerStartDate = "NULL";
        this.timerStopDate = "NULL";
        this.creationDateTime = "NULL";
        this.lastUpdatedDateTime = "NULL";
    }
}

class TfNotesData {
    eventId: number;
    tfDateTime: string | null;
    empNumber: string;
    empName: string;
    empDepartment: string;
    editedFlag: string;
    tfNote: string;
    noteType: number;
    noteId: number;
    changeType: number;

    constructor() {
        this.eventId = 0;
        this.tfDateTime = null;
        this.empNumber = "NULL";
        this.empName = "NULL";
        this.empDepartment = "NULL";
        this.editedFlag = "N";
        this.tfNote = "NULL";
        this.noteType = 0;
        this.noteId = 0;
        this.changeType = 0;
    }
}