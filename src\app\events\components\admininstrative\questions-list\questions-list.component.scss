.questions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.questions-panel {
  width: 90%;
  height: 90%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.25);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.questions-header {
  display: flex;
  flex-direction: row;
  padding-top: 10px;
  padding-bottom: 4px;
  text-align: center;
  font-size: 1.7em;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: #333;
  width: 100%;
  justify-content: center;
  color: white;
  background-color: rgb(175, 146, 146);
}

.questions-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
}

.question-item {
  margin-bottom: 16px;
  /* Reduced margin for smaller spacing */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
}

.question-item mat-card-content {
  padding-bottom: 10px;
}

.answers-list {
  margin-top: 10px;
}

.answers-list mat-list {
  padding: 0;
}

.answers-list mat-list-item {
  padding: 8px 0;
}

.questions-content::-webkit-scrollbar {
  width: 10px;
}

.questions-content::-webkit-scrollbar-track {
  background: transparent;
}

.questions-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
}

.questions-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

::ng-deep .question-item.mat-mdc-card {
  height: 25% !important;
  padding: 10px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-header {
  padding: 0 !important;
  height: 22% !important;
  width: 100%;
}

::ng-deep .question-item.mat-mdc-card {
    box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-header-text {
  width: 100%;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-header .mat-mdc-card-title{
  font-size: 18px !important;
  color: rgb(175, 146, 146);
  display: flex;
  flex-direction: row;
  gap: 1%;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-content {
  padding: 0 !important;
  height: 35% !important;
  display: flex !important;
  flex-direction: row !important;
  gap: 2% !important;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-content .answers-list {
  margin: 0;
  display: flex;
  flex-direction: row;
  gap: 6%;
  align-items: center;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-content .answers-list .mat-list {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-content .answers-list .mat-list .mat-mdc-list-item-content {
  white-space: normal !important;
  overflow-wrap: break-word;
}

::ng-deep .question-item.mat-mdc-card .mat-mdc-card-actions{
  min-height: 0;
  height: 25%;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: end;
  gap: 1%;
}