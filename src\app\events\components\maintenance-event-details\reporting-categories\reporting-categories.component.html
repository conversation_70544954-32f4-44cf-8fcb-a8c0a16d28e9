<div class="apply1Pading" [class.apply1Pading] = "eventValue == ''">
    <!-- <div class="niw-timer-heading" *ngIf = "eventValue == ''">
        <p class="heading-1">Reporting Categories</p>
    </div> -->
    <form #categoriesForm="ngForm">
      <div class="outer-div">
        <div *ngIf = "reportingTypes['Mx_Type']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Mx Type</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap1-container applyAdditionalSpacing">
                <mat-radio-group [(ngModel)]="formModel.Mx_Type" name="Mx_Type" (ngModelChange)="emitFormChanges()">
                  @for (type of reportingTypes['Mx_Type'].values; track type) {
                    <mat-radio-button [value]="type">{{type}}</mat-radio-button>
                  }
                </mat-radio-group>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['DOA_Reasons']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>DOA Reasons</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap1-container applyAdditionalSpacing">
                  <mat-checkbox *ngFor="let type of reportingTypes['DOA_Reasons'].values; let i = index" (ngModelChange)="emitFormChanges()"
                  [value] = "type" [(ngModel)]="formModel.DOA_Reasons[type]" name="DOA_Reasons{{i}}"> {{ type }} </mat-checkbox>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['Initial_Reason']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Initial Reason</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap2-container applyAdditionalSpacing">
                <mat-radio-group [(ngModel)]="formModel.Initial_Reason" name="Initial_Reason" (ngModelChange)="emitFormChanges()">
                  @for (type of reportingTypes['Initial_Reason'].values; track type) {
                    <mat-radio-button [value]="type">{{type}}</mat-radio-button>
                  }
                </mat-radio-group>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['Departure_Prob']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Departure Prob</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box2-container gap3-container applyAdditionalSpacing">
                <mat-radio-group [(ngModel)]="formModel.Departure_Prob" name="Departure_Prob" (ngModelChange)="emitFormChanges()">
                  @for (type of reportingTypes['Departure_Prob'].values; track type) {
                    <mat-radio-button [value]="type">{{type}}</mat-radio-button>
                  }
                </mat-radio-group>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['Delay']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Delay</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap1-container applyAdditionalSpacing">
                <mat-radio-group [(ngModel)]="formModel.Delay" name="Delay" (ngModelChange)="emitFormChanges()">
                  @for (type of reportingTypes['Delay'].values; track type) {
                    <mat-radio-button [value]="type">{{type}}</mat-radio-button>
                  }
                </mat-radio-group>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['ETIC_Driver']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>ETIC Driver</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap4-container applyAdditionalSpacing">
                <mat-radio-group [(ngModel)]="formModel.ETIC_Driver" name="ETIC_Driver" (ngModelChange)="emitFormChanges()">
                  @for (type of reportingTypes['ETIC_Driver'].values; track type) {
                  <mat-radio-button [value]="type">{{type}}</mat-radio-button>
                  }
                </mat-radio-group>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <div *ngIf = "reportingTypes['Miscellaneous']['values'].length > 0" class="category-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Miscellaneous</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="box-container gap2-container applyAdditionalSpacing">
                  <mat-checkbox *ngFor="let type of reportingTypes['Miscellaneous'].values; let i = index" (ngModelChange)="emitFormChanges()"
                  [(ngModel)]="formModel.Miscellaneous[type]" [value] = "type"
                  name="Miscellaneous{{i}}"> {{ type }} </mat-checkbox>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </form>
    <!-- <div style="margin-bottom: 2%;text-align: center;" *ngIf = "eventValue == ''">
      <button mat-raised-button (click) = "saveCategories()" style="background-color: #7f7b7d;color: white;">Save</button>
    </div> -->
</div>