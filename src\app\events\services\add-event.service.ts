import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { RetrievalDto } from '../dto/retrievalDto';
import { EnvironmentService } from '../../app-layout/services/environment.service';
import { SessionStorageKeys } from '../constants/sessionStorageKeys';

@Injectable({
  providedIn: 'root'
})
export class AddEventService {

  private addNewEvent: Subject<boolean> = new Subject<boolean>();
  addNewEvent$ = this.addNewEvent.asObservable();

  constructor(private http: HttpClient, private environmentService: EnvironmentService) { }

  validateAcnData(data: any): Observable<any> {
    return this.http.post<any>(this.environmentService.validateAcnData, data);
  }

  addEvent(data: any): Observable<any> {
    return this.http.post(this.environmentService.addEvent, data);
  }

  getFlightLegDetails(acn: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.get<any>(`${this.environmentService.getFlightDetails}${acn}`, { params });
  }

  getManagerDetails(station: string): Observable<any> {
    return this.http.get<any>(`${this.environmentService.getManagerDetails}${station}`);
  }

  getEmployeeIdFromStorage() {
    const userDetails = this.getItem<{ id: string }>(SessionStorageKeys.LOGGED_USER_DETAILS);
    if (userDetails && userDetails?.id) {
      return parseInt(userDetails.id);
    } else {
      throw new Error('User details not found in session storage or missing id');
    }
  }

  triggerAddNewEvent(): void {
    this.addNewEvent.next(true);
  }

  /** Generic method to set sessionStorage items */
  private setItem(key: string, value: any): void {
    if (value !== null && value !== undefined) {
      sessionStorage.setItem(key, JSON.stringify(value));
    }
  }

  /** Generic method to get sessionStorage items */
  private getItem<T>(key: string): T | null {
    const storedValue = sessionStorage.getItem(key);
    if (!storedValue) return null;
    try {
      return JSON.parse(storedValue) as T;
    } catch (error) {
      console.error(`Error parsing sessionStorage key: ${key}`, error);
      return null;
    }
  }


  getAircraftType(): Observable<any> {
    return this.http.get<any>(`${this.environmentService.getAcnCache}`);
  }

  validateAcnExists(acn: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      this.getAircraftType().subscribe({
        next: (result: any) => {
          if (result && result.ACN_CACHE_DETAIL && result.ACN_CACHE_DETAIL.length > 0) {
            const acnExists = result.ACN_CACHE_DETAIL.some((entry: any) => entry.acn == acn);
            observer.next(acnExists);
            observer.complete();
          } else {
            observer.next(false);
            observer.complete();
          }
        },
        error: (err: any) => {
          console.error('Error validating ACN:', err);
          observer.next(false);
          observer.complete();
        }
      });
    });
  }
}
