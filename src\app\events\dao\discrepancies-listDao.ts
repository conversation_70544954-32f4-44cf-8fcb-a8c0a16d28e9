export interface OpenDiscrepanciesResponseDao {
    discrepancyList: DiscrepanciesList[];
    linkedDiscrepancyList: DiscrepanciesList[];
}

export interface DiscrepanciesList {
    eventId: number;
    link: boolean;
    ata: string;
    number: string;
    discType: string;
    eventType: string | null;
    openDate: string;
    displayOpenDate?: string;
    openStation: string;
    inWork: boolean;
    copyToManagerNotes?: boolean;
    copyToComment?: boolean;
    closed: string | null;
    text: string[];
    message: string | null;
    status: string;
    isModified: boolean;
    isLinkModified: boolean;
    isDowningModified: boolean;
    isDowningItem: boolean;
    downingItem: boolean;
    isDowningItemPreviouslySelected: boolean;
    timeRemaining: string;
}