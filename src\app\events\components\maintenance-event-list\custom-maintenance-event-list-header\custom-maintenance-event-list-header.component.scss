// .ag-header-cell-label {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   width: 100%;
// }

// .ag-header-cell-text {
//   flex-grow: 1;
//   text-align: center;
//   font-weight: bold;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   white-space: nowrap;
//   overflow: hidden;
//   padding-right: 10px; /* Add padding to the right */
// }

// .ag-sort-icon {
//   margin-left: 1px; /* Increase margin to ensure proper spacing */
//   display: inline-flex;
//   align-items: center;
// }

// .ag-filter-icon {
//   position: absolute;
//   left: 5px;
//   margin-left: auto;
//   cursor: pointer;
//   display: flex;
//   align-items: center;
//   color: gray;
// }

// .filter-active {
//   color: #ba7659 !important;
// }

.ag-header-cell-label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  position: relative;
}

.ag-filter-icon {
  display: none; /* Initially hidden */
  cursor: pointer;
  align-items: center;
  color: gray;
  margin-right: 10px;
  margin-top: 3px;
}

/* Show filter icon when hovering */
.ag-header-cell-label:hover .ag-filter-icon {
  display: flex;
}

/* Keep filter icon visible when filter is active */
.filter-active {
  display: flex !important;
  color: #ba7659 !important;
}

/* Default hover state when filter is inactive */
.filter-hover {
  color: gray;
}

.ag-header-cell-text {
  display: flex !important;
}
