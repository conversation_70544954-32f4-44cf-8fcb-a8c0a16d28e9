import { ChangeDetectorRef, Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { RetrievalDto } from '../../../dto/retrievalDto';
import { EventFlightEticResponseDao } from '../../../dao/event-flight-etic-detailsDao';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { MaintenanceEventDetailsSharedService } from '../maintenance-event-details-shared.service';

@Component({
  selector: 'app-flight-etic-info',
  standalone: false,
  templateUrl: './flight-etic-info.component.html',
  styleUrl: './flight-etic-info.component.scss'
})
export class FlightEticInfoComponent implements OnInit{

  animateTitle: boolean = false;

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;

  eventFlightEticDetails: EventFlightEticResponseDao = {} as EventFlightEticResponseDao;

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService, private cdRef: ChangeDetectorRef,
    private maintenanceEventDetailsSharedService: MaintenanceEventDetailsSharedService,
  ) {}

  ngOnInit(): void {
    this.maintenanceEventDetailsService.showTitleEffect$.subscribe((show: boolean) => {
      if (show) {
        this.animateTitle = false; // reset
        setTimeout(() => {
          this.animateTitle = true; // trigger reflow to apply animation
        }, 0);
      } else {
        this.animateTitle = false;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") {
        this.getEventFlightEticDetails(this.detailsViewObj);
      } else {
        this.eventFlightEticDetails = {} as EventFlightEticResponseDao;
      }
    }
  }

  getEventFlightEticDetails(detailsViewObj?: DetailViewResponseDao): void {
    this.maintenanceEventDetailsService.getEventFlightEticDetails(this.detailsViewObj?.eventID).subscribe({
      next: (response: EventFlightEticResponseDao) => {
        this.eventFlightEticDetails = response;
        this.maintenanceEventDetailsSharedService.setAffectedFlightDetails(this.eventFlightEticDetails.flightLegNumber, this.eventFlightEticDetails.flightDate,this.eventFlightEticDetails.flightNumber);
        this.eventFlightEticDetails.initialEtic = this.formatDate(this.eventFlightEticDetails.initialEtic);
        this.eventFlightEticDetails.arrival = this.formatDate(this.eventFlightEticDetails.arrival);
        this.eventFlightEticDetails.departure = this.formatDate(this.eventFlightEticDetails.departure);
        this.eventFlightEticDetails.scheduledDeparture = this.formatDate(this.eventFlightEticDetails.scheduledDeparture);
        this.eventFlightEticDetails.actualDeparture = this.formatDate(this.eventFlightEticDetails.actualDeparture);
        this.eventFlightEticDetails.flightDate = this.formatDate(this.eventFlightEticDetails.flightDate);
        this.cdRef.detectChanges();
      },
      error: (error: any) => {
      }
    });
  }

  formatDate(dateString: string): string {
    if (dateString == null || dateString == "null") {
      return '';
    }
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    
    const dateObj = new Date(dateString);
    const day = dateObj.getDate().toString().padStart(2, '0'); // Ensures two-digit format
    const month = months[dateObj.getMonth()]; // Gets month abbreviation
    const year = dateObj.getFullYear().toString().slice(-2); // Gets last two digits of the year
    const hours = dateObj.getHours().toString().padStart(2, '0'); // Ensures two-digit hours
    const minutes = dateObj.getMinutes().toString().padStart(2, '0'); // Ensures two-digit minutes
  
    return `${day}${month}${year} ${hours}:${minutes}`;
  }

}
