import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MaintenanceEventListService } from '../../../../services/maintenance-event-list.service';
import { RequestDAODto } from '../../../../dto/requestDOAForm';
import { ResponseDOAForm } from '../../../../dto/ResponseDOAForm';
import { updateDOAFormDto } from '../../../../dto/updateDOAFormRequest';
import { SessionStorageKeys } from '../../../../constants/sessionStorageKeys';
import { ToastrMessageService } from '../../../../../app-layout/services/toastr-message.service';

@Component({
  selector: 'app-doa-form',
  standalone: false,
  templateUrl: './doa-form.component.html',
  styleUrl: './doa-form.component.scss'
})
export class DoaFormComponent implements OnInit{

  constructor(private route: ActivatedRoute, 
              private location: Location,
              private service: MaintenanceEventListService,
              private toastrMessageService: ToastrMessageService
            ) { }

  selectedRowData!: any;
  ACN!: string;
  status!: string;
  reason!: string;
  duration!: string;
  etic!: string;
  start!: string;
  owner!: string;
  OST!: string;
  station!: string;
  comments!: string;
  etic_comments!: string;
  msns!: string;
  responsible_manager!: string;
  contact!: string;
  eventID!: string;

  DOA_Flt_No: string | null | undefined;
  DOA_Flt_Date!: string | null | undefined;
  DOA_Flt_Leg!: string | null | undefined;
  DOA_Flt_Dest!: string | null | undefined;
  DOA_Flt_ETA!: string | null | undefined;

  eventCreatedBy!: string;
  eventCreatedAt!: string;
  eventClosedBy!: string;
  eventClosedAt!: string;
  eventLastUpdated!: string;

  Additional_description!: string;

  doaFormResponse!: ResponseDOAForm;

  checkFlightReq!: boolean;

  MxCrew!: boolean;


  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.ACN = params['ACN'];
    });
    this.getDaoDetails();
  }

  getDaoDetails(){
    this.service.getDOAformDetails(parseInt(this.ACN)).subscribe(response => {
      this.doaFormResponse = response;
      this.DOA_Flt_No = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.doaFlightLegNumber;
      this.DOA_Flt_Date = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.doaFlightDate;
      this.DOA_Flt_Dest = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventStation;
      this.DOA_Flt_ETA = "";//not in response
      this.DOA_Flt_Leg = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.doaFlightLegNumber;

      this.status = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventStatus;
      this.reason = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventEticReasonCd;
      this.duration = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.duration;
      this.etic = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventEticDateTime;
      this.start = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.startDateTime;
      this.owner = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.contactInfoOwnerList.join(', ');
      this.OST = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventOST;
      this.comments = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventCurrentComment;
      this.etic_comments = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventEticReasonComment;
      this.msns = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.numberOfMSN;
      this.responsible_manager = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.memDeskContact ?? '';
      this.contact = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.contact ?? '';
      this.eventLastUpdated = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventLastUpdateDateTime;
      this.eventCreatedAt = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventCreatedDateTime;
      this.eventCreatedBy = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventCreatedBy;
      this.eventClosedAt = "null";//not in response
      this.eventClosedBy = "null";//not in response
    });
  }

  goBack() {
    this.location.back();
  }

  prepareDetails(){
    const UpdateRequest = new updateDOAFormDto();
    UpdateRequest.mode = "EVENT_UPDATE";
    UpdateRequest.detail_view_data.doaData.eventId = Number(this.eventID);
    UpdateRequest.detail_view_data.doaData.doaOriginator = "John Doe";// not available in response of DOA retrival
    UpdateRequest.detail_view_data.doaData.createdAt = "2021-08-20T10:00:00";// not available in response of DOA retrival
    UpdateRequest.detail_view_data.doaData.checkFlightRequrired = true;// not available in response of DOA retrival
    UpdateRequest.detail_view_data.doaData.comment = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.eventCurrentComment;
    UpdateRequest.detail_view_data.doaData.flightNumber = this.DOA_Flt_No ?? '';
    UpdateRequest.detail_view_data.doaData.flightDate = this.DOA_Flt_Date ?? '';
    UpdateRequest.detail_view_data.doaData.flightLegNumber = this.DOA_Flt_Leg ?? '';
    UpdateRequest.detail_view_data.doaData.destination = this.DOA_Flt_Dest ?? '';
    UpdateRequest.detail_view_data.doaData.estimatedTimeOfArrival = this.DOA_Flt_ETA ?? '';
    UpdateRequest.detail_view_data.doaData.additionalDescription = this.Additional_description;
    UpdateRequest.detail_view_data.doaData.closedBy = "Alice Johnson";// not available in response of DOA retrival
    UpdateRequest.detail_view_data.doaData.closedAt = "2021-08-20T10:00:00";// not available in response of DOA retrival
    UpdateRequest.detail_view_data.doaData.createdAt = "2025-01-01 12:00:00";// make it current date and time
    UpdateRequest.detail_view_data.doaData.maintenanceCrew = this.MxCrew;
    UpdateRequest.detail_view_data.doaData.lastUpdated = this.eventLastUpdated;
    UpdateRequest.detail_view_data.doaData.discVector = this.doaFormResponse.data.DETAIL_VIEW_OBJECT.linkedDiscList;
    this.service.updateDOAformDetails(UpdateRequest).subscribe(Response => {
      this.toastrMessageService.success("DOA Form updated successfully");
    });
  }

  onClick(){
    this.prepareDetails();
  }
}