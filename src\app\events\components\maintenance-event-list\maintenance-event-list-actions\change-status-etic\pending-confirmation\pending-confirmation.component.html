<!-- Dialog Header -->
<div class="dialog-header">
  <span>Change Status / ETIC Event Change Action</span>
</div>
<mat-dialog-content class="dialog-container">
  <!-- Dialog Content -->
  <div class="dialog-content">
    <!-- Message -->
    <div class="message-section">
      <div class="message-badge">
        <mat-icon class="badge-icon">warning</mat-icon>
        <span class="message-text">
          This event has a pending change request. <strong>Do you wish to modify it?</strong>
        </span>
      </div>
    </div>

    <!-- Data Cards -->
    <div class="data-grid">
      <div class="data-row first-row">
        <div class="data-item">
          <span class="label">ACN:</span>
          <span class="value">{{ data.selectedRow?.acn || '-' }}</span>
        </div>
        <div class="data-item">
          <span class="label">Fleet:</span>
          <span class="value">{{ data.selectedRow?.fleetDesc || '-' }}</span>
        </div>
        <div class="data-item">
          <span class="label">Station:</span>
          <span class="value">{{ data.selectedRow?.station || '-' }}</span>
        </div>
      </div>
      <div class="data-row second-row">
        <div class="data-item">
          <span class="label">Current Status:</span>
          <span class="value">{{ data.selectedRow?.status || '-' }}</span>
        </div>
        <div class="data-item">
          <span class="label">Current ETIC:</span>
          <span class="value">{{ data.selectedRow?.eticDateTime || '-' }}</span>
        </div>
      </div>
      <div class="data-row third-row">
        <div class="data-item">
          <span class="label">New Status:</span>
          <span class="value">{{ data.selectedRow?.newStatus || '-' }}</span>
        </div>
        <div class="data-item">
          <span class="label">New ETIC:</span>
          <span class="value">{{ data.selectedRow?.newEticDateTime || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- Input Fields -->
    <div class="comments-section">
      <div class="comment-wrapper">
        <h3 class="section-title">Current Comment</h3>
        <mat-form-field appearance="outline" class="form-field comment-field">
          <textarea
            matInput
            [(ngModel)]="currentComment"
            name="currentComment"
            readonly
            class="purple-text text_area_style"
          ></textarea>
        </mat-form-field>
      </div>
      <div class="comment-wrapper">
        <h3 class="section-title">New Comment</h3>
        <mat-form-field appearance="outline" class="form-field comment-field">
          <textarea
            matInput
            [(ngModel)]="newComment"
            name="newComment"
            class="purple-text text_area_style"
            readonly
            #newCommentInput="ngModel"
          ></textarea>
        </mat-form-field>
      </div>
      <div class="comment-wrapper">
        <h3 class="section-title">Tub File Note Entry</h3>
        <mat-card class="compact-note-card tf-note-card">
          <div class="note-content">
            <div class="note-text">{{ latestTubFileEntry?.tfNote }}</div>
            <div class="note-meta-content">
              <span class="emp-name">{{ latestTubFileEntry?.empName }}</span>
              <span class="update-time">{{ latestTubFileEntry?.lastUpdateDtTm | date: 'short' }}</span>
            </div>
          </div>
        </mat-card>
        <!-- <mat-form-field appearance="outline" class="form-field comment-field">
          <textarea
            matInput
            [(ngModel)]="tubFileNote"
            name="tubFileNote"
            class="purple-text text_area_style"
            readonly
            #tubFileNoteInput="ngModel"
            required
          ></textarea>
        </mat-form-field> -->
      </div>
    </div>
  </div>
</mat-dialog-content>

<!-- Dialog Actions -->
<div class="dialog-footer">
  <button
    mat-raised-button
    color="primary"
    class="action-button yes-button"
    (click)="onYes()"
  >
    Yes
  </button>
  <button
    mat-raised-button
    color="warn"
    class="action-button no-button"
    (click)="onNo()"
  >
    No
  </button>
</div>