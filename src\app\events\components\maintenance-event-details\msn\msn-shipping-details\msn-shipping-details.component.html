<div class="dialog-content-container">
  <h2 class="dialog-header">MSN Shipping Details</h2>

  <div class="table-wrapper">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 mat-table-custom">

      <!-- Column Definitions -->
      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let element">{{ element.convertedDate }}</td>
      </ng-container>

      <ng-container matColumnDef="waybill">
        <th mat-header-cell *matHeaderCellDef>WayBill</th>
        <td mat-cell *matCellDef="let element">{{ element.waybill }}</td>
      </ng-container>

      <ng-container matColumnDef="ict">
        <th mat-header-cell *matHeaderCellDef>ICT</th>
        <td mat-cell *matCellDef="let element">{{ element.ict }}</td>
      </ng-container>

      <ng-container matColumnDef="isn">
        <th mat-header-cell *matHeaderCellDef>ISN</th>
        <td mat-cell *matCellDef="let element">{{ element.isn }}</td>
      </ng-container>

      <ng-container matColumnDef="type">
        <th mat-header-cell *matHeaderCellDef>TYPE</th>
        <td mat-cell *matCellDef="let element">{{ element.type }}</td>
      </ng-container>

      <ng-container matColumnDef="shippingType">
        <th mat-header-cell *matHeaderCellDef>Shipping Type</th>
        <td mat-cell *matCellDef="let element">{{ element.shippingType }}</td>
      </ng-container>

      <ng-container matColumnDef="location">
        <th mat-header-cell *matHeaderCellDef>Location</th>
        <td mat-cell *matCellDef="let element">{{ element.location }}</td>
      </ng-container>

      <ng-container matColumnDef="quantity">
        <th mat-header-cell *matHeaderCellDef>Quantity</th>
        <td mat-cell *matCellDef="let element">{{ element.quantity }}</td>
      </ng-container>

      <ng-container matColumnDef="airline">
        <th mat-header-cell *matHeaderCellDef>Airline</th>
        <td mat-cell *matCellDef="let element">{{ element.airline }}</td>
      </ng-container>

      <ng-container matColumnDef="flight">
        <th mat-header-cell *matHeaderCellDef>Flight</th>
        <td mat-cell *matCellDef="let element">{{ element.flight }}</td>
      </ng-container>

      <ng-container matColumnDef="etd">
        <th mat-header-cell *matHeaderCellDef>ETD</th>
        <td mat-cell *matCellDef="let element">{{ element.etd }}</td>
      </ng-container>

      <ng-container matColumnDef="eta">
        <th mat-header-cell *matHeaderCellDef>ETA</th>
        <td mat-cell *matCellDef="let element">{{ element.eta }}</td>
      </ng-container>

      <ng-container matColumnDef="foisEta">
        <th mat-header-cell *matHeaderCellDef>FOIS ETA</th>
        <td mat-cell *matCellDef="let element">{{ element.foisEta }}</td>
      </ng-container>

      <ng-container matColumnDef="fromStation">
        <th mat-header-cell *matHeaderCellDef>From Station</th>
        <td mat-cell *matCellDef="let element">{{ element.fromStation }}</td>
      </ng-container>

      <ng-container matColumnDef="fromDept">
        <th mat-header-cell *matHeaderCellDef>From Dept</th>
        <td mat-cell *matCellDef="let element">{{ element.fromDept }}</td>
      </ng-container>

      <ng-container matColumnDef="toStation">
        <th mat-header-cell *matHeaderCellDef>To Station</th>
        <td mat-cell *matCellDef="let element">{{ element.toStation }}</td>
      </ng-container>

      <ng-container matColumnDef="toDept">
        <th mat-header-cell *matHeaderCellDef>To Dept</th>
        <td mat-cell *matCellDef="let element">{{ element.toDept }}</td>
      </ng-container>

      <!-- Table Rows -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <div class="button-container">
    <button mat-raised-button class="closeButton" (click)="back()">Back</button>
  </div>
</div>