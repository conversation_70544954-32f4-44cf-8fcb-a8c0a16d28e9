import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { MatExpansionPanel } from '@angular/material/expansion';
import { CustomMsnTableFilterComponent } from './custom-msn-table-filter/custom-msn-table-filter.component';
import { SessionStorageKeys } from '../../../../constants/sessionStorageKeys';

@Component({
  selector: 'app-msn-details-info',
  standalone: false,
  templateUrl: './msn-details-info.component.html',
  styleUrl: './msn-details-info.component.scss',
})
export class MsnDetailsInfoComponent implements OnInit {

  @ViewChild('shippingPanel') shippingPanel!: MatExpansionPanel;
  @ViewChild('commentsPanel') commentsPanel!: MatExpansionPanel;

    msnColumns = [
    'MSN',
    'ATA',
    'Status Code',
    'Follow Up',
    'MPN',
    'CPN',
    'CPN Qty',
    'CPN Description',
    'Disc Num',
    'Ship To Station',
    'Ship To Department',
    'Plan Method Indicator',
    'Date/Time Part Need By',
    'Requested By',
    'ACTIONS'
  ];

  shippingColumns: string[] = [
    'DATE', 'WAYBILL', 'ICT', 'ISN', 'TYPE', 'SHIPPING TYPE', 'LOCATION',
    'QUANTITY', 'AIRLINE', 'FLIGHT', 'ETD', 'ETA', 'FOIS ETA', 'FROM STATION',
    'FROM DEPT', 'TO STATION', 'TO DEPT', 'ACTIONS'
  ];

  commentsColumns: string[] = ['CREATE DATE', 'INITIAL', 'COMMENTS', 'ACTIONS'];

  selectedRow: any = null;
  msnDataSource = new MatTableDataSource();
  shippingDataSource = new MatTableDataSource();
  commentsDataSource = new MatTableDataSource();

  displayMsnTableColumns: string[] = this.msnColumns;
  displayShippingTableColumns: string[] = this.shippingColumns;
  displayCommentsTableColumns: string[] = this.commentsColumns;

  hiddenMsnColumns: string[] = [];
  hiddenShippingColumns: string[] = [];
  hiddenCommentsColumns: string[] = [];

  constructor(
    public dialogRef: MatDialogRef<MsnDetailsInfoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { msnTableData: any[], selectedPart: any },
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private dialog: MatDialog
  ) {
    this.msnDataSource.data = data.msnTableData || [];
    this.selectedRow = data.selectedPart || null; 
    if (this.selectedRow) {
      this.maintenanceEventDetailsService.getMsnShippingInfo(this.selectedRow.msn).subscribe({
        next: (response: any) => {
          response.shippingInfoTreeData.forEach((shippingInfo: any) => {
            shippingInfo.convertedDate = this.formatTimestamp(shippingInfo.date);
          });
          this.shippingDataSource.data = response.shippingInfoTreeData || [];
        },
        error: (error: any) => {
          console.error('Error fetching shipping details:', error);
        }
      });
    }
  }

  ngOnInit(): void {
      this.displayMsnTableColumns = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_TABLECOLUMNS);
      this.displayShippingTableColumns = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_SHIPPING_TABLECOLUMNS);
      this.displayCommentsTableColumns = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_COMMENTS_TABLECOLUMNS);
      this.displayMsnTableColumns = this.displayMsnTableColumns.length > 0 ? this.displayMsnTableColumns : this.msnColumns;
      this.displayShippingTableColumns = this.displayShippingTableColumns.length > 0 ? this.displayShippingTableColumns : this.shippingColumns;
      this.displayCommentsTableColumns = this.displayCommentsTableColumns.length > 0 ? this.displayCommentsTableColumns : this.commentsColumns;
  }

  onRowClick(row: any) {
    if (!this.selectedRow || this.selectedRow.msn !== row.msn) {
      this.selectedRow = row;
      this.maintenanceEventDetailsService.getMsnShippingInfo(row.msn).subscribe({
        next: (response: any) => {
          response.shippingInfoTreeData.forEach((shippingInfo: any) => {
            shippingInfo.convertedDate = this.formatTimestamp(shippingInfo.date);
          });
          this.shippingDataSource.data = response.shippingInfoTreeData || [];
        },
        error: (error: any) => {
          console.error('Error fetching shipping details:', error);
        }
      });
    } else {
      this.selectedRow = null;
      this.shippingDataSource.data = [];
      this.commentsDataSource.data = [];
    }
  }

  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
  
    const pad = (num: number) => num.toString().padStart(2, '0');
  
    const day = pad(date.getDate());
    const month = pad(date.getMonth() + 1); // Months are 0-based
    const year = date.getFullYear();
  
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
  
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  }

  onCancel() {
    this.dialogRef.close();
  }

  openColumnSettingsDialog(tableName: string) {
    let allMsnTableColumns = JSON.parse(JSON.stringify(this.msnColumns));
    let allShippingTableColumns = JSON.parse(JSON.stringify(this.shippingColumns));
    let allCommentsTableColumns = JSON.parse(JSON.stringify(this.commentsColumns));

    const msnTableColumnsFromStorage = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_TABLECOLUMNS);
    const shippingTableColumnsFromStorage = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_SHIPPING_TABLECOLUMNS);
    const commentsTableColumnsFromStorage = this.maintenanceEventDetailsService.getMsnTableColumnsFromSessionStorage(SessionStorageKeys.MSN_COMMENTS_TABLECOLUMNS);

    this.dialog.open(CustomMsnTableFilterComponent, {
      width: '600px',
      data: {
        tableName: tableName,
        allColumns: tableName === "MSN_TABLE" ? allMsnTableColumns.slice(0, -1) : tableName === "SHIPPING_TABLE" ? allShippingTableColumns.slice(0, -1) : allCommentsTableColumns.slice(0, -1),
        visibleColumns : tableName === "MSN_TABLE" ? msnTableColumnsFromStorage.slice(0, -1) : tableName === "SHIPPING_TABLE" ? shippingTableColumnsFromStorage.slice(0, -1) : commentsTableColumnsFromStorage.slice(0, -1),
      }
    }).afterClosed().subscribe(result => {
      if (result) {
        if (result.tableName === "MSN_TABLE") {
          result.displayColumns.push('ACTIONS'); // Ensure 'actions' is always included
          this.displayMsnTableColumns = result.displayColumns;
          this.hiddenMsnColumns = result.hideColumns;
          this.maintenanceEventDetailsService.saveMsnTableColumnsInSessionStorage(SessionStorageKeys.MSN_TABLECOLUMNS, this.displayMsnTableColumns);
        } else if (result.tableName === "SHIPPING_TABLE") {
          result.displayColumns.push('ACTIONS'); // Ensure 'actions' is always included
          this.displayShippingTableColumns = result.displayColumns;
          this.hiddenShippingColumns = result.hideColumns;
          this.maintenanceEventDetailsService.saveMsnTableColumnsInSessionStorage(SessionStorageKeys.MSN_SHIPPING_TABLECOLUMNS, this.displayShippingTableColumns);
        } else if (result.tableName === "COMMENTS_TABLE") {
          result.displayColumns.push('ACTIONS'); // Ensure 'actions' is always included
          this.displayCommentsTableColumns = result.displayColumns;
          this.hiddenCommentsColumns = result.hideColumns;
          this.maintenanceEventDetailsService.saveMsnTableColumnsInSessionStorage(SessionStorageKeys.MSN_COMMENTS_TABLECOLUMNS, this.displayCommentsTableColumns);
        }
      }
    });
  }
}
