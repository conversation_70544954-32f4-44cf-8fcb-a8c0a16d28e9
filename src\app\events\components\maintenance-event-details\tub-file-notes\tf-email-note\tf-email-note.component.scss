.tf-selection-wrapper {
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  overflow: hidden;
  max-height: none !important; /* override internal height restrictions */
}

mat-dialog-content {
  overflow: hidden !important;
  max-height: none !important;
}

::ng-deep .mat-dialog-container {
  overflow: visible !important;
  max-height: none !important;
  padding: 0 !important;
}

/* Orange button styling */
.orange-button {
  background-color: #ff6600 !important;
  color: white !important;
  border-radius: 20px;
  font-weight: bold;
  padding: 6px 20px;
  transition: background-color 0.3s ease;
}

.orange-button:hover {
  background-color: #ffa366 !important;
  color: black !important;
}
