export interface TimerData {
  timerId: string;
  timerName: string;
  timerDesc: string;
  activeCatg: string; // Consider converting to boolean if 'Y'/'N' is consistent
  systemCatg: string; // Consider converting to boolean if 'Y'/'N' is consistent
  listOrder: number;
  lastUpdateDtTm: number; // This is a Unix timestamp (milliseconds)
}

export interface EventActiveTimerDataList {
  eventTimersPk: EventTimersPk;
  timerStartDtTm: string; // ISO 8601 string with timezone offset
  timerStopDtTm: string | null; // Can be a string (ISO 8601) or null
  timerId: string; // Or number, if always numeric
  lastUpdateDtTm: number; // ISO 8601 string with timezone offset
}

export interface EventTimersPk {
  eventId: number;
  creationDtTm: number; // ISO 8601 string with timezone offset
}

export interface NiwTimerResponse {
  timerDataList: TimerData[];
  eventActiveTimerDataList: EventActiveTimerDataList[]; // You might want to define a specific interface for this if it's not always empty
  eventTimerDataList: any[]; // You might want to define a specific interface for this if it's not always empty
}