<h2 mat-dialog-title class="dialog-title">Add ACN to Event List Table</h2>
<p class="error" *ngIf="error">{{errorMessage}}</p>
<mat-dialog-content>
    <div class="dialog-container">
  
      <div class="section margin-bottom-20">        
        <mat-radio-group [(ngModel)]="selectedAcnOption" (change)="optionChanged()">          
          <mat-radio-button value="singleAcn">ACN 
                <!-- SINGLE ACN -->
                <mat-form-field class="small-input">
                <input matInput
                        type="text"
                        maxlength="5"
                        [disabled]="!isSingleAcnSelected"
                        [(ngModel)]="acnValue"
                        (ngModelChange)="checkApplyButtonState()"
                        placeholder="Enter ACN"
                        (keypress)="allowOnlyDigits($event)"
                        (input)="sanitizeInput('single')" />
                </mat-form-field>
            </mat-radio-button>

            <mat-divider class="full-width-divider"></mat-divider>
          
            <mat-radio-button value="multipleAcn">Multiple ACN's
            <div class="full-width-container">
                <mat-form-field appearance="fill" class="full-width-textarea">
                <textarea matInput
                            [disabled]="!isMultipleAcnSelected"
                            [(ngModel)]="multipleAcnValues"
                            (ngModelChange)="checkApplyButtonState()"
                            placeholder="Enter ACNs separated by commas"
                            matTextareaAutosize
                            matAutosizeMinRows="4"
                            matAutosizeMaxRows="6"></textarea>
                <mat-hint>Provide ACNs separated by commas</mat-hint>
                </mat-form-field>
            </div>
            </mat-radio-button>

        </mat-radio-group>
      </div>
  
    </div>
</mat-dialog-content>

<mat-dialog-actions class="dialog-actions">
    <button mat-raised-button [disabled]="!enableApplyFilterButton" class="action-button save" (click)="apply()" cdkFocusInitial>Apply</button>
    <button mat-raised-button class="action-button close" (click)="cancel()" cdkFocusInitial>Cancel</button>
</mat-dialog-actions>