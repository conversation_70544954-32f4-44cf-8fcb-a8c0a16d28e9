<div class="confirmation-dialog">
  <mat-dialog-title class="dialog-title">
    Confirmation Required!
  </mat-dialog-title>

  <mat-dialog-content class="dialog-content">
    <p *ngIf="data.oldTimerName !== 'None'">
      The <span class="highlight-purple">{{ data.oldTimerName }}</span> timer is already running.
      Do you want to stop it and start <span class="highlight-purple">{{ data.newTimerName }}</span> timer?
    </p>
    <p *ngIf="data.oldTimerName === 'None'">
      Do you want to start the timer <span class="highlight-orange">{{ data.newTimerName }}</span>?
    </p>
  </mat-dialog-content>

  <mat-dialog-actions align="center" class="dialog-actions">
    <button mat-raised-button class="btn-yes" (click)="closeDialog('YES')">Yes</button>
    <button mat-raised-button class="btn-no" (click)="closeDialog('NO')">No</button>
  </mat-dialog-actions>
</div>