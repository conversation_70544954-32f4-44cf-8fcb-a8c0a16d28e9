<h2 mat-dialog-title class="dialog-title">Filter Discrepancy Table</h2>
<p class="error" *ngIf="error">{{errorMessage}}</p>
<mat-dialog-content>
    <div class="dialog-container">
  
      <div class="section margin-bottom-35">
        <h2 class="section-title">Time Frame</h2>
        
        <mat-radio-group [(ngModel)]="selectedTimeFrame" (change)="timeFrameChange()">
          <mat-radio-button value="all">All</mat-radio-button>
          
          <mat-radio-button value="days">Days 
            <mat-form-field class="small-input">
              <input matInput type="number" [disabled]="!isDaysOptionSelected" [(ngModel)]="daysInput" placeholder="Days" (input)="validateDaysInput()" min="1">
            </mat-form-field>
          </mat-radio-button>
          
          <mat-radio-button value="timePeriod">Time Period
            <div class="date-range-container">
                <mat-form-field class="small-input">
                  <mat-label>From</mat-label>
                  <input [disabled]="!isTimePeriodOptionSelected" matInput [matDatepicker]="fromDatePicker" [(ngModel)]="fromDate" [matDatepickerFilter]="dateRangeFilter" (dateChange)="checkApplyButtonState()">
                  <mat-datepicker-toggle matSuffix [for]="fromDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #fromDatePicker></mat-datepicker>
                </mat-form-field>
                <span class="to-text">To</span>
                <mat-form-field class="small-input">
                  <mat-label>To</mat-label>
                  <input [disabled]="!isTimePeriodOptionSelected" matInput [matDatepicker]="toDatePicker" [(ngModel)]="toDate" [matDatepickerFilter]="dateRangeFilter" (dateChange)="checkApplyButtonState()">
                  <mat-datepicker-toggle matSuffix [for]="toDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #toDatePicker></mat-datepicker>
                </mat-form-field>
              </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
  
      <div class="section margin-bottom-15">
        <h2 class="section-title">
          Discrepancy Type
          <div class="border-line"></div>
        </h2>
  
        <mat-checkbox (change)="selectedDiscrepancyTypes()" [(ngModel)]="pdis" checked>PDIS</mat-checkbox>
        <mat-checkbox (change)="selectedDiscrepancyTypes()" [(ngModel)]="smis" checked>SMIS</mat-checkbox>
        <mat-checkbox (change)="selectedDiscrepancyTypes()" [(ngModel)]="mdis" checked>MDIS</mat-checkbox>
        <mat-checkbox (change)="selectedDiscrepancyTypes()" [(ngModel)]="mtsi" checked>MTSI</mat-checkbox>
      </div>
  
    </div>
</mat-dialog-content>

<mat-dialog-actions class="dialog-actions">
    <button mat-raised-button [disabled]="!enableApplyFilterButton" class="action-button save" (click)="apply()" cdkFocusInitial>Apply</button>
    <button mat-raised-button class="action-button close" (click)="cancel()" cdkFocusInitial>Cancel</button>
</mat-dialog-actions>