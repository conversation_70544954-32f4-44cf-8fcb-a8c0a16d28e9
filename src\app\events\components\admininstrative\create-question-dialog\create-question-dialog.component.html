<h2 mat-dialog-title>New Question</h2>
<mat-dialog-content>
  <div class="required-checkbox-container">
    <mat-checkbox [(ngModel)]="isRequired" color="primary">Required</mat-checkbox>
  </div>

  <p>Enter Question:</p>
  <div class="question-input-container">
    <mat-form-field appearance="outline">
      <textarea
        matInput
        placeholder="Enter your question text"
        [(ngModel)]="questionText"
        #questionTextArea
      ></textarea>
    </mat-form-field>
    <button
      mat-icon-button
      (click)="startSpeechRecognition()"
      [disabled]="isListening || error === 'Speech recognition is not supported in this browser.'"
      class="mic-button"
      title="Start speech recognition"
    >
      <mat-icon>mic</mat-icon>
    </button>
  </div>

  <div *ngIf="isListening" class="listening-indicator">
    Listening... (Say "Stop Jarvis" to stop)
  </div>
  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <p>Question Group:</p>
  <mat-radio-group [(ngModel)]="questionGrp" class="radio-group">
    <mat-radio-button value="MULTIPLE">Multiple</mat-radio-button>
    <mat-radio-button value="RADIO">Radio</mat-radio-button>
    <mat-radio-button value="TEXT">Text</mat-radio-button>
    <mat-radio-button value="DATE">Date</mat-radio-button>
    <mat-radio-button value="DATE_RANGE">Date Range</mat-radio-button>
  </mat-radio-group>

  <p *ngIf="questionGrp === 'MULTIPLE' || questionGrp === 'RADIO'">Enter Answers:</p>
  <mat-form-field *ngIf="questionGrp === 'MULTIPLE' || questionGrp === 'RADIO'" appearance="outline">
    <input
      matInput
      placeholder="Enter the answers to be shown (comma-separated)"
      [(ngModel)]="answers"
      (change)="addAnswers()"
    />
  </mat-form-field>

  <div *ngIf="questionGrp === 'MULTIPLE' || questionGrp === 'RADIO'" class="answer-tiles">
    <div *ngFor="let answer of answerTiles" class="answer-tile">
      {{ answer }}
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions>
  <button
    mat-raised-button
    color="primary"
    [disabled]="!questionText || !questionGrp"
    (click)="save()"
  >
    Save
  </button>
  <button mat-raised-button color="warn" (click)="cancel()">Cancel</button>
</mat-dialog-actions>