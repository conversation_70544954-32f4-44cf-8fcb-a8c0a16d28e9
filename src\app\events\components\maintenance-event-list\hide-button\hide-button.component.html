<h2 mat-dialog-title class="dialog-title">Customize Table</h2>
<mat-dialog-content class="dialog-content">
  <div cdkDropList (cdkDropListDropped)="drop($event)" class="options-container">
    <div *ngFor="let option of options; let i = index" cdkDrag class="option-item">
      <mat-icon class="drag-handle">drag_indicator</mat-icon>
      <mat-checkbox [(ngModel)]="option.selected" class="checkbox">{{ option.name }}</mat-checkbox>
    </div>    
  </div>
</mat-dialog-content>
<mat-dialog-actions class="dialog-actions">
    <button mat-raised-button class="action-button save" (click)="showAll()" cdkFocusInitial>Show All</button>
    <button mat-raised-button class="action-button save" (click)="onApply()" cdkFocusInitial>Apply</button>
    <button mat-raised-button class="action-button cancel" (click)="onCancel()">Cancel</button>
</mat-dialog-actions>