import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TubFilesNotesRequestDto } from '../../../dto/TubFileNotesRequestDto';
import { Location } from '@angular/common';

import { MatDialog } from '@angular/material/dialog';
import { MaintenanceEventListService } from '../../../services/maintenance-event-list.service';
import { TubFileNotesFinalResponse } from '../../../dto/TubFileNotesFinalResponse';
import { TubFileNotesStatusUpdateDto } from '../../../dto/TubFileNotesStatusUpdate';
import { TubFileNotesResponseDto } from '../../../dto/TubFileNotesResponseDto';
import { MaintenanceEventListStatusDto } from '../../../dto/maintenance-event-ListStatusDto';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { MaintenanceEventDetailsSharedService } from '../maintenance-event-details-shared.service';
import { TfnoteService } from '../../../services/tfnote.service';


@Component({
  selector: 'app-tub-file-notes',
  templateUrl: './tub-file-notes.component.html',
  styleUrl: './tub-file-notes.component.scss',
  standalone: false
})
export class TubFileNotesComponent implements OnInit, OnChanges {

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;
  @Input() changeStatusEticObj!: MaintenanceEventListStatusDto;
  @Output() noteSelected = new EventEmitter<any>();
  @Output() allNotesChanged = new EventEmitter<any[]>();
  selectedNote: any = null;
  allNotes: any = null;
  new_tub_file_note!: string;
  existing_tub_file_notes!: string[];
  eventId!: string;
  tubfilenotesrequest!: TubFilesNotesRequestDto;
  tubfilenotesresponse!: TubFileNotesResponseDto[];
  tubfilenotesupdaterequest!: TubFileNotesStatusUpdateDto;
  notes!: TubFileNotesResponseDto[];
  groupedNotes: any[] = [];
  sortOrder: string = 'desc';
  acn!: string;

  selectedRowData!: MaintenanceEventListStatusDto;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private noteService: TfnoteService,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private location: Location,
    public dialog: MatDialog,
    private sanitizer: DomSanitizer,
    private maintenanceEventDetailsSharedService: MaintenanceEventDetailsSharedService
  ) {}

  ngOnInit(): void {
    this.getTubFileNotes();
    this.noteService.tfnoteUpdated$.subscribe(() => {
      this.getTubFileNotes(); // Refresh notes when notified
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj']) {
      if (this.detailsViewObj.eventACN != "") {
        this.eventId = String(this.detailsViewObj.eventID);
        this.getTubFileNotes();
      }
    }

    if (changes['changeStatusEticObj']) {
      this.selectedRowData = this.changeStatusEticObj;
      this.eventId = String(this.selectedRowData.eventId);
      this.acn = this.selectedRowData.ACN;
      this.getTubFileNotes();
    }
  }

  sortNotes() {
    if (this.sortOrder === 'asc') {
      this.groupedNotes = this.groupedNotes.sort((a, b) => new Date(a.lastUpdateDtTm).getTime() - new Date(b.lastUpdateDtTm).getTime());
    } else {
      this.groupedNotes = this.groupedNotes.sort((a, b) => new Date(b.lastUpdateDtTm).getTime() - new Date(a.lastUpdateDtTm).getTime());
    }
  }

  selectNote(group: any) {
    if (this.selectedNote === group) {
      this.selectedNote = null;
      this.noteSelected.emit(null);
    } else {
      this.selectedNote = group;
      this.noteSelected.emit(group);
    }
  }

  getTubFileNotes() {
    this.maintenanceEventDetailsService.getTubfileNotes(parseInt(this.eventId)).subscribe(response => {
      this.tubfilenotesresponse = response;
      this.notes = this.tubfilenotesresponse;

      // Group notes by noteId
      const groupedByNoteId: { [key: number]: TubFileNotesResponseDto[] } = {};
      this.notes.forEach(note => {
        note.safeTfNote = note.tfNote || ''; // Handle null tfNote
        const noteId = note.noteId;
        if (!groupedByNoteId[noteId]) {
          groupedByNoteId[noteId] = [];
        }
        groupedByNoteId[noteId].push(note);
      });

      // Convert grouped notes to groupedNotes array
      this.groupedNotes = Object.values(groupedByNoteId).map(group => ({
        lastUpdateDtTm: group[0].lastUpdateDtTm,
        empName: group[0].empName,
        notes: group,
        eventTfNotesPk: group[0].eventTfNotesPk,
        noteId: group[0].noteId
      }));

      // Sort grouped notes by lastUpdateDtTm descending
      this.groupedNotes.sort((a, b) => new Date(b.lastUpdateDtTm).getTime() - new Date(a.lastUpdateDtTm).getTime());

      if (this.groupedNotes.length > 0) {
        this.maintenanceEventDetailsSharedService.setTubeFileNotes(this.notes);
      }

      // Calculate the maximum line length across all notes and footers
      let maxLength = 0;
      const noteGroups = this.groupedNotes.map(group => {
        const notesText = group.notes.map((note: TubFileNotesResponseDto) => note.safeTfNote).join('<br>');
        const empName = group.empName || 'Unknown';
        const lastUpdate = new Date(group.lastUpdateDtTm).toLocaleString();
        const footerText = `Updated by: ${empName} | Last Updated: ${lastUpdate}`;

        // Find max length among note lines
        const noteLines = notesText.split('<br>').map((line: string) => line.trim().length);
        const currentMax = Math.max(...noteLines, footerText.length);
        maxLength = Math.max(maxLength, currentMax);

        return { notesText, empName, lastUpdate };
      });

      // Create separator with maxLength + 10
      const separator = '<br>' + '-'.repeat(maxLength + 30) + '<br>';

      // Generate allNotes with separator between entries
      this.allNotes = noteGroups
        .map(group => {
          return `<br><b>TF Note:</b> ${group.notesText}<br><b>Updated by:</b> ${group.empName} | <b>Last Updated:</b> ${group.lastUpdate}<br>`;
        })
        .join(separator);

      this.allNotesChanged.emit(this.allNotes);
    });
  }

  goBack() {
    this.location.back();
  }
    
}