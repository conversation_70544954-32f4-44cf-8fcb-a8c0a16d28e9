import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppComponent } from '../app.component';
import { HeaderComponent } from '../app-layout/components/header/header.component';
import { FooterComponent } from '../app-layout/components/footer/footer.component';
import { LayoutComponent } from '../app-layout/components/layout/layout.component';
import { MainComponent } from '../app-layout/components/main/main.component';
import { MaintenanceEventListComponent } from '../events/components/maintenance-event-list/maintenance-event-list.component';
import { MaintenanceEventDetailsComponent } from '../events/components/maintenance-event-details/maintenance-event-details.component';
import { MaintenanceEventInquiryComponent } from '../events/components/maintenance-event-inquiry/maintenance-event-inquiry.component';
import { MaintenanceEventReportsComponent } from '../events/components/maintenance-event-reports/maintenance-event-reports.component';
import { CustomMaintenanceEventListHeaderComponent } from '../events/components/maintenance-event-list/custom-maintenance-event-list-header/custom-maintenance-event-list-header.component';
import { CustomMaintenanceEventListStatusComponent } from '../events/components/maintenance-event-list/custom-maintenance-event-list-status/custom-maintenance-event-list-status.component';
import { SpinnerComponent } from '../app-layout/components/spinner/spinner.component';
import { CustomMaintenanceEventListEticComponent } from '../events/components/maintenance-event-list/custom-maintenance-event-list-etic/custom-maintenance-event-list-etic.component';
import { CustomMaintenanceEventListAlertsComponent } from '../events/components/maintenance-event-list/custom-maintenance-event-list-alerts/custom-maintenance-event-list-alerts.component';
import { HideButtonComponent } from '../events/components/maintenance-event-list/hide-button/hide-button.component';
import { CustomiseSettingsIconComponent } from '../events/components/maintenance-event-list/customise-settings-icon/customise-settings-icon.component';
import { DiscrepanciesComponent } from '../events/components/maintenance-event-details/discrepancies/discrepancies.component';
import { EventDetailsHeaderComponent } from '../events/components/maintenance-event-details/event-details-header/event-details-header.component';
import { DiscrepanciesDetailDialogComponent } from '../events/components/maintenance-event-details/discrepancies/discrepancies-detail-dialog/discrepancies-detail-dialog.component';
import { DiscrepanciesFilterDialogComponent } from '../events/components/maintenance-event-details/discrepancies/discrepancies-filter-dialog/discrepancies-filter-dialog.component';
import { FlightEticInfoComponent } from '../events/components/maintenance-event-details/flight-etic-info/flight-etic-info.component';
import { MaintenanceOosEventComponent } from '../events/components/maintenance-event-details/maintenance-oos-event/maintenance-oos-event.component';
import { CustomFilterIconComponent } from '../events/components/maintenance-event-details/discrepancies/custom-filter-icon/custom-filter-icon.component';
import { AddMaintenanceEventComponent } from '../events/components/add-maintenance-event/add-maintenance-event.component';
import { AddEventHeaderComponent } from '../events/components/add-maintenance-event/add-event-header/add-event-header.component';
import { MsnComponent } from '../events/components/maintenance-event-details/msn/msn.component';
import { CustomMsnCheckboxComponent } from '../events/components/maintenance-event-details/msn/custom-msn-checkbox/custom-msn-checkbox.component';
import { AddEventConfirmationPopupComponent } from '../events/components/add-maintenance-event/add-event-confirmation-popup/add-event-confirmation-popup.component';
import { DoaFormComponent } from '../events/components/maintenance-event-list/maintenance-event-list-forms/doa-form/doa-form.component';
import { IntakeFormComponent } from '../events/components/add-maintenance-event/intake-form/intake-form.component';

import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { MaterialModule } from './material.module';
import { CommonLibrariesModule } from './common.module';
import { ChangeStatusETICComponent } from '../events/components/maintenance-event-list/maintenance-event-list-actions/change-status-etic/change-status-etic.component';
import { TubFileNotesComponent } from '../events/components/maintenance-event-details/tub-file-notes/tub-file-notes.component';
import { CustomLinkDowningitemComponent } from '../events/components/maintenance-event-details/discrepancies/custom-link-downingitem/custom-link-downingitem.component';
import { MsnShippingDetailsComponent } from '../events/components/maintenance-event-details/msn/msn-shipping-details/msn-shipping-details.component';
import { MsnDetailsInfoComponent } from '../events/components/maintenance-event-details/msn/msn-details-info/msn-details-info.component';
import { CustomMsnTableFilterComponent } from '../events/components/maintenance-event-details/msn/msn-details-info/custom-msn-table-filter/custom-msn-table-filter.component';
import { AddAcnComponent } from '../events/components/maintenance-event-list/add-acn/add-acn.component';
import { MgrCaptureDialogComponent } from '../events/components/maintenance-event-details/mgr-capture-dialog/mgr-capture-dialog.component';

import { QuillModule } from 'ngx-quill';
import { FormsModule } from '@angular/forms';
import { ReportingCategoriesComponent } from '../events/components/maintenance-event-details/reporting-categories/reporting-categories.component';
import { NiwTimersComponent } from '../events/components/maintenance-event-details/niw-timers/niw-timers.component';
import { EditTimersComponent } from '../events/components/maintenance-event-details/niw-timers/edit-timers/edit-timers.component';
import { AddEditTimersComponent } from '../events/components/maintenance-event-details/niw-timers/add-edit-timers/add-edit-timers.component';
import { TimerCellComponent } from '../events/components/maintenance-event-details/niw-timers/timer-cell/timer-cell.component';
import { TotalNiwTimerCellComponent } from '../events/components/maintenance-event-details/niw-timers/total-niw-timer-cell/total-niw-timer-cell.component';
import { StartTimerConfirmationPopupComponent } from '../events/components/maintenance-event-details/niw-timers/start-timer-confirmation-popup/start-timer-confirmation-popup.component';
import { TfEditorComponent } from '../events/components/maintenance-event-details/tub-file-notes/tf-editor/tf-editor.component';
import { TfEmailNoteComponent } from '../events/components/maintenance-event-details/tub-file-notes/tf-email-note/tf-email-note.component';
import { TfEmailComponent } from '../events/components/maintenance-event-details/tub-file-notes/tf-email/tf-email.component';
import { AdmininstrativeComponent } from '../events/components/admininstrative/admininstrative.component';
import { CreateIntakeFormComponent } from '../events/components/admininstrative/create-intake-form/create-intake-form.component';
import { CreateQuestionDialogComponent } from '../events/components/admininstrative/create-question-dialog/create-question-dialog.component';
import { EditIntakeFormComponent } from '../events/components/admininstrative/edit-intake-form/edit-intake-form.component';
import { QuestionsListComponent } from '../events/components/admininstrative/questions-list/questions-list.component';
import { UserIntakeFormDialogComponent } from '../events/components/admininstrative/user-intake-form-dialog/user-intake-form-dialog.component';
import { PendingConfirmationComponent } from '../events/components/maintenance-event-list/maintenance-event-list-actions/change-status-etic/pending-confirmation/pending-confirmation.component';
import { SuccessErrorDialogComponent } from '../events/components/maintenance-event-list/maintenance-event-list-actions/change-status-etic/success-error-dialog/success-error-dialog.component';
import { ReviewAlertComponent } from '../events/components/maintenance-event-list/maintenance-event-list-actions/review-alert/review-alert.component';
import { CloseEventComponent } from '../events/components/maintenance-event-list/close-event/close-event.component';

ModuleRegistry.registerModules([AllCommunityModule]);

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    LayoutComponent,
    MainComponent,
    MaintenanceEventListComponent,
    MaintenanceEventDetailsComponent,
    MaintenanceEventInquiryComponent,
    MaintenanceEventReportsComponent,
    CustomMaintenanceEventListHeaderComponent,
    CustomMaintenanceEventListStatusComponent,
    SpinnerComponent,
    CustomMaintenanceEventListEticComponent,
    CustomMaintenanceEventListAlertsComponent,
    HideButtonComponent,
    CustomiseSettingsIconComponent,
    DiscrepanciesComponent,
    EventDetailsHeaderComponent,
    DiscrepanciesDetailDialogComponent,
    DiscrepanciesFilterDialogComponent,
    NiwTimersComponent,
    EditTimersComponent,
    AddEditTimersComponent,
    ReportingCategoriesComponent,
    TimerCellComponent,
    TotalNiwTimerCellComponent,
    FlightEticInfoComponent,
    MaintenanceOosEventComponent,
    CustomFilterIconComponent,
    StartTimerConfirmationPopupComponent,
    AddMaintenanceEventComponent,
    AddEventHeaderComponent,
    MsnComponent,
    CustomMsnCheckboxComponent,
    AddEventConfirmationPopupComponent,
    ChangeStatusETICComponent,
    TubFileNotesComponent,
    DoaFormComponent,
    IntakeFormComponent,
    CustomLinkDowningitemComponent,
    MsnShippingDetailsComponent,
    MsnDetailsInfoComponent,
    CustomMsnTableFilterComponent,
    AddAcnComponent,
    TfEditorComponent,
    TfEmailNoteComponent,
    TfEmailComponent,
    MgrCaptureDialogComponent,
    AdmininstrativeComponent,
    CreateIntakeFormComponent,
    CreateQuestionDialogComponent,
    EditIntakeFormComponent,
    QuestionsListComponent,
    UserIntakeFormDialogComponent,
    PendingConfirmationComponent,
    SuccessErrorDialogComponent,
    ReviewAlertComponent,
    CloseEventComponent
  ],
  exports: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    LayoutComponent,
    MainComponent,
    MaintenanceEventListComponent,
    MaintenanceEventDetailsComponent,
    MaintenanceEventInquiryComponent,
    MaintenanceEventReportsComponent,
    CustomMaintenanceEventListHeaderComponent,
    CustomMaintenanceEventListStatusComponent,
    SpinnerComponent,
    CustomMaintenanceEventListEticComponent,
    CustomMaintenanceEventListAlertsComponent,
    HideButtonComponent,
    CustomiseSettingsIconComponent,
    DiscrepanciesComponent,
    EventDetailsHeaderComponent,
    DiscrepanciesDetailDialogComponent,
    DiscrepanciesFilterDialogComponent,
    NiwTimersComponent,
    EditTimersComponent,
    AddEditTimersComponent,
    ReportingCategoriesComponent,
    TimerCellComponent,
    TotalNiwTimerCellComponent,
    FlightEticInfoComponent,
    MaintenanceOosEventComponent,
    CustomFilterIconComponent,
    StartTimerConfirmationPopupComponent,
    AddMaintenanceEventComponent,
    AddEventHeaderComponent,
    MsnComponent,
    CustomMsnCheckboxComponent,
    AddEventConfirmationPopupComponent,
    ChangeStatusETICComponent,
    TubFileNotesComponent,
    DoaFormComponent,
    IntakeFormComponent,
    CustomLinkDowningitemComponent,
    MsnShippingDetailsComponent,
    MsnDetailsInfoComponent,
    CustomMsnTableFilterComponent,
    AddAcnComponent,
    TfEditorComponent,
    TfEmailNoteComponent,
    TfEmailComponent,
    MgrCaptureDialogComponent,
    AdmininstrativeComponent,
    CreateIntakeFormComponent,
    CreateQuestionDialogComponent,
    EditIntakeFormComponent,
    QuestionsListComponent,
    UserIntakeFormDialogComponent,
    PendingConfirmationComponent,
    SuccessErrorDialogComponent,
    ReviewAlertComponent,
    CloseEventComponent
  ],
  imports: [
    CommonModule,
    CommonLibrariesModule,
    MaterialModule,
    FormsModule,
    QuillModule.forRoot()
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ComponentsModule { }