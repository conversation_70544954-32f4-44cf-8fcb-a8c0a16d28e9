.form{
    padding-top: 50%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    margin: auto;
}

.full-width{
    width: 100%;
}

.mat-card{
    width: 25%;
    position: relative;
    margin-top: 10px;
    border-radius: 25px;
}

.mat-form-field{
    padding-block-end: 100%;
}

.field-padding {
    margin-bottom: 25%; /* Adds padding below each field */
    margin-right: 10%;
    margin-left: 10%;
}

.mat-card-content {
    position: relative;
    background-color: whitesmoke;
}

.spaced-button {
    margin-right: 10px; /* Adjust for the desired space between buttons */
    margin-bottom: 10px;
    margin-bottom: 10px;
}

.box-field{
    margin: auto;
    margin-right: 10px;
}

.mb-3 {
    display: inline-block;
    margin-left: 10px;
    margin-bottom: 10px;
    // margin-top: 10px;
    margin-right: 10px;
}

.example-tree {
    width: 300px;
    max-width: 400px;
    overflow: hidden;
    background: #fff;
    border-radius: 4px;
  }
  
  .example-tree-invisible {
    display: none;
  }
  
  .mat-tree-node {
    display: flex;
    align-items: center;
    padding-left: 16px;  /* Adjust this for the indentation */
  }
  
  .example-tree-children {
    padding-left: 20px; /* Increase the padding for child items */
    transition: all 0.3s ease-in-out;
  }
  
  .mat-icon-button {
    margin-right: 8px;
  }

  .mat-card {
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
    border-radius: 25px;
}

.mainBody {
    display: flex;
    flex-direction: row;
    width: 100%; /* Ensure mainBody takes full width */
    align-items: center;
    box-sizing: border-box; /* Include padding and border in element's total width and height */
  }

  .example-tree {
    width: 300px;
    max-width: 400px;
    overflow: hidden;
    background: #fff;
    border-radius: 4px;
  }
  
  .example-tree-invisible {
    display: none;
  }
  
  .mat-tree-node {
    display: flex;
    align-items: center;
    padding-left: 16px;  /* Adjust this for the indentation */
  }
  
  .example-tree-children {
    padding-left: 20px; /* Increase the padding for child items */
    transition: all 0.3s ease-in-out;
  }
  
  .mat-icon-button {
    margin-right: 8px;
  }