<h2 mat-dialog-title>{{editorTitle}}</h2>
            <div *ngIf="editorTitle === 'Add Tub File Note'">         
              <mat-form-field appearance="fill" class="wide-dropdown">
                    <mat-label>Notes for discrepancy</mat-label>
                        <mat-select   [(ngModel)]="selectedDisc" (ngModelChange)="onDiscChange($event)">
                            <mat-option *ngFor="let ld of linkedDiscrepancies" [value]="ld">
                                    <div style="font-size: 12px; font-weight: bold;">{{ ld }}</div>
                          </mat-option>
                      </mat-select>
                </mat-form-field>
            </div>
<mat-dialog-content>
  <quill-editor [modules]="quillModules" [(ngModel)]="editorContent" (onEditorCreated)="onEditorCreated($event)" [style.width.%]="100"
  [style.height.px]="300"></quill-editor>

  <div class="file-upload">
    <label for="fileInput">Attach Files:</label>
    <input type="file" id="fileInput" multiple (change)="onFileChange($event)">
  </div>
</mat-dialog-content>

<mat-dialog-actions class="email-notes-wrapper-actions">
  <button  mat-raised-button mat-dialog-close class="orange-button">Cancel</button>
  <button mat-raised-button class="orange-button" (click)="submit()">Submit</button>
</mat-dialog-actions>
