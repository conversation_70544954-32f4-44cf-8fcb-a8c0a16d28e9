<div class="create-intake-form-container">
    <div class="intake-form-drag-box">
        <div class="intake-form-drag-box-title">{{ selectedFormTitle }}
            <div class="additional-fields-container">
                <div class="field-item">
                    <span class="field-label">Role:</span>
                    <span class="field-value">{{role}}</span>
                </div>
                <div class="field-item">
                    <span class="field-label">Event Type:</span>
                    <span class="field-value">{{event}}</span>
                </div>
                <div *ngIf="fleetType" class="field-item">
                    <span class="field-label">Fleet:</span>
                    <span class="field-value">{{fleetType}}</span>
                </div>
            </div>
        </div>
        <div class="create-new-question-container">
            <div class="icon-text-container" (click)="addNewQuestion()">
                <mat-icon>add</mat-icon>
                <div class="create-new-question">Create New Question</div>
            </div>
        </div>
        <div class="intake-form-scrollable-content">
            <div cdkDropList #todoList="cdkDropList" [cdkDropListData]="selectedIntakeFormQuestions"
                [cdkDropListConnectedTo]="[doneList]" class="intake-form-questions-list"
                (cdkDropListDropped)="drop($event)">
                @for (item of selectedIntakeFormQuestions; track item; let i = $index) {
                <div class="intake-form-box" cdkDrag>
                    <div class="question-details">
                        <p class="question-text">Question: {{ item.questionTxt }}</p>
                        <p class="question-type" *ngIf="item.questionGrp">
                            Answer Type: {{ item.questionGrp }}
                        </p>
                        <p class="question-answers" *ngIf="
                item.questionGrp === 'RADIO' || item.questionGrp === 'MULTIPLE'
              ">
                            Answers: {{ getAnswersText(item) }}
                        </p>
                    </div>
                    <div class="intake-form-box-actions">
                        <mat-icon class="edit-icon" (click)="editQuestion(item)">edit</mat-icon>
                        <mat-icon class="delete-icon" (click)="deleteQuestion(item)">delete</mat-icon>
                    </div>
                </div>
                }
            </div>
        </div>
        <div class="button-container">
            <div class="submit-button">
                <button mat-raised-button class="mat-button1" [disabled]="!selectedIntakeFormQuestions.length"
                    (click)="OnSubmit()">
                    Submit
                </button>
            </div>
            <div class="action-button">
                <button mat-raised-button class="mat-button1" (click)="OnCancel()">
                    Cancel
                </button>
            </div>
            <div class="delete-button">
                <button mat-raised-button class="mat-button1" (click)="OnDelete()">
                    Delete
                </button>
            </div>
        </div>
    </div>
    <div class="divider"></div>
    <div class="previous-questions-container">
        <mat-accordion multi="false" class="expansion-accordion">
            <mat-expansion-panel *ngFor="let panel of panels; let i = index" class="expansion-panels"
                [expanded]="openIndex === i" (opened)="setOpenPanel(i)">
                <mat-expansion-panel-header class="previous-questions-title-container">
                    <mat-panel-title class="previous-questions-title">
                        {{ panel.title }}
                    </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="previous-questions-scrollable-content">
                    <div *ngIf="panel.panelId === 1">
                        <div class="search-container">
                            <mat-icon class="search-icon">search</mat-icon>
                            <input type="text" class="search-input" placeholder="Search questions..."
                                [(ngModel)]="searchTerm" (input)="filterQuestions()" />
                            <div class="search-border-animation"></div>
                        </div>
                        <div cdkDropList #doneList="cdkDropList" [cdkDropListData]="filteredQuestions"
                            [cdkDropListConnectedTo]="[todoList]" class="questions-list"
                            (cdkDropListDropped)="drop($event)">
                            @for (item of filteredQuestions; track item) {
                            <div class="intake-form-box" cdkDrag>
                                <div class="question-details">
                                    <p class="question-text">Question: {{ item.questionTxt }}</p>
                                    <p class="question-type" *ngIf="item.questionGrp">
                                        Question Type: {{ item.questionGrp }}
                                    </p>
                                    <p class="question-answers" *ngIf="
                                        item.questionGrp === 'RADIO' ||
                                        item.questionGrp === 'MULTIPLE'">
                                        Answers: {{ getAnswersText(item) }}
                                    </p>
                                </div>
                            </div>
                            }
                        </div>
                    </div>
                    <div *ngIf="panel.panelId === 2" class="intake-form-list">
                        <p *ngFor="let userIntakeForm of userIntakeFormList" class="intake-form-list-item" (click)="
                            modifyTheSelectedForm(userIntakeForm.intakeForm.intakeFormId)">
                            {{ userIntakeForm.intakeFormNm }}
                        </p>
                    </div>
                </div>
            </mat-expansion-panel>
        </mat-accordion>
    </div>
</div>