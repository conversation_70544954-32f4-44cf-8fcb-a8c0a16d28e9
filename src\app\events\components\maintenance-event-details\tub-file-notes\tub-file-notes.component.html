<div class="notes-wrapper">
  <div class="sort-by-sticky">
    <mat-label class="sort-label">Sort by</mat-label>
    <mat-radio-group [(ngModel)]="sortOrder" (ngModelChange)="sortNotes()" class="sort-radio-group">
      <mat-radio-button value="asc" class="sort-radio">Oldest Notes First</mat-radio-button>
      <mat-radio-button value="desc" class="sort-radio">Newest Notes First</mat-radio-button>
    </mat-radio-group>
  </div>
  <div class="notes-scroll-container">
    <div class="existing-notes-container">
      <p *ngIf="!groupedNotes || groupedNotes.length === 0" class="no-notes">No existing tub file notes available.</p>
      <mat-card *ngFor="let group of groupedNotes" class="compact-note-card tf-note-card selectable" 
                (click)="selectNote(group)" 
                [ngClass]="{'selected-card': group === selectedNote}">
        <div class="note-content">
          <div class="note-text-container">
            <div *ngFor="let note of group.notes; let i = index" 
                 [ngClass]="{'note-heading': (i === 0 && (group.notes.length > 1 || note.tfNote?.toLowerCase().startsWith('posted new') || note.tfNote?.toLowerCase().startsWith('previous change'))), 'note-text': !(i === 0 && (group.notes.length > 1 || note.tfNote?.toLowerCase().startsWith('posted new') || note.tfNote?.toLowerCase().startsWith('previous change'))),'preserve-linebreaks': true}">
              {{ note.tfNote }}
            </div>
          </div>
          <div class="note-meta-content">
            <div class="meta-item">
              <span class="material-icons meta-icon">person</span>
              <span class="emp-name">{{ group.empName }}</span>
            </div>
            <div class="meta-item">
              <span class="material-icons meta-icon">schedule</span>
              <span class="update-time">{{ group.lastUpdateDtTm | date: 'short' }}</span>
            </div>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
</div>