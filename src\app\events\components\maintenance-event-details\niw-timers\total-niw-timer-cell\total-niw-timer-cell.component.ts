import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';


@Component({
  selector: 'app-total-niw-timer-cell',
  standalone: false,
  template: `{{ totalTime }}`,
  styleUrl: './total-niw-timer-cell.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TotalNiwTimerCellComponent implements OnInit{

  @Input() totalTime!: string;

  ngOnInit(): void {}

  constructor(private cd: ChangeDetectorRef) {}

}
