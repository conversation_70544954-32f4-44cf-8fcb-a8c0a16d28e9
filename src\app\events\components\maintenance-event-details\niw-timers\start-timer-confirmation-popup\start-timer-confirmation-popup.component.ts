import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-start-timer-confirmation-popup',
  standalone: false,
  templateUrl: './start-timer-confirmation-popup.component.html',
  styleUrl: './start-timer-confirmation-popup.component.scss'
})
export class StartTimerConfirmationPopupComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, 
    private dialogRef: MatDialogRef<StartTimerConfirmationPopupComponent>) {}
  
  closeDialog(data: any) {
    this.dialogRef.close(data);
  }

}
