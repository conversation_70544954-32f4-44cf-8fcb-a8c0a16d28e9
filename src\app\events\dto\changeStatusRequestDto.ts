// event-details.model.ts (or a more specific name if this represents a particular type of event/change)

export interface ChangeStatusRequestDTO {
  eventId: number;
  changeType: number;
  accessLevel: string;
  acn: string;
  status: string;
  empDepartment: string;
  resMgrId: string;
  memDeskContact: string;
  eticDateTime: string | null; // ISO 8601 string (e.g., "YYYY-MM-DDTHH:mm:ss")
  eticInfo: string;
  eticComment: string;
  userId: string;
  employeeName: string;
  tokenId: string;
  newStatus: string;
  newEticDateTime: string | null; // ISO 8601 string
  newEticInfo: string;
  newEticComment: string;
  OST: string; // Consider 'boolean' if values are consistently 'Y'/'N'
  newOST: string; // Consider 'boolean' if values are consistently 'Y'/'N'
  eticRsnCd: string;
  newEticRsnCd: string;
  eticRsnComment: string;
  newEticRsnComment: string;
  changeRequestLastUpdated: string | null; // String format, might need Date conversion
  requestStatus: string;
  activeTimerId: string | null; // Or number, depending on typical usage (e.g., "1")
  timerId: string | null; // Or number
  timerName: string | null;
  timerLastUpdated: string | null; // ISO 8601 string
  timerCreatedDateTime: string | null; // ISO 8601 string
  timerStartDateTime: string | null; // ISO 8601 string
  timerStopDateTime: string | null; // ISO 8601 string
  isPendingRequest: boolean;
  eticEnteredInError: boolean;
  modifyPendingEvent: boolean;
  cancelPendingEvent: boolean;
  statusModified: boolean;
  eticModified: boolean;
  commentModified: boolean;
  ostModified: boolean;
  eticRsnCdModified: boolean;
  eticRsnCommentModified: boolean;
  tfNotesList: string[]; // Array of strings for "Tub File Notes"
  createdDateTime: string; // ISO 8601 string
  changedDateTime: string; // ISO 8601 string
}