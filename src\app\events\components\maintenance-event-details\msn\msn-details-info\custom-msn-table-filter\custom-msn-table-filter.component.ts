import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-custom-msn-table-filter',
  standalone: false,
  templateUrl: './custom-msn-table-filter.component.html',
  styleUrl: './custom-msn-table-filter.component.scss'
})
export class CustomMsnTableFilterComponent {

  tableName: string = '';
  displayedColumns: string[] = [];
  hiddenColumns: string[] = [];

  constructor(
    public dialogRef: MatDialogRef<CustomMsnTableFilterComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { tableName: string, allColumns: string[], visibleColumns: string[] }
  ) {
    this.tableName = data.tableName;
    this.displayedColumns = data.visibleColumns.length > 0 ? [...data.visibleColumns] : [...data.allColumns];
    this.hiddenColumns = data.allColumns.filter(col => !this.displayedColumns.includes(col));
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      // Move within the same list
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Move between lists
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onApply(): void {
    this.dialogRef.close({tableName: this.tableName, displayColumns: this.displayedColumns, hideColumns: this.hiddenColumns});
  }

}
