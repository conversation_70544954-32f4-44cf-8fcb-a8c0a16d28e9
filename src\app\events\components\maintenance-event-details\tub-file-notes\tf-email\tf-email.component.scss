.email-notes-wrapper {
  background-color: #3F2876; 
  color: white;
  padding: 2px;
  border-radius: 8px;
  overflow: hidden !important;
}

.email-notes-wrapper h2 {
  color: white;
  text-align: center;
}

mat-form-field {
  background: white;
  border-radius: 4px;
  margin-bottom: 16px;
}

.orange-button {
  background-color: #ff6600 !important; 
  color: white !important;
  font-weight: bold;
  border-radius: 20px;
  padding: 6px 20px;
  transition: background-color 0.3s ease;
}

.orange-button:hover {
  background-color: #ffa366 !important;
  color: black !important;
}
.email-notes-wrapper quill-editor .ql-editor {
  background-color: white !important;
}
.green-snackbar {
  background-color: #4CAF50 !important; 
  color: white;
}

.red-snackbar {
  background-color: #f44336 !important; 
  color: white;
}
 .email-notes-wrapper .mat-mdc-dialog-content>:last-child {
    margin-bottom: 0;
    padding-top: 10px;
}
.email-notes-wrapper-actions{
  align-items: center;
  justify-content: center;
}