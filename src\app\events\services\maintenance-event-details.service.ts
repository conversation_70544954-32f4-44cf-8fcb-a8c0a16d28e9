import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { RetrievalDto } from '../dto/retrievalDto';
import { BehaviorSubject, Observable } from 'rxjs';
import { MetsEventUpdateEntity } from '../dto/maintenance-event-update-dto';
import { EnvironmentService } from '../../app-layout/services/environment.service';
import { SessionStorageKeys } from '../constants/sessionStorageKeys';
import { UpdateDiscrepancyDto } from '../dto/updateDiscrepancyDto';
import { DetailViewResponseDao } from '../dao/detailViewDao';
import { TfSendEmail } from '../dto/TfSendEmail';
import { DiscrepancyTxtReq } from '../dao/discrepancyUpdTxt';
import { TubFileNotesStatusUpdateDto } from '../dto/TubFileNotesStatusUpdate';

@Injectable({
  providedIn: 'root'
})

export class MaintenanceEventDetailsService {

  private showTitleEffect: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  showTitleEffect$ = this.showTitleEffect.asObservable();

  private showFlightEticTitleEffect: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  showFlightEticTitleEffect$ = this.showFlightEticTitleEffect.asObservable();

  constructor(private http: HttpClient, private environmentService: EnvironmentService) { }

  getMaintenanceEventDetailsDiscrepanciesTableHeaders(): Observable<any> {
    return this.http.get<any>(this.environmentService.getMaintenanceEventDetailsDiscrepanciesTableHeaders);
  }

  getMsnTableHeaders(): Observable<any> {
    return this.http.get<any>(this.environmentService.getMsnTableHeaders);
  }

  getReportingCategories(eventId: string, eventType: string): Observable<any> {
    let params = null;
    if(eventId != "") {
      params = new HttpParams().set('eventId', eventId);
    } else {
      params = new HttpParams().set('eventType', eventType);
    }
    return this.http.get<any>(this.environmentService.getReportingCategories, { params });
  }

  updateReportingCategories(data: any): Observable<any> {
    return this.http.post<any>(this.environmentService.updateReportingCategories, data);
  }

  getNiwTimers(data: any): Observable<any> {
    let params: any;
    if(data['eventId'] != "" && data['timerId'] != '') {
      params = new HttpParams().set('eventId', data['eventId']).set('timerId', data['timerId']);
    } else if(data['eventId'] != "" && data['timerId'] == '') {
      params = new HttpParams().set('eventId', data['eventId']);
    }
    return this.http.get<any>(this.environmentService.getNiwTimers, { params });
  }

  getAllNiwTimers(): Observable<any> {
    return this.http.get<any>(this.environmentService.getAllNiwTimers);
  }

  getAogNiwTimers(eventId: number): Observable<any> {
    let params: any;
    params = new HttpParams().set('eventId', eventId);
    return this.http.get<any>(this.environmentService.getNiwTimers, { params });
  }

  updateNiwTimers(data: any): Observable<any> {
    return this.http.post<any>(this.environmentService.updateNiwTimers, data);
  }

  getDiscrepanciesTableList(acn: number, eventId: number | null): Observable<any> {
    let params = new HttpParams().set('acn', acn).set('userId', this.getEmployeeIdFromStorage());
    if (eventId !== null) {params = params.set('eventId', eventId.toString())}
    return this.http.get<any>(this.environmentService.getDiscrepancyTableLData, { params });
  }

  getDiscrepancyDetailInfo(acn: number, ata: number, discNumber: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage()); 
    return this.http.get<any>(`${this.environmentService.getDiscrepancyDetailInfo}${acn}/${ata}/${discNumber}`, { params });
  }

  getMainDetailScreenData(acn: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.get<any>(`${this.environmentService.getDetailViewData}${acn}`, { params });
  }

  getEventFlightEticDetails(eventId: number): Observable<any> {
    return this.http.get<any>(`${this.environmentService.getEventFlightEticDetails}${eventId}`);
  }

  updateMainDetailScreenEventDetails(detailViewResponse: DetailViewResponseDao): Observable<any> {
    return this.http.post<MetsEventUpdateEntity>(this.environmentService.updateMainDetailScreenEventDetails, detailViewResponse);
  }

  sendEmail(tfSendEmailDto: TfSendEmail): Observable<any> {
    return this.http.post<TfSendEmail>(this.environmentService.sendEmail, tfSendEmailDto);
  }

  updateDiscrepancies(updateDiscrepancyDto: UpdateDiscrepancyDto): Observable<any> {
    return this.http.post<UpdateDiscrepancyDto>(this.environmentService.updateDiscrepancies, updateDiscrepancyDto);
  }

  getMsnTableData(acn: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.get<any>(`${this.environmentService.getMsnTableData}${acn}`, { params });
  }

  getMsnShippingInfo(msn: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.get<any>(`${this.environmentService.getMsnshippingInfo}${msn}`, { params });
  }

  getMsnShippingDetail(msn: number): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.get<any>(`${this.environmentService.getMsnshippingDetail}${msn}`, { params });
  }
  
  showTitleEffects(value: boolean) {
    this.showTitleEffect.next(value);
  }

  showFlightEticTitleEffects(value: boolean) {
    this.showFlightEticTitleEffect.next(value);
  }

  saveDiscrepanciesFilterInSessionStorage(value: any) {
    this.setItem(SessionStorageKeys.DISCREPANCY_TABLE_FILTER, value);
  }

  getDiscrepanciesFilterFromSessionStorage() {
    return this.getItem<any>(SessionStorageKeys.DISCREPANCY_TABLE_FILTER);
  }

  getEmployeeIdFromStorage() {
    const userDetails = this.getItem<{ id: string }>(SessionStorageKeys.LOGGED_USER_DETAILS);
    if (userDetails && userDetails?.id) {
      return parseInt(userDetails.id);
    } else {
      throw new Error('User details not found in session storage or missing id');
    }
  }

  saveMsnTableColumnsInSessionStorage(key: string, value: string[]) {
    if (value && Array.isArray(value)) {
      this.setItem(key, value);
    } else {
      console.error(`Invalid value for key ${key}. Expected an array.`);
    }
  }

  getMsnTableColumnsFromSessionStorage(key: string): string[] {
    const storedValue = this.getItem<string[]>(key);
    if (storedValue && Array.isArray(storedValue)) {
      return storedValue;
    } else {
      console.warn(`No valid data found for key ${key} in session storage.`);
      return [];
    }
  }
  getUserInfo(): Observable<any> {
    const user = this.getEmployeeIdFromStorage();
    return this.http.get<any>(`${this.environmentService.getUserInfo}${user}`);
  }
  getTubfileNotes(eventId: number): Observable<any> {
      const url = `${this.environmentService.getTubFileNotes}${eventId}`;
      return this.http.get<any>(url);
    }
    
  updateTubFileNotes(tubFilesNotesDto: TubFileNotesStatusUpdateDto) : Observable<any> {
    return this.http.post<TubFileNotesStatusUpdateDto>(this.environmentService.updateTubFileNotes, tubFilesNotesDto);
  }
  /** Generic method to set sessionStorage items */
  private setItem(key: string, value: any): void {
    if (value !== null && value !== undefined) {
      sessionStorage.setItem(key, JSON.stringify(value));
    }
  }

  /** Generic method to get sessionStorage items */
  private getItem<T>(key: string): T | null {
    const storedValue = sessionStorage.getItem(key);
    if (!storedValue) return null;
    try {
      return JSON.parse(storedValue) as T;
    } catch (error) {
      console.error(`Error parsing sessionStorage key: ${key}`, error);
      return null;
    }
  }

  getDiscrepancyUpdtTxts(dscrpUpdtTxtReqList: DiscrepancyTxtReq[]): Observable<any> {
    const params = new HttpParams().set('userId', this.getEmployeeIdFromStorage());
    return this.http.post<MetsEventUpdateEntity>(this.environmentService.getDiscrepancyUpdttxt, dscrpUpdtTxtReqList, { params });
  }
}
