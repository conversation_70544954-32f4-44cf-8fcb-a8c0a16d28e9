import { Component } from '@angular/core';

@Component({
  selector: 'app-custom-filter-icon',
  standalone: false,
  templateUrl: './custom-filter-icon.component.html',
  styleUrl: './custom-filter-icon.component.scss'
})
export class CustomFilterIconComponent {

  params: any; // Ag-Grid injects params here

  agInit(params: any): void {
    this.params = params;
  }

  handleClick() {
    if (this.params?.context?.onSettingsClick) {
      this.params.context.onSettingsClick();
    }
  }

}
