/* Disable overflow on the entire viewport */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden; /* Prevent scrolling on the outer container */
}

.app-container {
  min-height: 100vh;
}

// /* Style for the header */
// app-header {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 10% !important; /* Set a fixed height for header */
//   z-index: 1000;
// }

.login-error {
  font-weight: 450;
  font-size: 1rem;
  padding-bottom: 1.5rem;
  color: black;
  text-align: center;
}

/* Style for the main content area */
.main-content {
  position: absolute;
  top: 0%; /* Space below header */
  bottom: 0; /* Space above footer */
  left: 0;
  right: 0;
  overflow-y: auto; /* Only main content is scrollable */
  box-sizing: border-box;
}

/* Style for the footer */
app-footer {
  position: fixed;
  background-color: #4d148c;
  color: white;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2.5%;
  z-index: 1000;
}