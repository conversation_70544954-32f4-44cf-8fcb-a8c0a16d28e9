  <!-- Main Section -->
  <div class="main">
    <mat-sidenav-container class="sidenav-container">
      <mat-sidenav
        #sidenav
        mode="side"
        [opened]="true"
        class="sidenav"
        [class.collapsed]="isCollapsed"
      >

      <div class="headerSection">
        <app-header></app-header>
      </div>
      
      <div class="sidenav-inner">
        <div class="icon-section" [ngClass]="{ 'collapsed': isCollapsed }">
          <mat-nav-list class="app-section-nav-list">
            <div class="badge-icon-wrapper"
              [matBadge]="unreadNotificationsCount"
              [matBadgeHidden]="unreadNotificationsCount === 0"
              matBadgeColor="warn"
              matBadgePosition="above after">
                <mat-list-item
                class="app-icon-section"
                #notificationPopover="ngbPopover"
                [ngbPopover]="notificationTemplate"
                popoverClass="custom-notification-popover"
                triggers="click"
                placement="right-top"
                container="body"
                (hidden)="onNotificationPopoverClosed()"
                matTooltip="Notifications" 
                matTooltipClass="tooltip"
                matTooltipPosition="right"
                matTooltipShowDelay="100"
                matTooltipHideDelay="200"
              >
                <mat-icon
                  class="menu-icon"
                  [ngClass]="{ 'has-unread': hasUnreadNotifications, 'has-read': !hasUnreadNotifications }"
                  matListIcon
                >
                  notifications
                </mat-icon>
              </mat-list-item>
            </div>
           
            <ng-template #notificationTemplate>
              <div class="notification-content">
                <div class="notification-header">Notifications</div>

                <div class="notification-list">

                  <!-- NEW / UNREAD NOTIFICATIONS -->
                  <ng-container *ngIf="unreadNotifications.length > 0">
                    <div class="notification-section-title">New</div>
                    <div *ngFor="let notification of unreadNotifications" class="notification-item">
                      <div class="notification-body font-weight-bold">
                        <ng-container *ngIf="extractAcn(notification.body) as acn; else plainText">
                          New Event Added for
                          <a [routerLink]="['/maintenance-event-details']" [queryParams]="{ acn: acn }">
                            ACN : {{ acn }}
                          </a>
                        </ng-container>
                        <ng-template #plainText>
                          {{ notification.body }}
                        </ng-template>
                      </div>
                      <div class="notification-timestamp font-weight-bold">{{ notification.timestamp | date: 'short' }}</div>
                    </div>
                  </ng-container>

                  <!-- READ / EARLIER NOTIFICATIONS -->
                  <ng-container *ngIf="readNotifications.length > 0">
                    <div class="notification-section-title">Earlier</div>
                    <div *ngFor="let notification of readNotifications" class="notification-item read">
                      <div class="notification-body">
                        <ng-container *ngIf="extractAcn(notification.body) as acn; else plainText">
                          New Event Added for
                          <a [routerLink]="['/maintenance-event-details']" [queryParams]="{ acn: acn }">
                            ACN : {{ acn }}
                          </a>
                        </ng-container>
                        <ng-template #plainText>
                          {{ notification.body }}
                        </ng-template>
                      </div>
                      <div class="notification-timestamp font-weight-bold">{{ notification.timestamp | date: 'short' }}</div>
                    </div>
                  </ng-container>

                  <!-- NO NOTIFICATIONS AT ALL -->
                  <ng-container *ngIf="notifications.length === 0">
                    <div class="no-data-message">No notifications.</div>
                  </ng-container>

                </div>
              </div>
            </ng-template>

            <mat-list-item 
              class="app-icon-section" 
              (click)="loadComponent('/maintenance-event-list')" [class.selectedIcon]="isSelected('/maintenance-event-list')"
              matTooltip="Home" 
              matTooltipClass="tooltip"
              matTooltipPosition="right"
              matTooltipShowDelay="100"
              matTooltipHideDelay="200"
            >
              <mat-icon class="menuIcon" matListIcon>home</mat-icon>
            </mat-list-item>
            <mat-list-item 
              [disabled]="true" 
              [class.selectedIcon]="selectedItem === 'home'"
              class="app-icon-section temporaryDisabled" 
              (click)="selectItem('settings')"
            >
              <mat-icon class="menuIcon not-allowed-cursor" matListIcon>settings</mat-icon>
            </mat-list-item>
            <mat-list-item 
              [disabled]="true"
              [class.selectedIcon]="selectedItem === 'account_circle'"
              class="app-icon-section temporaryDisabled" 
              (click)="selectItem('account_circle')"
            >
              <mat-icon class="menuIcon not-allowed-cursor" matListIcon>account_circle</mat-icon>
            </mat-list-item>
            <mat-list-item 
              [disabled]="true"
              [class.selectedIcon]="selectedItem === 'info'"
              class="app-icon-section temporaryDisabled" 
              (click)="selectItem('info')"
            >
              <mat-icon class="menuIcon not-allowed-cursor" matListIcon>info</mat-icon>
            </mat-list-item>
          </mat-nav-list>
          <mat-nav-list class="app-section-nav-list">
            <mat-list-item 
              class="app-icon-section" 
              (click)="loadComponent('/mets-admininstrative')" 
              [class.selectedIcon]="isSelected('/mets-admininstrative')"
              matTooltip="Admin"
              matTooltipClass="tooltip"
              matTooltipPosition="right"
              matTooltipShowDelay="100"
              matTooltipHideDelay="200"
            >
              <mat-icon class="menuIcon" matListIcon>security</mat-icon>
            </mat-list-item>
            <mat-list-item class="app-icon-section" (click)="logout()" 
              matTooltip="Logout" 
              matTooltipClass="tooltip"
              matTooltipPosition="right"
              matTooltipShowDelay="100"
              matTooltipHideDelay="200"
            >
              <img style="width: 25px;" src="../../../assets/images/logout.png" id="logout" alt="logout" />
            </mat-list-item>
          </mat-nav-list>   
        </div>
        <div class="menu-text-section" style="opacity: 1 !important;" [ngClass]="{'openBehaviour': !isCollapsed}" *ngIf="!delayedHide">
          <mat-nav-list class="menu-section-nav-list">

            <mat-list-item #notificationPopover="ngbPopover"
                [ngbPopover]="notificationTemplate"
                popoverClass="custom-notification-popover"
                triggers="click"
                placement="right-top"
                container="body"
                (hidden)="onNotificationPopoverClosed()">
              <span class="menu-text display-center">Notifications</span>
            </mat-list-item>

            <mat-list-item (click)="loadComponent('/maintenance-event-list')" [class.selected-tab]="isSelected('/maintenance-event-list')">
              <span class="menu-text display-center">Maintenance Event List</span>
            </mat-list-item>
            <!-- <mat-list-item (click)="loadComponent('/maintenance-event-details')">
              <span class="menu-text">Maintenance Event Details</span>
            </mat-list-item> -->

            <mat-expansion-panel
              class="expansion-panel"
              [(expanded)]="isPanelExpanded"
              [class.selected-tab]="isSelected('/maintenance-event-details')">
              <mat-expansion-panel-header class="expansion-panel-header">
                <mat-panel-title class="expansion-panel-title">
                  Maintenance Event Details
                </mat-panel-title>
              </mat-expansion-panel-header>

              <!-- Fixed Button and ACN List -->
              <div class="fixed-add-button">
                <button mat-fab class="small-fab" (click)="addAcn()" matTooltip="Add ACN" matTooltipPosition="below">
                  <mat-icon>add</mat-icon> Add ACN
                </button>
              </div>

              <mat-list class="acn-list">
                <mat-list-item
                  class="mat-acn-list-item"
                  *ngFor="let acn of acnList"
                  (click)="selectAcn(acn, true)"
                  [class.selected]="acn.name === selectedAcn?.name"
                  [attr.data-acn]="acn.name"
                >
                  <!-- <span class="acn-name">{{ acn.name }}{{ " | " }}{{acn.eventType}}</span> -->
                  <span class="acn-name">{{ acn.name }}</span>
                  <button *ngIf="acn.userAdded" mat-icon-button class="delete-button" (click)="deleteAcn($event, acn)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </mat-list-item>
              </mat-list>
            </mat-expansion-panel>
            
            <mat-list-item class="temporaryMatListDisabled"  (click)="loadComponent('')" [class.selected-tab]="isSelected('/maintenance-event-inquiry')">
              <span class="menu-text not-allowed-cursor display-center">Maintenance Event Inquiry</span> 
            </mat-list-item>
            <mat-list-item class="temporaryMatListDisabled"  (click)="loadComponent('')" [class.selected-tab]="isSelected('/maintenance-event-reports')">
              <span class="menu-text not-allowed-cursor display-center">Maintenance Event Reports</span>
            </mat-list-item>
            <!-- <mat-list-item (click)="loadComponent('/add-event')" [class.selected-tab]="isSelected('/add-event')">
              <span class="menu-text display-center">Add Event</span>
            </mat-list-item> -->
          </mat-nav-list>
        </div>
      </div>

      
      </mat-sidenav>

      <mat-sidenav-content
        class="sidenav-content"
        [class.full-width]="isCollapsed"
      >
        <div class="content">
          <div class="toggle-icon" (click)="toggleSidebar(isCollapsed, false)">
            <div class="test">
              <mat-icon class="chevron_icon">{{ isCollapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
            </div>
          </div>
          <div [ngClass]="{'disableOverflow': (isEventListScreenSelected || isEventDetailsScreenSelected)}" class="event-content">
            <router-outlet></router-outlet>
          </div>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </div>