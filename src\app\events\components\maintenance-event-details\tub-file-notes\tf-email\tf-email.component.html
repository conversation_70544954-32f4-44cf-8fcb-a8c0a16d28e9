<div class="email-notes-wrapper">
    <h2 mat-dialog-title>Send Email</h2>

    <mat-dialog-content>
    <mat-form-field appearance="fill" style="width: 100%;">
        <mat-label>To</mat-label>
        <input matInput [(ngModel)]="email.to">
    </mat-form-field>

    <mat-form-field appearance="fill" style="width: 100%;">
        <mat-label>From</mat-label>
         <input matInput [value]="email.from" readonly>
    </mat-form-field>

    <mat-form-field appearance="fill" style="width: 100%;">
        <mat-label>Subject</mat-label>
        <input matInput [(ngModel)]="email.subject">
    </mat-form-field>

        <mat-form-field appearance="fill" style="width: 100%;">
        <mat-label>Date</mat-label>
        <input matInput [value]="currentDateTime" readonly>
        </mat-form-field>

      <quill-editor
            [modules]="quillModules"
            [(ngModel)]="data.tfNote"
            (ngModelChange)="onBodyChange($event)"
             [style.width.%]="100"
            [style.height.px]="300">
            </quill-editor>




        <!-- <mat-form-field appearance="fill" style="width: 100%;"> -->
            <!-- <mat-label>Body</mat-label>
            <quill-editor [(ngModel)]="body" [style]="{height: '200px'}"></quill-editor> -->
            <!-- <input matInput>
              <div class="html-textarea-display" [innerHTML]="body"></div> -->
        <!-- </mat-form-field> -->
    </mat-dialog-content>

    <mat-dialog-actions class="email-notes-wrapper-actions" align="end">
    <button mat-button mat-dialog-close class="orange-button">Cancel</button>
    <button mat-button class="orange-button" (click)="sendEmail()">send</button>
    </mat-dialog-actions>
</div>