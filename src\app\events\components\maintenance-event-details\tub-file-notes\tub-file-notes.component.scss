.notes-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f9fafb;
}

.sort-by-sticky {
  position: sticky;
  top: 10px;
  width: 98%;
  align-self: center;
  background: linear-gradient(180deg, #ffffff, #f1f5f9);
  padding: 5px 18px;
  margin: 5px 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  border-radius: 12px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.sort-by-sticky:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.sort-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #5b21b6;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.sort-radio-group {
  display: flex;
  gap: 16px;
}

.sort-radio {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.sort-radio ::ng-deep .mat-radio-checked .mat-radio-outer-circle {
  border-color: #7c3aed;
}

.sort-radio ::ng-deep .mat-radio-checked .mat-radio-inner-circle {
  background-color: #7c3aed;
}

.notes-scroll-container {
  flex: 1;
  overflow-y: auto;
  margin-top: 5px;
  padding-bottom: 20px;
}

.existing-notes-container {
  max-height: none;
  padding: 0 14px;
}

.existing-notes-container::-webkit-scrollbar {
  width: 6px;
}

.existing-notes-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 12px;
}

.existing-notes-container::-webkit-scrollbar-thumb {
  background: #a5b4fc;
  border-radius: 12px;
}

.existing-notes-container::-webkit-scrollbar-thumb:hover {
  background: #818cf8;
}

.compact-note-card {
  padding: 5px 10px;
  margin: 8px 0;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 16px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.compact-note-card:hover {
  background: linear-gradient(145deg, #f0f0ff, #ede9fe);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.15);
}

.compact-note-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  border-radius: 12px 0 0 12px;
  background: linear-gradient(180deg, #7c3aed, #FF7518);
  transition: width 0.3s ease;
}

.compact-note-card:hover::before {
  width: 8px;
}

.note-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.note-text-container {
  background: rgba(139, 92, 246, 0.04);
  padding: 10px 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.note-heading {
  font-size: 1rem;
  color: #FF7518;
  line-height: 1.5;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.note-text {
  font-size: 0.95rem;
  color: #1e3a8a;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.note-meta-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  background: rgba(139, 92, 246, 0.05);
  padding: 8px 10px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.note-meta-content:hover {
  background: rgba(139, 92, 246, 0.08);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.7rem;
  color: #374151;
}

.meta-icon {
  color: #7c3aed;
  font-size: 12px;
}

.emp-name {
  color: #1f2a44;
  font-weight: 500;
  font-size: 0.65rem;
}

.update-time {
  color: #6b7280;
  font-style: italic;
  font-weight: 400;
  font-size: 0.65rem;
}

.no-notes {
  color: #6b7280;
  font-size: 1rem;
  font-style: italic;
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  margin: 16px 0;
  border: 1px solid rgba(139, 92, 246, 0.08);
}

.selectable {
  user-select: none;
}

.selected-card {
  background: linear-gradient(145deg, #fae4d6, #fae4d6) !important;
  border: 2px solid #a5b4fc !important;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.2);
  transform: scale(1.01);
}

.selected-card::before {
  width: 8px;
  background: linear-gradient(180deg, #6a5acd, #d97706);
}
.preserve-linebreaks {
  white-space: pre-line;
}
