.row1{
    display: flex;
    gap: 10px;
    flex-direction: row;
    width: 90%;
    height: 14%;
    pointer-events: none;
    align-items: center;
}
// mat-form-field {
//     width: fit-content;
// }
// .row-1{
//     pointer-events: none;
//     display: flex;
//     flex-direction: row;
// }
.header {
    display: flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 9%;
    align-items: center;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    color: white; /* Change transparent to a visible color */
    justify-content: right;
}

.mat-card {
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    position: relative;
    width: 60%;
    gap: 15px;
    height: 100%;
    flex-direction: row;
    border: 0;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.etic-comment-mat-card {
    gap: 4%;
    height: 15%;
    flex-direction: row; 
    border: 0;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.reason-mat-card-content {
    width: 85%;
    display: flex;
    justify-content: center;
}

.mat-card-title {
    display: flex;
    font-size: 14px;
    font-weight: 500;
    align-items: center;
}

.etic-mat-card-title {
    display: flex;
    font-size: 14px;
    font-weight: 500;
    align-items: center;
    margin-left:6%;
    width:44%
}

.mat-card-content {
    display: inline-flex;
    padding: 0;
    font-size: 15px;
    font-weight: bold;
    align-items: center;
    color:rgb(163 120 120);
}

.etic-mat-card-content {
    display: inline-flex;
    padding: 0;
    font-size: 15px;
    font-weight: bold;
    align-items: center;
    color:rgb(163 120 120);
}

.mat-form-field {
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.max-width-15 {
    max-width: 15% !important;
}

.max-width-25 {
    max-width: 25% !important;
}
.max-width {
    max-width: fit-content !important;
}
.flex-auto {
    flex: auto;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 100%;
    bottom: 0;
    left: 0;
    background-color: rgb(237, 219, 219);
    animation: fillBackground 4.5s forwards;
}

@keyframes fillBackground {
    0% {
        right: 100%;
    }
    100% {
        right: 0;
    }
}

  .header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 100%;
    bottom: 0;
    left: 0;
    // background-color: rgb(237, 219, 219);
    background-color: rgb(188, 154, 154);
    animation: fillBackground 5s forwards;
  }
  @keyframes fillBackground {
    0% {
      right: 100%;
    }
    100% {
      right: 0;
    }
  }
  
.maintenance-oos-event-container{
    margin : 2% 0% 0 12%;
    display: flex;
    flex-direction: column;
    height: 93%;
    gap: 10px;
    border-radius: 8px;
    padding: 20px;
    background-color: whitesmoke;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease-in-out;
    justify-content: space-between;
}

.maintenance-oos-event-container:hover{
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
}

.row2{
    display: inline-flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 15%;
    pointer-events: none;
    align-items: center;
    //border-style: dotted;
}

::ng-deep .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input {
    color: rgb(163 120 120) !important;
    font-size: 14px !important;
    font-weight: bold !important;
}

::ng-deep .mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label {
    font-weight: 500 !important;
}


// mat-form-field mdc-line-ripple {
//     /* Default color when no interaction occurs */
//    display: none;
//   }

//   ::ng-deep .mat-mdc-form-field-subscript-wrapper,
::ng-deep .mdc-line-ripple {
    display: none !important;
}

::ng-deep .mat-mdc-select-value-text {
    color: rgb(163 120 120) !important;
    font-size: 14px !important;
    font-weight: bold !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}
::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color:#faf9fd;
}

.row3{
    display: inline-flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 20%;
}

.field3-row6 {
    min-width: 49% !important;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2) !important;    
}

.row0{
    display: inline-flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 15%;
    //border-style: dotted;
    align-items: center;
}

.row4{
    display: inline-flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 15%;
    //border-style: dotted;
    align-items: center;
}

.row5{
    display: inline-flex;
    gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 19%;
    justify-content: right;
    //border-style: dotted;
}

.row2-5{
    display: inline-flex;
    // gap: 10px;
    flex-direction: row;
    width: 100%;
    height: 65%;
    //border-style: dotted;
}
.row2-5-container{
    display: inline-flex;
    gap: 10px;
    flex-direction: column;
    width: 56%;
    height: 100%;
    justify-content: space-around;
    //border-style: dotted;
}
.comments-container{
    display: inline-flex;
    gap: 6%;
    margin-left: 3%;
    flex-direction: column;
    width: 45%;
    height: 100%;
    justify-content: end;
    //border-style: dotted;
}
.row6{
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
    width: 100%;
    height: 14%;
    gap:2%;
    align-items: center;
    // border-style: dotted;
}
.row6-container{
    display: flex;
    gap: 10px;
    flex-direction: row;
    width: 70%;
    height: 100%;
}
.row7{
    display: flex;
    gap: 10px;
    flex-direction: column;
    width: 40%;
    height: 14%;
    //border-style: dotted;
}

.button-container button:not(:disabled) {
    background-color: rgb(175, 146, 146) !important;
    color: white !important;
    border-radius: 10px;
    padding: 5px 30px;
}

.button-container button:not(:disabled):hover {
    background-color: #de9c9c !important;
    color: white !important;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.button-container button:disabled{
    pointer-events: auto;
    cursor: not-allowed !important;
}

.mat-button1 {
    border-radius: 10px;
    padding: 5px 30px;
}


.action-button-container button {
    background-color: rgb(175, 146, 146) !important;
    color: white !important;
    border-radius: 10px;
    padding: 5px 30px;
}

.action-button-container button:hover{
    background-color: #de9c9c !important;
    color: white !important;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}
#start-card:hover.rotate30{
    
        transform: rotate3d(1, 2, 1, 5deg);
      
}


  