<mat-sidenav-container class="event-detail">
  <mat-sidenav-content class="sidenav-content">
    <div class="event_details card">
      <div class="navbar-row-1">
        <mat-form-field class="event-detail-form-field">
          <mat-label style="font-size: 16px;font-weight: bold;">ACN</mat-label>
          <input matInput style="background-color: #6d4db6 !important;color: white !important;" name="Acn" [(ngModel)]="acn" (keydown.enter)="getDetails()">
          <button mat-icon-button matSuffix (click)="getDetails()" style="opacity: 0.8; position: relative; top:8px;">
            <mat-icon style="color: #FF7518; font-weight: bold;">search</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Station</mat-label>
          <mat-select [(ngModel)]="station" (ngModelChange)="onStationChange()">
            <mat-option disabled value="">-</mat-option>
            <mat-option *ngFor="let fetchedStation of stationsList" [value]="fetchedStation">
              {{ fetchedStation }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Gate</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.gate || '-' : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Type</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.eventType || '-' : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Fleet</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.eventFleetDesc || '-' : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Etic</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.eventEticDateTime  || '-' : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Duration</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.duration  || '-' : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Owner</mat-label>
          <input matInput placeholder="" value="LINE" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Responsible Mgr</mat-label>
          <input matInput value="373085" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Contact</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.contact  || '-' : '-'" readonly>
        </mat-form-field>
      </div>
      <div class="navbar-row-2">
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Tire/Daily:</mat-label>
          <input matInput placeholder="" [value]="aircraftInfoChecks.serviceWarningDays.tireDaily != null ? aircraftInfoChecks.serviceWarningDays.tireDaily : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Sec Chk</mat-label>
          <input matInput placeholder="" [value]="aircraftInfoChecks.serviceWarningDays.securityChk != null ? aircraftInfoChecks.serviceWarningDays.securityChk : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Service Chk</mat-label>
          <input matInput placeholder="" [value]="aircraftInfoChecks.serviceWarningDays.serviceChk != null ? aircraftInfoChecks.serviceWarningDays.serviceChk : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>FOB</mat-label>
          <input matInput placeholder="" [value]="aircraftInfoChecks.fob != null ? aircraftInfoChecks.fob : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>EMR STATUS</mat-label>
          <input matInput placeholder="" #emrStatusContent [value]="aircraftInfoChecks.emrStatus != null ? aircraftInfoChecks.emrStatus : '-'" readonly>
        </mat-form-field>
        <mat-form-field class="event-detail-form-field" appearance="fill">
          <mat-label>Super Comment</mat-label>
          <input matInput placeholder="" [value]="detailViewData ? detailViewData.eventCurrentComment  || '-' : '-'" readonly style="font-weight: bold; text-transform: uppercase;">
        </mat-form-field>
      </div>
    </div>

    <div class="scroll-container">
      <!-- First screen with 2 quadrants -->
      <div class="event-detail-container" [style.gridTemplateColumns]="gridTemplateColumns">
        <div class="quadrant" #quadrant1>
          <div class="quadrant-header-wrapper">
            <div class="quadrant-header">Tub File Notes</div>
          </div>
          <div class="quadrant-content">
            <app-tub-file-notes class="content-wrapper" *ngIf="eventId" [detailsViewObj]="detailViewData" (allNotesChanged)="onAllNotesReceived($event)" (noteSelected)="onNoteSelected($event)"></app-tub-file-notes>
          </div>
          <div class="quadrant-footer">
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" (click)="openEditor()">
              <mat-icon>add</mat-icon>Add
            </button>
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" [disabled]="!isNoteSelected" (click)="openEmailFlow()">
              <mat-icon>email</mat-icon>Email
            </button>
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" [disabled]="!isNoteSelected" (click)="openEditDialog()">
              <mat-icon>edit</mat-icon>Edit
            </button>
          </div>
        </div>
        <div class="quadrant" #quadrant4>
          <div class="quadrant-header-wrapper" style="display: flex; justify-content: space-between; align-items: center; padding: 4px 8px;">
            <div class="quadrant-header">Manager Notes</div>
          </div>
          <button mat-icon-button #popout_btn aria-label="Open Popout" (click)="openManagerNotesCapture()" style="position: absolute; top: -8px; right: 8px; z-index: 10; color: #a56cc1 !important;">
            <mat-icon>open_in_new</mat-icon>
          </button>
          <div class="quadrant-content">
            <div class="manager-notes-container" style="height: 100%;">
              <textarea id="manager-notes-text" style="width: 100%; height: 99%; resize: none; box-sizing: border-box; border-radius: 8px; padding: 0.8%; font-size: 13px;" [(ngModel)]="mgrNotes" [disabled]="isMgrNotesDisabled"></textarea>
            </div>
          </div>
          <div class="quadrant-footer">
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" (click)="addManagerNote(true)">
              <mat-icon>edit</mat-icon>Edit
            </button>
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" (click)="disableManagerNote()">
              <mat-icon>undo</mat-icon>Cancel
            </button>
            <button mat-flat-button class="tiny-fab" aria-label="Icon button" (click)="updateEventDetails()">
              <mat-icon>done</mat-icon>Submit
            </button>
          </div>
        </div>
        <div class="resize-handle vertical resize-handle-first" (mousedown)="startResize($event, 'vertical')" [style.left]="getVerticalHandlePosition()"></div>
      </div>

      <!-- Second screen with 4 quadrants -->
      <div class="second-screen-wrapper">
        <div class="second-screen" [style.gridTemplateColumns]="secondScreenGridColumns" [style.gridTemplateRows]="gridTemplateRows">
          <div class="quadrant" #quadrant3>
            <div class="quadrant-header-wrapper">
              <div class="quadrant-header">Discrepancies</div>
            </div>
            <div class="quadrant-content">
              <app-discrepancies [isAddEventSelected]="false" [detailsViewObj]="detailViewData" (detailsDiscrepanciesData)="updateDiscrepanciesButtons($event)"></app-discrepancies>
            </div>
            <div class="quadrant-footer">
              <button [disabled]="!isDiscrepanciesUpdateEnabled" mat-flat-button class="tiny-fab" (click)="updateDiscrepancies()">
                <mat-icon>update</mat-icon>Update
              </button>
              <button [disabled]="!isDiscrepanciesViewEnabled" mat-flat-button class="tiny-fab" (click)="viewDiscrepancies()">
                <mat-icon>visibility</mat-icon>View
              </button>
            </div>
          </div>
          <div class="quadrant" #quadrant2>
            <div class="quadrant-header-wrapper">
              <div class="quadrant-header">Intake Forms</div>
            </div>
            <div class="quadrant-content">
              <app-intake-form [isAddEventSelected]="false" (detailEventIntakeFormData)="updateIntakeFormsButtons($event)"></app-intake-form>
            </div>
            <div class="quadrant-footer">
              <button mat-flat-button color="accent" [disabled]="!isIntakeFormUpdateEnabled" class="tiny-fab" aria-label="Icon button" (click)="updateIntakeForm()">
                <mat-icon>update</mat-icon>Update
              </button>
              <button mat-flat-button color="accent" [disabled]="!isIntakeFormUpdateEnabled" class="tiny-fab" aria-label="Icon button" (click)="cancelIntakeFormsUpdate()">
                <mat-icon>close</mat-icon>Cancel
              </button>
            </div>
          </div>
          <div class="quadrant" #secondQuadrant1>
            <div class="quadrant-header-wrapper">
              <div class="quadrant-header">NIW Timers</div>
            </div>
            <div class="quadrant-content">
              <app-niw-timers [detailsViewObj]="detailViewData"></app-niw-timers>
            </div>
            <div class="quadrant-footer">
              <button mat-flat-button class="tiny-fab" aria-label="Icon button">
                <mat-icon>add</mat-icon>Add
              </button>
            </div>
          </div>
          <div class="quadrant" #secondQuadrant2>
            <div class="quadrant-header-wrapper">
              <div class="quadrant-header">Reporting Categories</div>
            </div>
            <div class="quadrant-content">
              <app-reporting-categories [detailsViewObj]="detailViewData"></app-reporting-categories>
            </div>
            <div class="quadrant-footer">
              <button mat-flat-button class="tiny-fab" aria-label="Icon button">
                <mat-icon>update</mat-icon>Update
              </button>
            </div>
          </div>
          <div class="resize-handle vertical resize-handle-second" (mousedown)="startResize($event, 'vertical-second')" [style.left]="getSecondVerticalHandlePosition()"></div>
          <div class="resize-handle horizontal resize-handle-first" (mousedown)="startResize($event, 'horizontal')" [style.top]="getHorizontalHandlePosition()"></div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>

    <mat-sidenav class="custom-sidenav-panel" #sidePanel mode="over" position="end" [opened]="activePanel !== null" (closedStart)="activePanel = null">
    <div class="panel-container">
      <ng-container *ngIf="activePanel === 'ers'">
        <div class="panel-content note-box">
          <div class="panel-header">
            <span class="panel-title">ERS</span>
          </div>
          <div class="panel-content">
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="activePanel === 'fs'">
        <div class="panel-content note-box">
          <div class="panel-header">
            <span class="panel-title">Flight Schedule</span>
          </div>
          <div class="flt-panel-content">
            <div class="flight-table-container">
              <table *ngIf="inboundFlight && eventFlightEticDetails" class="mat-table-custom full-height-table">
                <thead>
                  <tr style="height: 55px;">
                    <th style="font-size: 14px; font-weight: bold;">Category</th>
                    <th style="font-size: 14px; font-weight: bold;">Flight</th>
                    <th style="font-size: 14px; font-weight: bold;">Ori/Dep</th>
                    <th style="font-size: 14px; font-weight: bold;">Dest/Arr</th>
                  </tr>
                </thead>
                <tbody class="flight-schedule-body">
                  <tr class="inbound-row">
                    <td class="merged-category">Inbound</td>
                    <td class="inbound">{{ inboundFlight.flightNumber || '-' }}/{{ inboundFlight.legNumber || '-' }} - {{ eventFlightEticDetails.flightDate || '-' }}</td>
                    <td class="inbound">{{ inboundFlight.legOrigin || '-' }}/{{ inboundFlight.legDepartureTime || '-' }}</td>
                    <td class="inbound">{{ inboundFlight.legDestination || '-' }}/{{ inboundFlight.legArrivalTime || '-' }}</td>
                  </tr>
                  <tr class="outbound-row">
                    <td class="merged-category">Affected Outbound</td>
                    <td class="outbound">{{ eventFlightEticDetails.flightNumber || '-' }}/{{ eventFlightEticDetails.flightLegNumber || '-' }} - {{ eventFlightEticDetails.flightDate || '-' }}</td>
                    <td class="outbound">{{ eventFlightEticDetails.origin || '-' }}/{{ eventFlightEticDetails.departure || '-' }}</td>
                    <td class="outbound">{{ eventFlightEticDetails.destination || '-' }}/{{ eventFlightEticDetails.arrival || '-' }}</td>
                  </tr>
                  <ng-container *ngIf="upcomingFltList.length > 0" class="actual-outbound-group">
                    <tr class="actual-outbound-row actual-outbound-first">
                      <td rowspan="2" class="merged-category actual-outbound-merged">Actual Outbound</td>
                      <td class="actual">
                        {{ upcomingFltList[0].flightNumber || '-' }}/{{ upcomingFltList[0].legNumber || '-' }} - {{ upcomingFltList[0].flightDate || '-' }}
                      </td>
                      <td class="actual">
                        {{ upcomingFltList[0].legOrigin || '-' }}/{{ upcomingFltList[0].legDepartureTime || '-' }}
                      </td>
                      <td class="actual">
                        {{ upcomingFltList[0].legDestination || '-' }}/{{ upcomingFltList[0].legArrivalTime || '-' }}
                      </td>
                    </tr>
                    <tr class="actual-outbound-row actual-outbound-second">
                      <td class="actual">
                        {{ upcomingFltList[1].flightNumber || '-' }}/{{ upcomingFltList[1].legNumber || '-' }} - {{ upcomingFltList[1].flightDate || '-' }}
                      </td>
                      <td class="actual">
                        {{ upcomingFltList[1].legOrigin || '-' }}/{{ upcomingFltList[1].legDepartureTime || '-' }}
                      </td>
                      <td class="actual">
                        {{ upcomingFltList[1].legDestination || '-' }}/{{ upcomingFltList[1].legArrivalTime || '-' }}
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="activePanel === 'parts'">
        <div class="panel-content note-box">
          <div class="panel-header note-header">
            <span class="panel-title note-title">Parts</span>
            <button *ngIf="partsList && partsList.length > 0" mat-icon-button class="popout-btn" aria-label="Open Popout" (click)="openMsnDetailedInfo(null)">
              <mat-icon style="display: flex; color: white;">open_in_new</mat-icon>
            </button>
          </div>
          <div class="table-scroll-wrapper">
            <ng-container *ngIf="partsList && partsList.length > 0; else noData">
              <table mat-table [dataSource]="partsList" class="custom-parts-table">
                <ng-container matColumnDef="msn">
                  <th mat-header-cell *matHeaderCellDef>MSN</th>
                  <td mat-cell *matCellDef="let element" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedPart === element}">{{ element.msn }}</td>
                </ng-container>
                <ng-container matColumnDef="quantity">
                  <th mat-header-cell *matHeaderCellDef>CPN Qty</th>
                  <td mat-cell *matCellDef="let element" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedPart === element}">{{ element.cpnQty }}</td>
                </ng-container>
                <ng-container matColumnDef="description">
                  <th mat-header-cell *matHeaderCellDef>Description</th>
                  <td mat-cell *matCellDef="let element" (click)="onRowClick(element)" [ngClass]="{'selected-row': selectedPart === element}">{{ element.cpnDescription }}</td>
                </ng-container>
                <tr mat-header-row *matHeaderRowDef="['msn', 'quantity', 'description']"></tr>
                <tr mat-row *matRowDef="let row; columns: ['msn', 'quantity', 'description']"></tr>
              </table>
            </ng-container>
            <ng-template #noData>
              <div class="no-data-message">No parts ordered</div>
            </ng-template>
          </div>
        </div>
      </ng-container>
    </div>
  </mat-sidenav>
</mat-sidenav-container>
<div class="right-sidebar">
  <div class="sidebar-tab" style="border-radius: 20px 0 0 0;" (click)="openPanel('ers')" [class.active]="activePanel === 'ers'">
    <mat-icon>engineering</mat-icon>ERS
  </div>
  <div class="sidebar-tab" (click)="openPanel('fs')" [class.active]="activePanel === 'fs'">
    <mat-icon>flight</mat-icon>Flight Schedule
  </div>
  <div class="sidebar-tab" style="border-radius: 0 0 0 20px;" (click)="openPanel('parts')" [class.active]="activePanel === 'parts'">
    <mat-icon>build</mat-icon>Parts
  </div>
</div>