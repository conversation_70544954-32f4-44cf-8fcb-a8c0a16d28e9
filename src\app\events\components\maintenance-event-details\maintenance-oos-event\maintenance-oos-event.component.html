<div class="maintenance-oos-event-container">
    <!-- <div class="row1"> -->
    <!-- <mat-form-field class="field1-row1">
            <mat-label>Status:</mat-label>
            <textarea matInput value={{detailsViewObj.eventStatus}}></textarea>
        </mat-form-field> -->
    <!-- <mat-card appearance="outlined" style="width: 38%;gap: 4%;height: 60%;flex-direction: row; border: 0;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
            <mat-card-title style="display: flex;font-size: 13px;align-items: center;margin-left: 14px;width: 20%;">Start</mat-card-title>
            <mat-card-content style="display: inline-flex;padding: 0;font-size: 15px;align-items: center;color:rgb(163 120 120);width:69%">{{detailsViewObj.startDateTime}}</mat-card-content>
        </mat-card> -->
    <!-- <mat-form-field class="field2-row1">
            <mat-label>Etic</mat-label>
            <textarea matInput value={{detailsViewObj.eventEticDateTime}}></textarea>
        </mat-form-field> -->
    <!--Todo should be etic date time-->
    <!-- <mat-card appearance="outlined" style="width: 30%;gap: 4%;height: 60%;flex-direction: row; border: 0;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
            <mat-card-title style="display: flex;font-size: 13px;align-items: center;margin-left:14px;width:20%">Etic</mat-card-title>
            <mat-card-content style="display: inline-flex;padding: 0;font-size: 15px;align-items: center;color:rgb(163 120 120);width:69%">{{detailsViewObj.eventEticDateTime}}</mat-card-content>
        </mat-card> -->
    <!-- <mat-form-field class="field3-row1">
            <mat-label>Ost</mat-label>
        </mat-form-field> -->
    <!-- <mat-card appearance="outlined" style="width: 30%;gap: 4%;height: 60%;flex-direction: row; border: 0;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
            <mat-card-title style="display: flex;font-size: 13px;align-items: center;margin-left:14px;width:20%">reason</mat-card-title>
            <mat-card-content style="display: inline-flex;padding: 0;font-size: 15px;align-items: center;color:rgb(163 120 120);width:69%">{{detailsViewObj.eventEticReasonComment}}</mat-card-content>
        </mat-card> -->
    <!-- </div> -->
     <div class="header">
        <h1 style="font-size: 1.4rem; z-index: 1;margin-right: 2%;">OOS EVENT</h1>
     </div>
    <div class="row2-5">
        <div class="row2-5-container">
            <div class="row0">
                <!-- <mat-form-field class="field1-row4" style="pointer-events: none;">
                    <mat-label>Duration</mat-label>
                    <textarea matInput value={{detailsViewObj.duration}}></textarea>
                </mat-form-field> -->
                <mat-card appearance="outlined" id="start-card" class="mat-card">
                    <mat-card-title class="mat-card-title">Start:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.startDateTime || ""}}</mat-card-content>
                </mat-card>
                <!-- <mat-form-field class="field2-row4">
                    <mat-label>Ost</mat-label>
                    <textarea matInput value={{detailsViewObj.eventOST}}></textarea>
                </mat-form-field> -->
                <mat-card appearance="outlined" class="mat-card">
                    <mat-card-title class="mat-card-title">Etic:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventEticDateTime || ""}}</mat-card-content>
                </mat-card>
                <mat-card appearance="outlined" class="mat-card max-width-25">
                    <mat-card-title class="mat-card-title">Status:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventStatus || ""}}</mat-card-content>
                </mat-card>
                <mat-card appearance="outlined" class="mat-card max-width-15">
                    <mat-card-title class="mat-card-title">Ost:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventOST || ""}}</mat-card-content>
                </mat-card>
            </div>
            <div class="row4">
                <!-- <mat-form-field class="field1-row4" style="pointer-events: none;">
                    <mat-label>Duration</mat-label>
                    <textarea matInput value={{detailsViewObj.duration}}></textarea>
                </mat-form-field> -->
                <mat-card appearance="outlined" class="mat-card max-width">
                    <mat-card-title class="mat-card-title">Duration:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.duration || ""}}</mat-card-content>
                </mat-card>
                <mat-card appearance="outlined" class="mat-card flex-auto">
                    <mat-card-title class="mat-card-title">Reason:</mat-card-title>
                    <mat-card-content class="mat-card-content reason-mat-card-content">{{detailsViewObj.eventEticReasonComment || ""}}</mat-card-content>
                </mat-card>
                <!-- <mat-form-field class="field2-row4">
                    <mat-label>Ost</mat-label>
                    <textarea matInput value={{detailsViewObj.eventOST}}></textarea>
                </mat-form-field> -->
                <!-- <mat-card appearance="outlined" class="mat-card">
                    <mat-card-title class="mat-card-title">Ost</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventOST}}</mat-card-content>
                </mat-card> -->
            </div>
            <!-- <div class="row2">
                <mat-form-field class="field1-row2">
                    <mat-label>reason</mat-label>
                    <textarea matInput value={{detailsViewObj.eventEticReasonCd}}></textarea>
                </mat-form-field>
                <mat-card appearance="outlined" class="mat-card">
                    <mat-card-title class="mat-card-title">Status</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventStatus}}</mat-card-content>
                </mat-card>
                <mat-form-field class="field2-row2">
                    <mat-label>Etic Comment</mat-label>
                    <textarea matInput value="{{detailsViewObj.eventEticText}}"></textarea>
                </mat-form-field>
                <mat-card appearance="outlined" class="mat-card">
                    <mat-card-title class="mat-card-title">Reason:</mat-card-title>
                    <mat-card-content class="mat-card-content">{{detailsViewObj.eventEticReasonComment}}</mat-card-content>
                </mat-card>
            </div> -->
            <div class="row3">
                <mat-form-field class="field3-row6">
                    <mat-label>Responsible Mgr</mat-label>
                    <textarea matInput value={{detailsViewObj.resMgrId}}></textarea>
                </mat-form-field>
                <mat-form-field class="field3-row6">
                    <mat-label>Contact</mat-label>
                    <textarea matInput [(ngModel)]="contactValue" (ngModelChange)="onContactChange()">{{detailsViewObj.contact || ""}}</textarea>
                </mat-form-field>
            </div>
            <div class="row5">
                <mat-form-field class="mat-form-field">
                    <!-- <mat-select [(ngModel)]="input" placeholder="Owner" name="owner" required> -->
                    <mat-label>Owner</mat-label>
                    <mat-select [(value)]="ownerValue" [(ngModel)]="ownerValue" (ngModelChange)="onOwnerChange()">
                        @for (ownerName of detailsViewObj.contactInfoOwnerList; track ownerName) {
                        <mat-option [value]="ownerName">{{ownerName}}</mat-option>
                        }
                    </mat-select>
                </mat-form-field>
                <mat-form-field class="mat-form-field">
                    <mat-label>Station</mat-label>
                    <mat-select [(ngModel)]="station" [(value)]="station" (ngModelChange)="onStationChange()">
                        @for (fetchedStation of fetchedSationList; track fetchedStation) {
                            <mat-option [value]="fetchedStation">{{fetchedStation}}</mat-option>
                            }
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <div class="comments-container">
            <mat-card appearance="outlined" class="etic-comment-mat-card">
                <mat-card-title class="etic-mat-card-title">Etic Comment:</mat-card-title>
                <mat-card-content class="etic-mat-card-content">{{detailsViewObj.eventEticText}}</mat-card-content>
            </mat-card>
            <mat-form-field class="field3-row6" style="height:30%;">
                <mat-label>Manager Notes</mat-label>
                <textarea matInput [(ngModel)]="mgrNotes" (ngModelChange)="onMgrNotesChange()"></textarea>
            </mat-form-field>
            <mat-form-field class="field3-row6" style="height:40%;pointer-events: none;">
                <mat-label>Comment</mat-label>
                <textarea matInput value={{detailsViewObj.eventCurrentComment}}></textarea>
            </mat-form-field>
        </div>
    </div>
    <div class="row6">
        <!-- <div class="row6-container">
            <mat-form-field class="field1-row6">
                <mat-label>Contact</mat-label>
                <textarea matInput [(ngModel)]="contactValue" (ngModelChange)="onContactChange()"></textarea>
            </mat-form-field>
            <mat-form-field class="field2-row6">
                <mat-label>MEM Desk Contact</mat-label>
                <textarea matInput value={{detailsViewObj.memDeskContact}}></textarea>
            </mat-form-field>
            <mat-form-field class="field3-row6">
                <mat-label>Responsible Mgr</mat-label>
                <textarea matInput value={{detailsViewObj.resMgrId}}></textarea>
            </mat-form-field>
        </div> -->
        <div class="button-container">
            <button mat-raised-button class="mat-button1" [disabled]="isUpdateButtonDisabled"
                (click)="update()">Update</button>
        </div>
        <div class="action-button-container">
            <button mat-raised-button class="mat-button1"(click)="action()">Action</button>
        </div>
    </div>
    <!-- <div class="row7">
        <button mat-raised-button class="mat-button1" >Update</button>
    </div> -->
    <!-- <div class="row3" style="height: 14%;"> -->
    <!-- <mat-form-field class="field1-row3" style="height:10vh;">
            <mat-label>Comment</mat-label>
            <textarea matInput></textarea>
        </mat-form-field> -->
    <!-- </div> -->
    <!-- <mat-form-field class="field3-row6" style="height:100px;">
        <mat-label>Manager Notes</mat-label>
        <textarea matInput value={{detailsViewObj.managerNote}}></textarea>
    </mat-form-field> -->
</div>