// .container {
//     min-width: 100%;
//     margin: 0;
//     padding: 0;
// }

.container {
    position: relative;
    padding: 0 5px;
    min-width: max-content; // Let it grow as wide as needed
    width: auto;             // Don't restrict to 100%
    max-width: none;
}

/* Make the whole dialog resizable */
.resizable-dialog .mat-mdc-dialog-container {
    resize: both; /* Allows horizontal & vertical resizing */
    overflow: auto; /* Ensures content is visible while resizing */
    min-width: 300px; /* Prevents too much shrinking */
    min-height: 200px;
    max-width: 90vw; /* Prevents exceeding viewport width */
    max-height: 90vh; /* Prevents exceeding viewport height */
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
}

/* Title Styling - Extreme Left & Interactive */
.title {
    flex: 0 0 80%; /* Occupies 80% of the space */
    margin: 0;
    text-align: left;
    padding: 6px 20px;
    color: white;
    font-size: 18px;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(135deg, rgb(145, 115, 115), rgb(175, 146, 146));
    background-size: 0% 100%;
    background-repeat: no-repeat;
    opacity: 1;
}

/* This class is added dynamically for animation */
.title.fill-animation {
    animation: fillBackground 2s ease-in-out forwards;
}

/* Keyframes to animate background fill */
@keyframes fillBackground {
    0% {
        background-size: 0% 100%; /* Start empty */
    }
    100% {
        background-size: 100% 100%; /* Fully filled */
    }
}

.table-container {
    position: relative;
    padding: 7px 0;
    margin: 0;
    width: auto;
    overflow-x: auto;
}

.ag-grid-table {
    border-radius: 10px !important;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.ag-grid-table {
  width: 100%;
  --ag-selected-row-background-color: #fae4d6 !important;
}

::ng-deep .ag-grid-table .ag-row-hover {
  cursor: pointer;
  background-color: #fae4d6 !important;
}

::ng-deep .ag-grid-table .ag-row-hover .ag-cell {
  cursor: pointer;
  background-color: #fae4d6 !important;
}

.ag-theme-alpine {
    --ag-checkbox-checked-color: rgb(175, 146, 146) !important; /* Change checkmark color */
}

:host ::ng-deep .ag-pinned-right-header {
//   background-color: lightgray !important;
    // background: linear-gradient(135deg, #3F2876, #6c49b9);
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    color: white !important;
}

.ag-theme-alpine .ag-cell {
    white-space: normal !important;
    word-wrap: break-word;
    line-height: 1.4; /* Adjust for better spacing */
} 

::ng-deep .ag-header-container, 
::ng-deep .ag-floating-top-container, 
::ng-deep .ag-floating-bottom-container, 
::ng-deep .ag-sticky-top-container, 
::ng-deep .ag-sticky-bottom-container {
//   background-color: lightgray !important;
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    // background: linear-gradient(135deg, #6c49b9, #6c49b9);
    color: white !important;
//   background-color: #D8C1F1 !important;
//   color: white !important;
}

:host ::ng-deep .ag-header-cell-resize {
  background-color: transparent !important; // parent transparent, child handles the color
  width: 1.5px !important;
  height: 16px !important; /* smaller height */
  top: 50% !important;
  transform: translateY(-50%) !important; /* center vertically */
  right: 0;
  position: absolute;
  z-index: 10;
  opacity: 1 !important;
  pointer-events: auto !important; /* ensures it's draggable */
}

:host ::ng-deep .ag-header-cell-resize::before {
  content: '';
  display: block;
  background-color: white;
  width: 100%;
  height: 100%;
  border-radius: 2px; /* optional: soft edges */
}

::ng-deep .ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    white-space: normal !important;
    text-wrap: break-word !important;
    overflow: visible !important;
  }

// .button-container {
//     position: relative;
//     top: 2px;
//     display: flex;
//     justify-content: flex-end;
//     width: 100%;
//     gap: 5px;
//     padding: 5px 25px;
// }

.button-container {
    display: flex;
    flex: 0 0 20%; /* Occupies 20% of the space */
    justify-content: space-evenly;
    gap: 5px;
    padding: 5px;
    overflow: hidden; /* Ensures fill effect stays inside */
    // background: linear-gradient(135deg, #6f6b6d, #8f8b8d);
}

.add-button-container {
    display: flex;
    position: relative;
    left: 90%;
    float: right;
    flex: 0 0 10%;
    justify-content: space-evenly;
    gap: 5px;
    padding: 5px;
    overflow: hidden;
}

/* This class is added dynamically for animation */
// .button-container.fill-animation {
//     animation: fillBackground 2s ease-in-out forwards;
// }

.button-container button:disabled {
    box-shadow: none;
    background-color: lightgrey !important;
    cursor: not-allowed !important;
}

.add-button-container button:disabled {
    box-shadow: none;
    background-color: lightgrey !important;
    cursor: not-allowed !important;
}
  
.button {
    background-color: rgb(175, 146, 146) !important;
    color: white !important;
    border-radius: 10px;
    min-width: 90px;
    height: 30px;
    font-size: 12px;
    padding: 4px 12px;
    margin-left: 8px;
}

.button:disabled {
    box-shadow: none;
    pointer-events: all !important;
    cursor: not-allowed !important;
}

.button:not(:disabled):hover{
    background-color: #de9c9c !important;
    color: white !important;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

::ng-deep .ag-header-cell-label {
    display: flex;
    justify-content: center;
    align-items: center;
}

::ng-deep .ag-ltr .ag-cell {
    display: flex;
    justify-content: center;
    align-items: center;
}

:host ::ng-deep .ag-root-wrapper.ag-layout-normal {
    border-radius: 10px;
}

:host ::ng-deep .ag-header-container, 
:host ::ng-deep .ag-floating-top-container, 
:host ::ng-deep .ag-floating-bottom-container, 
:host ::ng-deep .ag-sticky-top-container, 
:host ::ng-deep .ag-sticky-bottom-container {
    background-color: lightgray;
}