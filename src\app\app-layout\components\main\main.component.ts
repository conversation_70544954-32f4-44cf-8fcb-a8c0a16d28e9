import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON>ement<PERSON>ef, HostListener, NgZone, OnDestroy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { AppLayoutService } from '../../services/app-layout.service';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { MainService } from '../../services/main.service';
import { MaintenanceEventListService } from '../../../events/services/maintenance-event-list.service';
import { MaintenanceEventListResponseDao } from '../../../events/dao/maintenence-event-listDao';
import { SpinnerService } from '../../services/spinner.service';
import { UserLoginService } from '../../services/user-login.service';
import { ScreenPreference, UserPreferences } from '../../../events/components/maintenance-event-list/maintenance-event-list-interfaces';
import { Ng<PERSON><PERSON>op<PERSON> } from '@ng-bootstrap/ng-bootstrap';
import { Ok<PERSON>Auth } from '@okta/okta-auth-js';
import { OktaAuthStateService } from '@okta/okta-angular';
import { WebsocketService } from '../../services/websocket.service';
import { UserAddedAcn } from '../../../events/constants/userAddedAcn';

interface Notification {
  body: string;
  timestamp: Date;
  read: boolean;
}

@Component({
    selector: 'app-main',
    templateUrl: './main.component.html',
    styleUrl: './main.component.scss',
    standalone: false
})
export class MainComponent implements OnInit, OnDestroy {

  constructor(
    private appLayoutService: AppLayoutService,
    private mainService: MainService,
    private userLoginService: UserLoginService,
    private spinnerService: SpinnerService,
    private maintenanceEventListService: MaintenanceEventListService,
    private router: Router,
    private oktaAuthStateService: OktaAuthStateService,
    private websocketService: WebsocketService,
    private cdRef: ChangeDetectorRef
  ) {}

  isAuthenticated: boolean = false;
  isCollapsed = false;
  delayedHide = false;
  showUserNameSection: boolean = false;
  isEventListScreenSelected: boolean = false;
  isEventDetailsScreenSelected: boolean = false;
  isPanelExpanded: boolean = false;
  isApiCallInitiated: boolean = false;
  currentComponent: string | null = null;
  stations: any[] = [];
  userPreferences: any[] = [];
  filteredTableData: MaintenanceEventListResponseDao[] = [];
  rowData: MaintenanceEventListResponseDao[] = [];
  timeout: any;
  screenPref: UserPreferences[] = [];
  screenPreference: ScreenPreference = {};
  acnList: { name: string; eventId: string; eventType: string; selected: boolean; userAdded: boolean }[] = [];
  selectedAcn: { name: string; eventId: string; eventType: string; } | null = null;
  private acnCounter = 1;
  selectedItem: string | null = null;
  notifications: Notification[] = [];
  unreadNotificationsCount = 0;
  hasUnreadNotifications = false;
  showNotificationPanel = false;
  activeTab: 'unread' | 'read' = 'unread';

  @ViewChild('acnlist', { static: false }) acnlist!: ElementRef;
  @ViewChild('tabContent', { read: ViewContainerRef }) tabContent!: ViewContainerRef;

  setUserPreferences(preferences: any[]) {
    this.userPreferences = preferences;
  }

  getUserPreferences() {
    return this.userPreferences;
  }

  async ngOnInit() {
    this.oktaAuthStateService.authState$.subscribe((authState) => {
      this.isAuthenticated = !!authState?.isAuthenticated;
      if (this.isAuthenticated) {
        this.loadNgOnInItLogic();
      }
    });
  }

  ngOnDestroy() {
    this.isEventListScreenSelected = false;
    this.cdRef.detectChanges();
    this.saveMaintenanceEventListUserPreferences();
  }

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: Event) {
    this.saveMaintenanceEventListUserPreferences();
  }

  @HostListener('window:unload', ['$event'])
  onUnload(event: Event) {
    this.saveMaintenanceEventListUserPreferences();
  }

  onBellClick() {
    this.showNotificationPanel = !this.showNotificationPanel;
    if (this.showNotificationPanel) {
      this.hasUnreadNotifications = false;
    }
  }

  loadNgOnInItLogic() {
    this.websocketService.notifications$.subscribe(notification => {
      const modifyNotification = { body: notification.body, timestamp: new Date(notification.timestamp), read: false } as Notification;
      this.notifications.unshift(modifyNotification);
      this.notifications = this.notifications.slice(0, 10);
      this.unreadNotificationsCount = this.notifications.filter(n => !n.read).length;
      this.hasUnreadNotifications = true;
    });

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.isEventListScreenSelected = event.url.includes('maintenance-event-list') || event.urlAfterRedirects.includes('maintenance-event-list');
        this.isEventDetailsScreenSelected = event.url.includes('maintenance-event-details') || event.urlAfterRedirects.includes('maintenance-event-details');
      }
    });

    this.isEventListScreenSelected = window.location.href.includes('maintenance-event-list');
    this.selectedItem = this.isEventListScreenSelected ? 'home' : null;

    setTimeout(() => {
      this.loadDetailsAcnTabData();
    }, 1000);

    this.appLayoutService.sideNavClosedEmitterFromPreferences$.subscribe((isClosed: boolean) => {
      this.toggleSidebar(isClosed, true);
    });

    this.maintenanceEventListService.maintenanceEventListResponse$.subscribe((data: MaintenanceEventListResponseDao[]) => {
      this.rowData = data;
      if (data.length > 0) {
        setTimeout(() => {
          this.updateListDataToMainScreen(data);
        }, 200);
      } else {
        this.filteredTableData = [];
        this.acnList = [];
        this.loadDetailsAcnTabData();
      }
    });

    this.maintenanceEventListService.selectedEventListOptionsUpdated$.subscribe((value: boolean) => {
      if (value) {
        this.updateListDataToMainScreen(this.rowData);
      }
    });

    this.maintenanceEventListService.stationsList$.subscribe((stations: any[]) => {
      this.stations = stations;
    });

    this.mainService.moveScrollToAcnObservable$.subscribe((acn: string) => {
      setTimeout(() => {
        this.cdRef.detectChanges();
      });
      setTimeout(() => {
        this.isPanelExpanded = true;
        this.cdRef.detectChanges();
      }, 0);
      this.scrollToSelectedAcn(acn);
    });

    this.mainService.removeSelectedAcnTabObservable$.subscribe((value: boolean) => {
      if (value) {
        this.selectedAcn = null;
        setTimeout(() => {
          this.isPanelExpanded = false;
          this.cdRef.detectChanges();
        }, 0);
      }
    });

    this.mainService.refreshAcnTabDataObservable$.subscribe((value: boolean) => {
      if (value) {
        setTimeout(() => {
          this.updateAcnTabData();
        }, 300);
      }
    });
  }

  toggleSidebar(isClose: boolean | null, isSessionStorageTriggered: boolean) {
    if (isClose != null) {
      this.isCollapsed = isSessionStorageTriggered ? isClose : !isClose;
      this.delayedHide = this.isCollapsed;
      this.appLayoutService.setSideNavClosedInSessionStorage(this.isCollapsed);
      this.appLayoutService.setSideNavToggleClossed(this.isCollapsed);
      setTimeout(() => {
        this.delayedHide = this.isCollapsed;
      }, 300);
    } else {
      this.delayedHide = this.isCollapsed;
      this.appLayoutService.setSideNavClosedInSessionStorage(this.isCollapsed);
      this.appLayoutService.setSideNavToggleClossed(this.isCollapsed);
    }
    this.showUserNameSection = this.isCollapsed;
  }

  applyToggleFromStorage(isClosed: boolean) {
    this.isCollapsed = isClosed;
    this.cdRef.detectChanges();
  }

  loadComponent(componentName: string) {
    if (componentName) {
      this.isEventListScreenSelected = componentName.includes('maintenance-event-list');
      this.isEventDetailsScreenSelected = componentName.includes('maintenance-event-details');
      this.currentComponent = componentName;
      this.router.navigate([componentName]);
      this.saveMaintenanceEventListUserPreferences();
    }
  }

  logout() {
    this.userLoginService.logout();
  }

  isSelected(route: string): boolean {
    return this.router.url.includes(route);
  }

  updateAcnTabData() {
    const acnTabData = this.mainService.getAcnTabDataFromSessionStorage();
    if (acnTabData) {
      this.acnList = acnTabData;
      const selectedTab = acnTabData.find((tab: any) => tab.selected);
      if (selectedTab) {
        this.selectedAcn = selectedTab;
      }
    }
    const userAddedAcnsInEventListTable = this.maintenanceEventListService.getUserAddedEventListFromStorage();
    if (userAddedAcnsInEventListTable.length > 0) {
      userAddedAcnsInEventListTable.forEach((userAddedAcn: UserAddedAcn) => {
        const acnName = `ACN - ${userAddedAcn.acn}`;
        if (!this.acnList.some(tab => tab.name.toLowerCase() === acnName.toLowerCase())) {
          this.acnList.push({ name: acnName, eventId: userAddedAcn.eventId, eventType: userAddedAcn.eventType, selected: false, userAdded: true });
        } else {
          const existingTab = this.acnList.find(tab => tab.name.toLowerCase() === acnName.toLowerCase());
          if (existingTab) {
            existingTab.userAdded = true;
          }
        }
        this.mainService.setAcnTabsDatainSessionStorage(this.acnList);
      });
    }
  }

  saveMaintenanceEventListUserPreferences() {
    const columnState = this.maintenanceEventListService.getEventListTableColumnsFromStorage();
    const optionsFromSessionStorage =
      this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage() || {
        sideNavClosed: false,
        isOptionsClosed: false
      };

    let headerNames: any[] = [];
    if (columnState) {
      headerNames = columnState.map((col: any) => col.colId);
    } else {
      console.warn('No column state available from grid API.');
    }

    const selectedStorageValues = this.maintenanceEventListService.getSelectedValuesFromStorage();
    this.screenPref = [
      { preferenceName: 'Table', preferenceValues: headerNames },
      { preferenceName: 'Fleet', preferenceValues: selectedStorageValues.selectedFleets },
      { preferenceName: 'Event', preferenceValues: selectedStorageValues.selectedEvents },
      { preferenceName: 'Region', preferenceValues: [selectedStorageValues.selectedRegion || ''] },
      { preferenceName: 'Station', preferenceValues: [selectedStorageValues.selectedStation || ''] },
      { preferenceName: 'PowerPlantChecked', preferenceValues: [String(selectedStorageValues.powerPlantChecked)] },
      { preferenceName: 'sideNavClosed', preferenceValues: [optionsFromSessionStorage.sideNavClosed] },
      { preferenceName: 'isOptionsClosed', preferenceValues: [optionsFromSessionStorage.isOptionsClosed] }
    ];

    this.screenPreference.screenName = 'Home';
    this.screenPreference.preferenceInfo = this.screenPref;
    this.maintenanceEventListService.saveUserPreferences(this.screenPreference).subscribe();
  }

  loadDetailsAcnTabData() {
    const storageAcnTabData = this.mainService.getAcnTabDataFromSessionStorage();
    if (storageAcnTabData?.length) {
      this.acnList = [...new Map(storageAcnTabData.map(item => [item.name.toLowerCase(), item])).values()];
      const selectedAcnTab = this.acnList.find(tab => tab.selected);
      if (selectedAcnTab) {
        this.selectedAcn = selectedAcnTab;
      }
      this.mainService.setAcnTabsDatainSessionStorage(this.acnList);
      return;
    }

    const eventListResponse = this.maintenanceEventListService.getEventListData();
    if (eventListResponse?.length) {
      this.updateListDataToMainScreen(eventListResponse);
    } else if (!this.spinnerService.isEventListApiCallInitiated.getValue() && !this.isApiCallInitiated) {
      this.isApiCallInitiated = true;
      this.maintenanceEventListService.getMaintenanceEventList().subscribe({
        next: (response: MaintenanceEventListResponseDao[]) => {
          this.isApiCallInitiated = false;
          this.rowData = response;
          this.maintenanceEventListService.updateListDataToMainScreen(response);
          this.updateListDataToMainScreen(response);
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching maintenance event list:', error);
          this.isApiCallInitiated = false;
        }
      });
    }
    this.cdRef.detectChanges();
  }

  updateListDataToMainScreen(data: MaintenanceEventListResponseDao[] | []) {
  if (Array.isArray(data)) {
      this.rowData = data;
      const selectedValues = this.maintenanceEventListService.getSelectedValuesFromStorage();
      const storageAcnTabData = this.mainService.getAcnTabDataFromSessionStorage() || [];
      const userAddedTabs = storageAcnTabData.filter((tab: any) => tab.userAdded);

      // Initialize acnSet to track unique ACN names
      const acnSet = new Set<string>();

      if (selectedValues.selectedEvents?.length && selectedValues.selectedFleets?.length) {
        this.filteredTableData = Array.isArray(this.rowData)
          ? this.rowData.filter(row => {
              const matchesEvent = selectedValues.selectedEvents.includes(row.status);
              const matchesFleet = selectedValues.selectedFleets.some(fleet => row.fleetDesc?.toLowerCase().includes(fleet.toLowerCase()));
              const matchesPowerPlant = row.isPowerPlantEvent === selectedValues.powerPlantChecked;
              const matchesStation =
                !selectedValues.selectedRegion ||
                (this.stations.includes(row.station) && (!selectedValues.selectedStation || selectedValues.selectedStation === row.station));
              return matchesEvent && matchesFleet && matchesPowerPlant && matchesStation;
            })
          : [];


        this.acnList = [];
        this.filteredTableData?.forEach(row => {
          const acnName = `ACN - ${row.acn}`;
          if (!acnSet.has(acnName.toLowerCase())) {
            acnSet.add(acnName.toLowerCase());
            this.acnList.push({ name: acnName, eventId: JSON.stringify(row.eventID), eventType: row.type, selected: false, userAdded: false });
          }
        });

        userAddedTabs.forEach((tab: any) => {
          if (!acnSet.has(tab.name.toLowerCase()) && tab.name !== 'New Tab') {
            const foundInRowData = this.rowData.find(row => `ACN - ${row.acn}` === tab.name);
            this.acnList.push({
              name: tab.name,
              eventId: foundInRowData ? JSON.stringify(foundInRowData.eventID) : '',
              eventType: foundInRowData ? foundInRowData.type : '',
              selected: false,
              userAdded: !!foundInRowData || tab.userAdded
            });
            acnSet.add(tab.name.toLowerCase());
          }
        });

        const userAddedAcnsInEventListTable = this.maintenanceEventListService.getUserAddedEventListFromStorage();
        userAddedAcnsInEventListTable.forEach((userAddedAcn: UserAddedAcn) => {
          const acnName = `ACN - ${userAddedAcn.acn}`;
          if (!acnSet.has(acnName.toLowerCase())) {
            this.acnList.push({ name: acnName, eventId: userAddedAcn.eventId, eventType: userAddedAcn.eventType, selected: false, userAdded: true });
            acnSet.add(acnName.toLowerCase());
          }
        });

        const selectedAcnTab = storageAcnTabData.find((tab: any) => tab.selected);
        if (selectedAcnTab) {
          const foundTab = this.acnList.find(tab => tab.name.toLowerCase() === selectedAcnTab.name.toLowerCase());
          if (foundTab) {
            foundTab.selected = true;
            this.selectedAcn = foundTab;
            this.selectAcn(foundTab, false);
          }
        }

        this.acnList = this.mainService.sortBasedOnAscOrder(this.acnList);
        this.mainService.setAcnTabsDatainSessionStorage(this.acnList);
      } else {
        // Handle case when no filters are applied, keep only user-added tabs that are unique
        this.acnList = [];
        userAddedTabs.forEach((tab: any) => {
          if (!acnSet.has(tab.name.toLowerCase())) {
            acnSet.add(tab.name.toLowerCase());
            this.acnList.push(tab);
          }
        });
        this.mainService.setAcnTabsDatainSessionStorage(this.acnList);
      }

      this.cdRef.detectChanges();
    }

  }

  getlastTabIdValue(): number {
    return this.acnCounter++;
  }

  addAcn(): void {
    const existingTabs = this.mainService.getAcnTabDataFromSessionStorage();
    const existingNewTab = existingTabs?.some((tab: any) => tab.name === 'New Tab');

    if (!existingNewTab) {
      const newItem = { name: 'New Tab', eventId: '', eventType: '', selected: true, userAdded: true };
      this.acnList = [...new Map([...this.acnList, newItem].map(item => [item.name.toLowerCase(), item])).values()];
      this.mainService.addAcnTabDataToSessionStorage(newItem);
      this.selectAcn(newItem, false);
      this.mainService.refreshDetailsAcnTabData(true);
    } else {
      const existingTab = this.acnList.find(tab => tab.name === 'New Tab');
      if (existingTab) {
        this.selectAcn(existingTab, false);
      }
    }
    this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: '' } });
  }

  selectAcn(item: { name: string; eventId: string, eventType: string, selected: boolean, userAdded: boolean }, isSelectedFromList: boolean): void {
    setTimeout(() => {
      this.acnList.forEach(tab => (tab.selected = tab.name === item.name));
      this.selectedAcn = item;
      this.mainService.setSelectedTabDataToSessionStorage(item);
      if (isSelectedFromList) {
        this.router.navigate(['/maintenance-event-details'], {
          queryParams: { acn: item.name === 'New Tab' ? null : item.name.split('-')[1]?.trim() }
        });
      }
      this.scrollToSelectedAcn(item.name);
    }, 0);
  }

  private scrollToSelectedAcn(acnName: string) {
    if (acnName) {
      const acnElement = document.querySelector(`[data-acn="${acnName}"]`);
      if (acnElement) {
        acnElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      } else {
        console.warn(`scrollToSelectedAcn: No element found for ACN name - ${acnName}`);
      }
    }
  }

  deleteAcn(event: Event, item: { name: string, eventId: string, eventType: string }): void {
    event.stopPropagation();
    const index = this.acnList.findIndex(acn => (acn.name.toLowerCase() === item.name.toLowerCase() && acn.eventId === item.eventId && acn.eventType === item.eventType));
    if (index !== -1) {
      this.mainService.deleteDetailsTabDataFromSessionStorage(item);
      this.acnList.splice(index, 1);
      this.mainService.setAcnTabsDatainSessionStorage(this.acnList);
    }
    if (this.selectedAcn?.name.toLowerCase() === item.name.toLowerCase() && this.selectedAcn.eventId === item.eventId && this.selectedAcn.eventType === item.eventType) {
      this.selectedAcn = null;
      this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: null } });
    }
  }

  openPopover(popover: NgbPopover) {
    clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      popover.open();
    }, 100);
  }

  closePopover(popover: NgbPopover) {
    clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      popover.close();
    }, 100);
  }

  selectItem(item: string): void {
    if (item) {
      this.selectedItem = item;
      this.loadComponent(item);
    }
  }

  onNotificationPopoverClosed() {
    setTimeout(() => {
      this.notifications.forEach(n => (n.read = true));
      this.unreadNotificationsCount = 0;
      this.hasUnreadNotifications = false;
      this.cdRef.detectChanges();
    });
  }

  get unreadNotifications(): Notification[] {
    return this.notifications.filter(n => !n.read);
  }

  get readNotifications(): Notification[] {
    return this.notifications.filter(n => n.read);
  }

  extractAcn(message: string): number | null {
    const match = message.match(/ACN\s*:\s*(\d+)/);
    return match ? Number(match[1]) : null;
  }

}