import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DiscrepanciesFilterDialogComponent } from './discrepancies-filter-dialog.component';

describe('DiscrepanciesFilterDialogComponent', () => {
  let component: DiscrepanciesFilterDialogComponent;
  let fixture: ComponentFixture<DiscrepanciesFilterDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DiscrepanciesFilterDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DiscrepanciesFilterDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
