export class DoaDiscVector {
  ata: string = '';
  discrepancy: string = '';
  discrepancyText: string[] = [];
  eventType: string = '';
}

export class LinkedDiscrepancy {
  ata: string = '';
  discrepancy: string = '';
  discrepancyText: string[] | null = null;
  eventType: string | null = null;
}

export class LinkedMsns {
  eventId: number = 0;
  ata: string = '';
  discNum: string = '';
  msn: number = 0;
}

export class LinkedSelectedMsns {
  eventId: number = 0;
  ata: string = '';
  discNum: string = '';
  msn: number = 0;
  selected?: boolean = false;
}

export class DoaData {
  eventId: number = 0;
  doaOriginator: string = '';
  createdAt: string = '';
  checkFlightRequrired: boolean = false;
  comment: string = '';
  flightNumber: string = '';
  flightDate: string = '';
  flightLegNumber: string = '';
  destination: string = '';
  estimatedTimeOfArrival: string = '';
  discVector: DoaDiscVector[] = [];
  additionalDescription: string = '';
  closedBy: string = '';
  closedAt: string = '';
  maintenanceCrew: boolean = false;
  lastUpdated: string = '';
}

export class DetailViewResponseDao {
  eventID: number = 0;
  eventType: string = '';
  startDateTime: string = '';
  startDateTimeUTC: string = '';
  endDateTime: string = '';
  eventACN: string = '';
  eventFleetDesc: string = '';
  eventStation: string = '';
  eventStatus: string = '';
  eventEticDateTime: string | null = null;
  eventEticText: string = '';
  eventCurrentComment: string = '';
  eventOST: string | null = null;
  eventLastUpdateDateTime: string = '';
  eventLastUpdatedBy: string = '';
  eventCreatedDateTime: string = '';
  eventCreatedBy: string = '';
  eventOnwerGroupId: string = '';
  acOwnerGroupId: string = '';
  errorText: string | null = null;
  gate: string | null = null;
  mxSpeedDial: string | null = null;
  crew: string | null = null;
  contact: string = '';
  eventEticReasonCd: string | null = null;
  eventEticReasonComment: string | null = null;
  inboundFlightNumber: string | null = null;
  inboundFlightDate: string | null = null;
  inboundLegNumber: string | null = null;
  inboundLegDate: string | null = null;
  inboundOrigination: string | null = null;
  inboundFlightDepartureTime: string | null = null;
  inboundDestination: string | null = null;
  inboundArrivalDate: string | null = null;
  inboundArrivalTime: string | null = null;
  outboundFlightNumber: string | null = null;
  outboundFlightDate: string | null = null;
  outboundLegNumber: string | null = null;
  outboundLegDate: string | null = null;
  outboundOrigination: string | null = null;
  outboundFlightDepartureTime: string | null = null;
  outboundDestination: string | null = null;
  outboundArrivalDate: string | null = null;
  equipmentType: string = '';
  activeTimer: boolean = false;
  doaAlert: boolean = false;
  numberOfDiscrepancies: string = '';
  requestStatus: string = '';
  changeType: number = 0;
  linkedDiscList: LinkedDiscrepancy[] = [];
  linkedMsns: LinkedMsns[] = [];
  numberOfMSN: string = '';
  groupId: string = '';
  contactInfoOwnerList: string[] = [];
  doaData: DoaData | null = null;
  isEventCancelled: boolean = false;
  eventOriginalComment: string = '';
  isEventActive: boolean = false;
  managerNote: string = '';
  eventNewStatus: string = '';
  doaFlightNumber: string | null = null;
  doaFlightDate: string | null = null;
  doaFlightLegNumber: string | null = null;
  resMgrId: string = '';
  memDeskContact: string = '';
  duration: string = '';
  eventCancelled: boolean = false;
  eventActive: boolean = false;
}