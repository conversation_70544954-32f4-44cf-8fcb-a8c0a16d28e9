import { HttpClient } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { MsnShippingDetailInfo } from '../../../../constants/msnShippingDetailInfo';
import { MsnShippingResponseDAO } from '../../../../dao/msnShippingInfoDao';

@Component({
  selector: 'app-msn-shipping-details',
  standalone: false,
  templateUrl: './msn-shipping-details.component.html',
  styleUrl: './msn-shipping-details.component.scss'
})
export class MsnShippingDetailsComponent {

  receivedMsnShippingDao: MsnShippingResponseDAO = new MsnShippingResponseDAO();

  displayedColumns: string[] = [
    'date',
    'waybill',
    'ict',
    'isn',
    'type',
    'shippingType',
    'location',
    'quantity',
    'airline',
    'flight',
    'etd',
    'eta',
    'foisEta',
    'fromStation',
    'fromDept',
    'toStation',
    'toDept'
  ];
  dataSource: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { msn: number },
    private dialogRef: MatDialogRef<MsnShippingDetailsComponent>,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.maintenanceEventDetailsService.getMsnShippingInfo(this.data.msn).subscribe({
      next: (response: MsnShippingResponseDAO) => {
        this.receivedMsnShippingDao = response;
        this.receivedMsnShippingDao.shippingInfoTreeData.forEach((shippingInfo) => {
          shippingInfo.convertedDate = this.formatTimestamp(shippingInfo.date);
          shippingInfo.key = shippingInfo.waybill + shippingInfo.ict + shippingInfo.isn;
        });
        this.dataSource = this.receivedMsnShippingDao.shippingInfoTreeData;
      },
      error: (error: any) => {
        console.error('Error fetching shipping details:', error);
      }
    });
  }

  back(): void {
    this.dialogRef.close();
  }

  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
  
    const pad = (num: number) => num.toString().padStart(2, '0');
  
    const day = pad(date.getDate());
    const month = pad(date.getMonth() + 1); // Months are 0-based
    const year = date.getFullYear();
  
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
  
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  }
}
