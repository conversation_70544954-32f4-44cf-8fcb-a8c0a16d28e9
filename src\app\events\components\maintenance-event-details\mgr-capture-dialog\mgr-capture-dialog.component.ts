import { Component, Input, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { MatDialogRef } from '@angular/material/dialog';
import { MaintenanceEventDetailsSharedService } from '../maintenance-event-details-shared.service';
import { managerNotesCaptureHandler } from '../../../dao/managerNotesCaptureDao';
import { DiscrepanciesList } from '../../../dao/discrepancies-listDao';
import { DiscrepancySelectedTexts, discrepancyUpdTxt, linkedDiscrepancyUpdTxt } from '../../../dao/discrepancyUpdTxt';
import { TubFileNotesResponseDto } from '../../../dto/TubFileNotesResponseDto';

@Component({
  selector: 'app-mgr-capture-dialog',
  standalone: false,
  templateUrl: './mgr-capture-dialog.component.html',
  styleUrl: './mgr-capture-dialog.component.scss'
})
export class MgrCaptureDialogComponent implements OnInit {
  accordianData = [
    { title: 'MSN', countOfObjects: 0 },
    { title: 'Discrepancy', countOfObjects: 0 },
    { title: 'TFNotes', countOfObjects: 0 },
  ]

  private subscriptions: Subscription[] = [];
  linkedDiscrepancies: DiscrepanciesList[] = [];
  managerCaptureData = new managerNotesCaptureHandler(this.accordianData);
  discrepanciesData = this.managerCaptureData.getObjectByTitle('Discrepancy');

  openStates: boolean[] = [];
  discrepancyOpenState: boolean[] = [];
  selectedBlocks: string[] = [];
  temporary: number[] = [1, 2];
  lnkdDiscrepancyUpdtTxts: linkedDiscrepancyUpdTxt[] = [];
  selectedFormattedTexts: string[] = [];
  selectedDiscrepancyTxts: DiscrepancySelectedTexts[] = [];
  tfNotes!: TubFileNotesResponseDto[];
  selectedTfNotes: TubFileNotesResponseDto[] = [];

  constructor(private titleService: Title,
    public dialogRef: MatDialogRef<MgrCaptureDialogComponent>,
    private maintenanceEventDetailsSharedService: MaintenanceEventDetailsSharedService
  ) {
    this.openStates = new Array(this.accordianData.length).fill(false);
  }

  ngOnInit(): void {
    this.subscriptions.push(
      this.maintenanceEventDetailsSharedService.linkedDiscrepancies$.subscribe((data) => {
        this.linkedDiscrepancies = data;
        if (this.linkedDiscrepancies.length > 0) {
          this.managerCaptureData.setObjectsCount('Discrepancy', this.linkedDiscrepancies.length);
          this.discrepancyOpenState = new Array(this.linkedDiscrepancies.length).fill(false);
        }
      }),
      this.maintenanceEventDetailsSharedService.linkedDiscrepancyUpdtTxts$.subscribe((data) => {
        this.lnkdDiscrepancyUpdtTxts = data;
      }),
      this.maintenanceEventDetailsSharedService.tfNotes$.subscribe((data) => {
        this.tfNotes = data;
        if(this.tfNotes.length > 0) {
          this.managerCaptureData.setObjectsCount('TFNotes', this.tfNotes.length);
        }
      })
    );
  }

  getUpdateTextsForDiscrepancy(number: string): discrepancyUpdTxt[] {
    const match = this.lnkdDiscrepancyUpdtTxts.find(item => item.dscrNumber === number);
    return match ? match.dscrpTxts : [];
  }

  onTextCheckboxChange(discrepancy: DiscrepanciesList, formattedTxt: string, isChecked: boolean) {
    const existingEntry = this.selectedDiscrepancyTxts.find(entry => entry.discrepancy.number === discrepancy.number);

    if (isChecked) {
      if (existingEntry) {
        if (!existingEntry.selectedFormattedTexts.includes(formattedTxt)) {
          existingEntry.selectedFormattedTexts.push(formattedTxt);
        }
      } else {
        this.selectedDiscrepancyTxts.push({
          discrepancy: discrepancy,
          selectedFormattedTexts: [formattedTxt]
        });
      }
    } else {
      if (existingEntry) {
        existingEntry.selectedFormattedTexts = existingEntry.selectedFormattedTexts.filter(txt => txt !== formattedTxt);

        if (existingEntry.selectedFormattedTexts.length === 0) {
          this.selectedDiscrepancyTxts = this.selectedDiscrepancyTxts.filter(
            entry => entry.discrepancy.number !== discrepancy.number
          );
        }
      }
    }
  }

  onTfNoteCheckboxChange(tfNotes: TubFileNotesResponseDto, isChecked: boolean) {
    const existsIndex = this.selectedTfNotes.findIndex(note =>
      note.eventTfNotesPk.eventId === tfNotes.eventTfNotesPk.eventId &&
      note.eventTfNotesPk.tfDtTm === tfNotes.eventTfNotesPk.tfDtTm
    );
  
    if (isChecked) {
      if (existsIndex === -1) {
        this.selectedTfNotes.push(tfNotes);
      }
    } else {
      if (existsIndex !== -1) {
        this.selectedTfNotes.splice(existsIndex, 1);
      }
    }
  }
  


  toggleDisplay(index: number) {
    this.openStates[index] = !this.openStates[index];
  }

  toggleDiscrepancyDisplay(index: number) {
    this.discrepancyOpenState[index] = !this.discrepancyOpenState[index];
  }

  onDiscrepancyCheckboxChange(text: string, isChecked: boolean) {
    if (isChecked) {
      if (!this.selectedBlocks.includes(text)) {
        this.selectedBlocks.push(text);
      }
    } else {
      this.selectedBlocks = this.selectedBlocks.filter(b => b !== text);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }

  onSubmit() {
    this.dialogRef.close({
      selectedDiscrepancyTxts: this.selectedDiscrepancyTxts,
      selectedTfNotes: this.selectedTfNotes
    });
  }
}
