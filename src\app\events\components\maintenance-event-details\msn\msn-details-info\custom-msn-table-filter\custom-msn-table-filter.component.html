<h2 mat-dialog-title class="title">Customize MSN Table</h2>
<!-- <mat-divider></mat-divider> -->

<mat-dialog-content class="dialog-content">
    <div class="column-settings-container">
        <!-- Hidden Columns -->
        <div class="column-section">
            <h3>Hide</h3>
            <div
            cdkDropList
            [cdkDropListData]="hiddenColumns"
            [cdkDropListConnectedTo]="['displayedList']"
            class="column-list"
            (cdkDropListDropped)="drop($event)"
            id="hiddenList"
            >
            <div
                class="column-item"
                *ngFor="let column of hiddenColumns"
                cdkDrag
            >
                {{ column }}
            </div>
            </div>
        </div>

        <!-- Divider -->
        <div class="vertical-divider"></div>

        <!-- Displayed Columns -->
        <div class="column-section">
            <h3>Display</h3>
            <div
            cdkDropList
            [cdkDropListData]="displayedColumns"
            [cdkDropListConnectedTo]="['hiddenList']"
            class="column-list"
            (cdkDropListDropped)="drop($event)"
            id="displayedList"
            >
            <div
                class="column-item"
                *ngFor="let column of displayedColumns"
                cdkDrag
            >
                {{ column }}
            </div>
            </div>
        </div>
    </div>
</mat-dialog-content>

<div mat-dialog-actions class="dialog-actions">
  <button mat-button class="closeButton" (click)="onCancel()">Cancel</button>
  <button mat-button class="applyButton" [ngClass]="{'disabled': displayedColumns.length === 0}" [disabled]="displayedColumns.length === 0" (click)="onApply()">Apply</button>
</div>