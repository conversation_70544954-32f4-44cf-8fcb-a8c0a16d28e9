import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MaintenanceEventListComponent } from './events/components/maintenance-event-list/maintenance-event-list.component';
import { MaintenanceEventDetailsComponent } from './events/components/maintenance-event-details/maintenance-event-details.component';
import { MaintenanceEventInquiryComponent } from './events/components/maintenance-event-inquiry/maintenance-event-inquiry.component';
import { MaintenanceEventReportsComponent } from './events/components/maintenance-event-reports/maintenance-event-reports.component';
import { ChangeStatusETICComponent } from './events/components/maintenance-event-list/maintenance-event-list-actions/change-status-etic/change-status-etic.component';
import { AddEventHeaderComponent } from './events/components/add-maintenance-event/add-event-header/add-event-header.component';
import { DoaFormComponent } from './events/components/maintenance-event-list/maintenance-event-list-forms/doa-form/doa-form.component';

import { OktaAuthGuard, OktaCallbackComponent } from '@okta/okta-angular';
import { MgrCaptureDialogComponent } from './events/components/maintenance-event-details/mgr-capture-dialog/mgr-capture-dialog.component';
import { AdmininstrativeComponent } from './events/components/admininstrative/admininstrative.component';
import { CreateIntakeFormComponent } from './events/components/admininstrative/create-intake-form/create-intake-form.component';
import { EditIntakeFormComponent } from './events/components/admininstrative/edit-intake-form/edit-intake-form.component';
import { QuestionsListComponent } from './events/components/admininstrative/questions-list/questions-list.component';

const routes: Routes = [
  { path: '', redirectTo: 'maintenance-event-list', pathMatch: 'full' },
  { path: 'authorization-code/callback', component: OktaCallbackComponent, canActivate: [] },
  { path: 'maintenance-event-list', component: MaintenanceEventListComponent, canActivate: [OktaAuthGuard] },
  { path: 'maintenance-event-details', component: MaintenanceEventDetailsComponent, canActivate: [OktaAuthGuard] },
  { path: 'maintenance-event-inquiry', component: MaintenanceEventInquiryComponent, canActivate: [OktaAuthGuard] },
  { path: 'maintenance-event-reports', component: MaintenanceEventReportsComponent, canActivate: [OktaAuthGuard] },
  { path: 'add-event', component: AddEventHeaderComponent, canActivate: [OktaAuthGuard] },
  { path: 'doa-form', component: DoaFormComponent, canActivate: [OktaAuthGuard] },
  { path: 'mgr-capture-dialog', component: MgrCaptureDialogComponent, canActivate: [OktaAuthGuard] },
  { path: 'mets-admininstrative', component: AdmininstrativeComponent, canActivate: [OktaAuthGuard] },
  { path: 'create-intake-form', component: CreateIntakeFormComponent, canActivate: [OktaAuthGuard] },
  { path: 'edit-intake-form', component: EditIntakeFormComponent, canActivate: [OktaAuthGuard] },
  { path: 'questions-list', component: QuestionsListComponent, canActivate: [OktaAuthGuard] },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }