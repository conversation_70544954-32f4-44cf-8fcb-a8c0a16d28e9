import { intakeForm } from "./intakeFormDto";

export class UserIntakeForm{
    userIntakeFormId: number;
    roleId: number;
    eventId: number;
    dssAuthCode: string;
    intakeFormNm: string;
    intakeForm: intakeForm = new intakeForm();
    constructor(data: Partial<UserIntakeForm> = {}) {
        this.userIntakeFormId = data.userIntakeFormId || 0;
        this.roleId = data.roleId || 0;
        this.eventId = data.eventId || 0;
        this.dssAuthCode = data.dssAuthCode || '';
        this.intakeFormNm = data.intakeFormNm || '';
    }
}