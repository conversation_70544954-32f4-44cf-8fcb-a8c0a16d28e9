export interface EventFlightEticResponseDao {
    eventId: number;
    initialEtic: string;
    eticNumber: number;
    pastDue: string | null;
    flightNumber: string;
    flightDate: string;
    flightLegNumber: string;
    acn: string;
    destination: string;
    origin: string;
    flightStatus: string;
    flightType: string;
    scheduledDeparture: string;
    actualDeparture: string;
    totalDelay: string;
    delayCodes: string;
    fltIn: string;
    arrival: string;
    fltOut: string;
    departure: string;
    totalGroundTime: string | null;
    flightFlag: string | null;
    eticNumberModified: boolean;
    flightInfoModified: boolean;
}