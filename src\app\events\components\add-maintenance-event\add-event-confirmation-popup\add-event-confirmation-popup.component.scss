:host {
  display: block;
  width: 50vw;
  max-width: 90vw;
}

.custom-dialog-container.mat-mdc-dialog-panel.cdk-overlay-pane {
  width: 50vw !important;
  max-width: 90vw !important;
  min-width: 0 !important;
  margin: 0 auto;
  box-sizing: border-box;
}

.success-bg {
  background: linear-gradient(145deg, rgba(39, 174, 96, 0.25), rgba(39, 174, 96, 0.15));
}

.error-bg {
  background: linear-gradient(145deg, rgba(192, 57, 43, 0.25), rgba(192, 57, 43, 0.15));
}

.dialog-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15), 0 0 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.inner-container {
  padding: 0;
  width: 100%;
  box-sizing: border-box;
}

.zoom-container {
  animation: zoomIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white;
  padding: 2% 5%;
  height: 40px;
  margin: 15px 10px;
  border-radius: 10px 10px 0 0;
  animation: slideIn 0.5s ease-out;
}

.closeD {
  font-weight: bold;
  &:hover {
    cursor: pointer;
    color: #ff6600;
    transform: scale(1.2);
    transition: transform 0.2s ease;
  }
}

.content {
  padding: 10px 2%;
  text-align: center;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1));
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
}

.error-message {
  font-weight: 600;
  color: #ff7518;
  display: block;
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 1.6;
  animation: fadeIn 0.7s ease-out forwards;
}

.table-container {
  padding: 2% 0;
}

.mat-table-custom {
  width: 100%;
  table-layout: fixed;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  font-size: 11px;
  border-collapse: separate;
  border-spacing: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.5s ease-out;

  th, td {
    padding: 6px 8px;
    border-bottom: 1px solid #eee;
    text-align: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 11px;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(to right, lightgray, #eaeaea);
    font-weight: 600;
    color: #333;
    font-size: 12px;
  }

  tr:hover {
    cursor: pointer !important;
    background-color: #fafafa;
    transition: background-color 0.3s ease;
  }
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 4%;
  padding: 2%;
  animation: fadeInUp 0.5s ease-out;
}

.or-container {
  align-items: center;
}

.or-divider {
  display: flex;
  align-items: center;
}

.or-text {
  opacity: 0.6;
  color: #6c49b9;
  margin: 0 8px;
}

.button-style {
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 600;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    filter: brightness(110%);
  }
}

.yes-button, .option-button {
  background: linear-gradient(90deg, #6c5ce7, #a29bfe);
  color: white !important;
  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
  &:hover {
    transform: scale(1.05) translateY(-4px);
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.6);
    background: linear-gradient(90deg, #a29bfe, #6c5ce7);
    animation: pulse 1.5s infinite;
  }
  &:active {
    transform: scale(0.95) translateY(0);
    box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
  }
}

.no-button {
  background-color: #ff6600;
  color: white !important;
  box-shadow: 0 6px 20px rgba(255, 102, 0, 0.4);
  &:hover {
    transform: scale(1.05) translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 102, 0, 0.6);
    animation: pulse 1.5s infinite;
  }
  &:active {
    transform: scale(0.95) translateY(0);
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
  }
}

.success-button-width {
  width: 22%;
}

.custom-divider {
  margin: 6px 0;
  width: 85%;
  display: flex;
  place-self: anchor-center;
  height: 3px;
  background: linear-gradient(90deg, transparent, #6a5acd 20%, #ff8c00 80%, transparent);
  border: none;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.custom-divider.vertical {
  width: 2px;
  height: 95%;
  align-self: center;
  background: linear-gradient(to bottom, #6a5acd 0%, #6a5acd 40%, #d18b2e 50%, #ff8c00 60%, #ff8c00 100%);
  margin: 0 8px;
  border-radius: 2px;
}

.custom-divider::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.animation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.success-animation {
  display: flex;
  gap: 15px;
  flex-direction: column;
  align-items: center;

  .icon-wrapper {
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: relative;
    animation: glow 2s ease-in-out infinite;
    transition: transform 0.3s ease;

    &:hover {
      transform: rotate(5deg);
    }
  }

  .success-icon {
    background: rgba(39, 174, 96, 0.3);
    color: #27ae60;
  }

  .custom-icon {
    width: 48px;
    height: 48px;
    animation: spinPulse 2.5s ease-in-out infinite;
    z-index: 2;
  }

  .starburst {
    animation: starburst 2s ease-in-out infinite;
    transform-origin: center;
  }

  .timer-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    opacity: 0.5;
    z-index: 1;
    filter: drop-shadow(0 0 5px rgba(39, 174, 96, 0.3));

    circle {
      transform: rotate(-90deg);
      transform-origin: center;
    }
  }
}

.error-animation {
  display: flex;
  flex-direction: column;
  align-items: center;

  .icon-wrapper {
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: relative;
    animation: glow 2s ease-in-out infinite;
    transition: transform 0.3s ease;

    &:hover {
      transform: rotate(5deg);
    }
  }

  .error-icon {
    background: rgba(192, 57, 43, 0.3);
    color: #c0392b;
  }

  .custom-icon {
    width: 48px;
    height: 48px;
    animation: spinPulse 2.5s ease-in-out infinite;
    z-index: 2;
  }

  .starburst {
    animation: starburst 2s ease-in-out infinite;
    transform-origin: center;
  }
}

.success-message {
  font-weight: 600;
  color: #27ae60;
  font-size: 15px;
  line-height: 1.6;
  animation: fadeIn 0.7s ease-out forwards;
  font-family: 'Inter', sans-serif;
  max-width: 100%;
  word-wrap: break-word;
  margin: 0 !important;
}

.error-message {
  font-weight: 600;
  color: #c0392b;
  font-size: 15px;
  line-height: 1.6;
  animation: fadeIn 0.7s ease-out forwards;
  font-family: 'Inter', sans-serif;
  max-width: 100%;
  word-wrap: break-word;
}

@keyframes zoomIn {
  from {
    transform: scale(0.7);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2), 0 0 30px rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

@keyframes spinPulse {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(10deg) scale(1.15);
  }
  100% {
    transform: rotate(0deg) scale(1);
  }
}

@keyframes starburst {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(108, 92, 231, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(108, 92, 231, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(108, 92, 231, 0);
  }
}

@keyframes stroke {
  100% { stroke-dashoffset: 0; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeInUp {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  0% { opacity: 0; transform: translateX(-20px); }
  100% { opacity: 1; transform: translateX(0); }
}