<div class="header-container" [ngClass]="{'closed-header-container': isSideNavClosed}">
  <div class="header" [ngClass]="{'boxshadowEnabled': !isSideNavClosed, 'diableBoxshadow': isSideNavClosed}">
    <span>
      <img [ngClass]="{'sideNavClosed': isSideNavClosed, 'sideNavOpen': !isSideNavClosed}" src="../../../assets/images/fedex_logo.png" id="logo" alt="Logo" />
      <!-- <img [ngClass]="{'sideNavClosed': isSideNavClosed, 'sideNavOpen': !isSideNavClosed}" src="/mets-server-nextgen-ui/assets/images/fedex_logo.png" id="logo" alt="Logo" /> -->
    </span>
  </div>

  <div *ngIf="!showUserNameSection" class="user-sidenav-inner"
    [ngbPopover]="popoverTemplate"
    popoverClass="user-info-popover"
    triggers="mouseenter:mouseleave"
    placement="right"
    container="body">

    <ng-template #popoverTemplate>
      <div class="popover-profile-card">
        <div class="profile-avatar-section">
          <mat-icon class="profile-avatar-icon">account_circle</mat-icon>
        </div>
      
        <div class="profile-details">
          <div class="profile-role">Logged in as:</div>
          <div class="profile-name">{{ loggedInUserName }}</div>
        </div>
      </div>
      
      <!-- <div class="popover-user-card">
        <div class="user-icon-section">
          <mat-icon class="user-avatar-icon">person</mat-icon>
        </div>
        <div class="user-details-section">
          <div class="user-label">Logged in as</div>
          <div class="user-name">{{ loggedInUserName }}</div>
        </div>
      </div> -->
    </ng-template>

    <div class="user-icon-closed-section">
    <mat-nav-list>
      <mat-list-item class="mat-list-item">
        <mat-icon class="menuIcon" matListIcon>account_circle</mat-icon>
      </mat-list-item>
    </mat-nav-list>
    </div>
  </div>



  <div class="username-section" *ngIf="showUserNameSection">
    <div class="user-icon-section">
      <mat-icon class="menuIcon" matListIcon>account_circle</mat-icon>
    </div>
    <div class="user-name-section">
      <span class="userName">{{loggedInUserName}}</span>
    </div>
  </div>  
</div>