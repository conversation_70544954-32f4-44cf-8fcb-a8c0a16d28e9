export class updateDOAFormDto {
    mode: string;
    ACN: string;
    GROUP_ID: string;
    event_id: string;
    access_level: string;
    timer_id: string;
    flag: string;
    REQUEST_TYPE: string;
    user_id: string;
    token_id: string;
    DISCREPANCY_FILTER: string;
    DISCREPANCY_FROM_DATE: string;
    DISCREPANCY_TO_DATE: string;
    DISCREPANCY_SPAN: string;
    comment_updated: boolean;
    event_active: boolean;
    niw_timer_data: NiwTimerData;
    tf_notes_data: TfNotesData;
    TEST_FLIGHT_DATA: string;
    road_trip_data: RoadTripData;
    flight_etic_data: FlightEticData;
    event_doa_data: EventDoaData;
    detail_view_data: DetailViewData;
    TART_DATE_TIME: string;
    EMAIL_DATA: string;
    EVENT_DISCREPANCY_DATA: string;
    report_categories_data: any;
    WLM_NIW_TIMER_START_TIME: string;
    WLM_NIW_TIMER_STOP_TIME: string;
  
    constructor(data: Partial<updateDOAFormDto> = {}) {
      this.mode = data.mode || 'EVENT_UPDATE';
      this.ACN = data.ACN || 'NULL';
      this.GROUP_ID = data.GROUP_ID || 'NULL';
      this.event_id = data.event_id || '';
      this.access_level = data.access_level || '90';
      this.timer_id = data.timer_id || '15';
      this.flag = data.flag || 'EDIT';
      this.REQUEST_TYPE = data.REQUEST_TYPE || 'NULL';
      this.user_id = data.user_id || '';
      this.token_id = data.token_id || '';
      this.DISCREPANCY_FILTER = data.DISCREPANCY_FILTER || 'NULL';
      this.DISCREPANCY_FROM_DATE = data.DISCREPANCY_FROM_DATE || 'NULL';
      this.DISCREPANCY_TO_DATE = data.DISCREPANCY_TO_DATE || 'NULL';
      this.DISCREPANCY_SPAN = data.DISCREPANCY_SPAN || 'NULL';
      this.comment_updated = data.comment_updated ?? true;
      this.event_active = data.event_active ?? true;
      this.niw_timer_data = new NiwTimerData(data.niw_timer_data);
      this.tf_notes_data = new TfNotesData(data.tf_notes_data);
      this.TEST_FLIGHT_DATA = data.TEST_FLIGHT_DATA || 'NULL';
      this.road_trip_data = new RoadTripData(data.road_trip_data);
      this.flight_etic_data = new FlightEticData(data.flight_etic_data);
      this.event_doa_data = new EventDoaData(data.event_doa_data);
      this.detail_view_data = new DetailViewData(data.detail_view_data);
      this.TART_DATE_TIME = data.TART_DATE_TIME || 'NULL';
      this.EMAIL_DATA = data.EMAIL_DATA || 'NULL';
      this.EVENT_DISCREPANCY_DATA = data.EVENT_DISCREPANCY_DATA || 'NULL';
      this.report_categories_data = data.report_categories_data ?? null;
      this.WLM_NIW_TIMER_START_TIME = data.WLM_NIW_TIMER_START_TIME || 'NULL';
      this.WLM_NIW_TIMER_STOP_TIME = data.WLM_NIW_TIMER_STOP_TIME || 'NULL';
    }
  }
  
  class NiwTimerData {
    eventId: number;
    timerId: string;
    timerStartDate: string;
    timerStopDate: string;
    creationDateTime: string;
    lastUpdatedDateTime: string;
  
    constructor(data: Partial<NiwTimerData> = {}) {
      this.eventId = data.eventId || 0;
      this.timerId = data.timerId || '';
      this.timerStartDate = data.timerStartDate || '';
      this.timerStopDate = data.timerStopDate || '';
      this.creationDateTime = data.creationDateTime || '';
      this.lastUpdatedDateTime = data.lastUpdatedDateTime || '';
    }
  }
  
  class TfNotesData {
    eventId: number;
    tfDateTime: string;
    empNumber: string;
    empName: string;
    empDepartment: string;
    editedFlag: string;
    tfNote: string;
    noteType: number;
    noteId: number;
    changeType: number;
  
    constructor(data: Partial<TfNotesData> = {}) {
      this.eventId = data.eventId || 0;
      this.tfDateTime = data.tfDateTime || '';
      this.empNumber = data.empNumber || '';
      this.empName = data.empName || '';
      this.empDepartment = data.empDepartment || '';
      this.editedFlag = data.editedFlag || '';
      this.tfNote = data.tfNote || '';
      this.noteType = data.noteType || 0;
      this.noteId = data.noteId || 0;
      this.changeType = data.changeType || 0;
    }
  }
  
  class RoadTripData {
    eventId: number;
    creationDate: string;
    problem: string;
    roadTripEmployeeDto: RoadTripEmployeeDto;
    parts: string;
    stationContactInfo: string;
    stationContactManager: string;
    stationContactRampPhone: string;
    transportationTo: string;
    transportationToType: string;
    transportationToInfo: string;
    transportationBack: string;
    transportationBackType: string;
    transportationBackInfo: string;
    status: string;
    sequenceNumber: number;
    lastUpdatedTime: string;
    boeingContact: string;
    douglasContact: string;
    airbusContact: string;
  
    constructor(data: Partial<RoadTripData> = {}) {
      this.eventId = data.eventId || 0;
      this.creationDate = data.creationDate || '';
      this.problem = data.problem || '';
      this.roadTripEmployeeDto = new RoadTripEmployeeDto(data.roadTripEmployeeDto);
      this.parts = data.parts || '';
      this.stationContactInfo = data.stationContactInfo || '';
      this.stationContactManager = data.stationContactManager || '';
      this.stationContactRampPhone = data.stationContactRampPhone || '';
      this.transportationTo = data.transportationTo || '';
      this.transportationToType = data.transportationToType || '';
      this.transportationToInfo = data.transportationToInfo || '';
      this.transportationBack = data.transportationBack || '';
      this.transportationBackType = data.transportationBackType || '';
      this.transportationBackInfo = data.transportationBackInfo || '';
      this.status = data.status || '';
      this.sequenceNumber = data.sequenceNumber || 0;
      this.lastUpdatedTime = data.lastUpdatedTime || '';
      this.boeingContact = data.boeingContact || '';
      this.douglasContact = data.douglasContact || '';
      this.airbusContact = data.airbusContact || '';
    }
  }
  
  class RoadTripEmployeeDto {
    eventId: number;
    sequenceNumber: number;
    employeeNumber: string;
    employeeName: string;
  
    constructor(data: Partial<RoadTripEmployeeDto> = {}) {
      this.eventId = data.eventId || 0;
      this.sequenceNumber = data.sequenceNumber || 0;
      this.employeeNumber = data.employeeNumber || '';
      this.employeeName = data.employeeName || '';
    }
  }
  
  class FlightEticData {
    eventId: number;
    initialEtic: string | null;
    eticNumber: string | null;
    pastDue: string | null;
    flightNumber: string;
    flightDate: string;
    flightLegNumber: string;
    acn: string | null;
    destination: string | null;
    flightStatus: string | null;
    flightType: string | null;
    scheduledDeparture: string | null;
    actualDeparture: string | null;
    totalDelay: string | null;
    delayCodes: string | null;
    fltIn: string | null;
    arrival: string | null;
    fltOut: string | null;
    departure: string | null;
    totalGroundTime: string | null;
    flightFlag: string | null;
    eticNumberModified: boolean;
    flightInfoModified: boolean;
  
    constructor(data: Partial<FlightEticData> = {}) {
      this.eventId = data.eventId || 0;
      this.initialEtic = data.initialEtic || null;
      this.eticNumber = data.eticNumber || null;
      this.pastDue = data.pastDue || null;
      this.flightNumber = data.flightNumber || '';
      this.flightDate = data.flightDate || '';
      this.flightLegNumber = data.flightLegNumber || '';
      this.acn = data.acn || null;
      this.destination = data.destination || null;
      this.flightStatus = data.flightStatus || null;
      this.flightType = data.flightType || null;
      this.scheduledDeparture = data.scheduledDeparture || null;
      this.actualDeparture = data.actualDeparture || null;
      this.totalDelay = data.totalDelay || null;
      this.delayCodes = data.delayCodes || null;
      this.fltIn = data.fltIn || null;
      this.arrival = data.arrival || null;
      this.fltOut = data.fltOut || null;
      this.departure = data.departure || null;
      this.totalGroundTime = data.totalGroundTime || null;
      this.flightFlag = data.flightFlag || null;
      this.eticNumberModified = data.eticNumberModified ?? false;
      this.flightInfoModified = data.flightInfoModified ?? true;
    }
  }
  
  class EventDoaData {
    eventId: number | null;
    checkFlightRequrired: boolean | null;
    maintenanceCrew: string | null;
  
    constructor(data: Partial<EventDoaData> = {}) {
      this.eventId = data.eventId || null;
      this.checkFlightRequrired = data.checkFlightRequrired || null;
      this.maintenanceCrew = data.maintenanceCrew || null;
    }
  }
  
  class DetailViewData {
    eventID: number;
    eventType: string;
    startDateTime: string;
    endDateTime: string;
    eventACN: string;
    eventFleetDesc: string;
    eventStation: string;
    eventStatus: string;
    eventEticDateTime: string;
    eventEticText: string;
    eventCurrentComment: string;
    eventOST: string;
    eventLastUpdateDateTime: string;
    eventLastUpdatedBy: string;
    eventCreatedDateTime: string;
    eventCreatedBy: string;
    eventOnwerGroupId: string;
    acOwnerGroupId: string;
    errorText: string;
    gate: string;
    mxSpeedDial: string;
    crew: string;
    contact: string;
    eventEticReasonCd: string;
    eventEticReasonComment: string;
    inboundFlightNumber: string;
    inboundFlightDate: string;
    inboundLegNumber: string;
    inboundLegDate: string;
    inboundOrigination: string;
    inboundFlightDepartureTime: string;
    inboundDestination: string;
    inboundArrivalDate: string;
    inboundArrivalTime: string;
    outboundFlightNumber: string;
    outboundFlightDate: string;
    outboundLegNumber: string;
    outboundLegDate: string = '';
    outboundOrigination: string;
    outboundFlightDepartureTime: string;
    outboundDestination: string;
    outboundArrivalDate: string;
    equipmentType: string;
    doaChangeIndicator: string;
    testFlightList: any[];
    roadTripList: any[];
    testFlightData: string;
    activeTimer: boolean;
    doaAlert: boolean;
    numberOfDiscrepancies: string;
    requestStatus: string;
    changeType: number;
    linkedDiscList: LinkedDisc[];
    numberOfMSN: string;
    groupId: string;
    contactInfoOwnerList: string[];
    doaData: DoaData;
    isEventCancelled: boolean;
    eventOriginalComment: string;
    isEventActive: boolean;
    managerNote: string;
    eventNewStatus: string;
    doaFlightNumber: string;
    doaFlightDate: string;
    doaFlightLegNumber: string;
    resMgrId: string;
    memDeskContact: string;
  
    constructor(data: Partial<DetailViewData> = {}) {
      this.eventID = data.eventID || 0;
      this.eventType = data.eventType || '';
      this.startDateTime = data.startDateTime || '';
      this.endDateTime = data.endDateTime || '';
      this.eventACN = data.eventACN || '';
      this.eventFleetDesc = data.eventFleetDesc || '';
      this.eventStation = data.eventStation || '';
      this.eventStatus = data.eventStatus || '';
      this.eventEticDateTime = data.eventEticDateTime || '';
      this.eventEticText = data.eventEticText || '';
      this.eventCurrentComment = data.eventCurrentComment || '';
      this.eventOST = data.eventOST || '';
      this.eventLastUpdateDateTime = data.eventLastUpdateDateTime || '';
      this.eventLastUpdatedBy = data.eventLastUpdatedBy || '';
      this.eventCreatedDateTime = data.eventCreatedDateTime || '';
      this.eventCreatedBy = data.eventCreatedBy || '';
      this.eventOnwerGroupId = data.eventOnwerGroupId || '';
      this.acOwnerGroupId = data.acOwnerGroupId || '';
      this.errorText = data.errorText || '';
      this.gate = data.gate || '';
      this.mxSpeedDial = data.mxSpeedDial || '';
      this.crew = data.crew || '';
      this.contact = data.contact || '';
      this.eventEticReasonCd = data.eventEticReasonCd || '';
      this.eventEticReasonComment = data.eventEticReasonComment || '';
      this.inboundFlightNumber = data.inboundFlightNumber || '';
      this.inboundFlightDate = data.inboundFlightDate || '';
      this.inboundLegNumber = data.inboundLegNumber || '';
      this.inboundLegDate = data.inboundLegDate || '';
      this.inboundOrigination = data.inboundOrigination || '';
      this.inboundFlightDepartureTime = data.inboundFlightDepartureTime || '';
      this.inboundDestination = data.inboundDestination || '';
      this.inboundArrivalDate = data.inboundArrivalDate || '';
      this.inboundArrivalTime = data.inboundArrivalTime || '';
      this.outboundFlightNumber = data.outboundFlightNumber || '';
      this.outboundFlightDate = data.outboundFlightDate || '';
      this.outboundLegNumber = data.outboundLegNumber || '';
      this.outboundOrigination = data.outboundOrigination || '';
      this.outboundFlightDepartureTime = data.outboundFlightDepartureTime || '';
      this.outboundDestination = data.outboundDestination || '';
      this.outboundArrivalDate = data.outboundArrivalDate || '';
      this.equipmentType = data.equipmentType || '';
      this.doaChangeIndicator = data.doaChangeIndicator || '';
      this.testFlightList = data.testFlightList || [];
      this.roadTripList = data.roadTripList || [];
      this.testFlightData = data.testFlightData || '';
      this.activeTimer = data.activeTimer ?? false;
      this.doaAlert = data.doaAlert ?? false;
      this.numberOfDiscrepancies = data.numberOfDiscrepancies || '';
      this.requestStatus = data.requestStatus || '';
      this.changeType = data.changeType || 0;
      this.linkedDiscList = data.linkedDiscList || [];
      this.numberOfMSN = data.numberOfMSN || '';
      this.groupId = data.groupId || '';
      this.contactInfoOwnerList = data.contactInfoOwnerList || [];
      this.doaData = new DoaData(data.doaData);
      this.isEventCancelled = data.isEventCancelled ?? false;
      this.eventOriginalComment = data.eventOriginalComment || '';
      this.isEventActive = data.isEventActive ?? false;
      this.managerNote = data.managerNote || '';
      this.eventNewStatus = data.eventNewStatus || '';
      this.doaFlightNumber = data.doaFlightNumber || '';
      this.doaFlightDate = data.doaFlightDate || '';
      this.doaFlightLegNumber = data.doaFlightLegNumber || '';
      this.resMgrId = data.resMgrId || '';
      this.memDeskContact = data.memDeskContact || '';
    }
  }
  
  class LinkedDisc {
    ata: string;
    discrepancy: string;
    discrepancyText: string | null;
    eventType: string | null;
  
    constructor(data: Partial<LinkedDisc> = {}) {
      this.ata = data.ata || '';
      this.discrepancy = data.discrepancy || '';
      this.discrepancyText = data.discrepancyText || null;
      this.eventType = data.eventType || null;
    }
  }
  
  class DoaData {
    eventId: number;
    doaOriginator: string;
    createdAt: string;
    checkFlightRequrired: boolean;
    comment: string;
    flightNumber: string;
    flightDate: string;
    flightLegNumber: string;
    destination: string;
    estimatedTimeOfArrival: string;
    discVector: any[];
    additionalDescription: string;
    closedBy: string;
    closedAt: string;
    maintenanceCrew: boolean;
    lastUpdated: string;
  
    constructor(data: Partial<DoaData> = {}) {
      this.eventId = data.eventId || 0;
      this.doaOriginator = data.doaOriginator || '';
      this.createdAt = data.createdAt || '';
      this.checkFlightRequrired = data.checkFlightRequrired ?? false;
      this.comment = data.comment || '';
      this.flightNumber = data.flightNumber || '';
      this.flightDate = data.flightDate || '';
      this.flightLegNumber = data.flightLegNumber || '';
      this.destination = data.destination || '';
      this.estimatedTimeOfArrival = data.estimatedTimeOfArrival || '';
      this.discVector = data.discVector || [];
      this.additionalDescription = data.additionalDescription || '';
      this.closedBy = data.closedBy || '';
      this.closedAt = data.closedAt || '';
      this.maintenanceCrew = data.maintenanceCrew ?? false;
      this.lastUpdated = data.lastUpdated || '';
    }
  }