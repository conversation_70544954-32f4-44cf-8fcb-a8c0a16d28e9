export class MsnResponseDAO {
    status!: {
      successful: boolean;
      errorCode: string | null;
      errorMessage: string | null;
      code: string | null;
      level: string | null;
      message: string | null;
      addlMessage: string | null;
      token: string | null;
    };
  
    header!: {
      userId: string | null;
      clientIp: string | null;
      clientBoxDnsName: string | null;
      clientReqNbr: number;
      clientMethodNm: string | null;
      clientSubmittedTmstp: string | null;
      serverIp: string | null;
      serverBoxDnsName: string | null;
      serverReqStartTmstp: string | null;
      serverReqEndTmstp: string | null;
      serverDbTimeTaken: number;
    };
  
    msnDetails!: {
      shortageNoticeNumber: number;
      aircraft: string;
      cpn: string;
      quantity: number;
      mpn: string;
      enteredMpn: string;
      followUpCode: string;
      shipToStation: string;
      shipToDept: string;
      status: string;
      category: string;
      resFailureCd: string;
      discrepancyNumber: string;
      sequenceOrNonRoutine: string;
      workorderNumber: string;
      timeRemaining: string;
      requestedById: string;
      requestedByName: string;
      requestedByPhone: string;
      datePartNeedBy: string;
      timePartNeedBy: string;
      taskNbr: string;
      transTm: string;
      station: string;
      ataChap: string;
      ataSubChap: string;
      shortageNoticeIsn: number;
      refIpc: string;
      refTypeNbr: string;
      mpnHasMultipleCpns: boolean;
      bohDiscrpCd: string;
      interchangeCpn: string;
      previousFollowUpCode: string;
      printerId: string;
      discrepancyOid: string;
      prevResolutionDate: string;
      prevResolutionTime: string;
      maintType: string;
      fanBlade: boolean;
      requiredByDateTime: string;
    };
}  