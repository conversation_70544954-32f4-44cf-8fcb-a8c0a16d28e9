export class MsnShippingResponseDAO {
    status!: {
      successful: boolean;
      errorCode: string | null;
      errorMessage: string | null;
      code: string | null;
      level: string | null;
      message: string | null;
      addlMessage: string | null;
      token: string | null;
    }

    header!: {
      userId: string | null;
      clientIp: string | null;
      clientBoxDnsName: string | null;
      clientReqNbr: number;
      clientMethodNm: string | null;
      clientSubmittedTmstp: number | null;
      serverIp: string;
      serverBoxDnsName: string;
      serverReqStartTmstp: number;
      serverReqEndTmstp: number;
      serverDbTimeTaken: number;
    }
  
    shippingInfo: Array<{
      eventNbr: number;
      date: number;
      shippingType: string;
      airline: string | null;
      flight: string;
      etd: string | null;
      eta: string | null;
      waybill: string;
      fromStation: string;
      fromDept: string;
      toStation: string;
      toDept: string;
      ict: string;
      sensawareDeviceNbr: string | null;
      additionalInfo: string | null;
      pfmAirbill: string | null;
      etaChanged: boolean;
      type: string | null;
      isn: number;
      quantity: number;
      location: string | null;
      foisEta: string | null;
      key: string;
      events: any[];
    }> = [];
  
    shippingInfoTreeData: Array<{
      eventNbr: number;
      date: number;
      convertedDate?: string;
      shippingType: string;
      airline: string | null;
      flight: string;
      etd: string | null;
      eta: string | null;
      waybill: string;
      fromStation: string;
      fromDept: string;
      toStation: string;
      toDept: string;
      ict: string;
      sensawareDeviceNbr: string | null;
      additionalInfo: string | null;
      pfmAirbill: string | null;
      etaChanged: boolean;
      type: string | null;
      isn: number;
      quantity: number;
      location: string | null;
      foisEta: string | null;
      key: string;
      events: any[];
    }> = [];
}  