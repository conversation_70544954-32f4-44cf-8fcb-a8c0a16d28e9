.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    pointer-events: auto;
}

.test {
    background-color: purple !important;
    color: purple !important;
}

body.spinner-active {
    overflow: hidden;
}

::ng-deep mat-progress-bar {
    width: 25% !important;
}

.progress-text {
    text-align: center;
    position: absolute;
    top: 52%;
    left: 47%;
    font-weight: bold;
    color: white;
}




.mat-progress-bar.test .mat-progress-bar-fill {
    background-color: purple !important;
}
.mat-progress-bar.test .mat-progress-bar-fill::after {
    background-color: purple !important;
}
::ng-deep .mat-progress-bar.test .mat-progress-bar-fill {
    background-color: purple !important;
}

::ng-deep .mat-progress-bar.test .mat-progress-bar-buffer {
    background-color: rgba(128, 0, 128, 0.3) !important; /* Light purple buffer */
}

