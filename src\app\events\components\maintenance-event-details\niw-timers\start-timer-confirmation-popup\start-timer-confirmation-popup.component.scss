.confirmation-dialog {
  display: block;
  width: 100%;

  .dialog-title {
    width: 100%;
    display: flex;
    justify-content: center;
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    color: white;
    padding: 10px 5px;
    font-size: 1.1rem;
    font-weight: bold;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    box-sizing: border-box;
  }

  .dialog-content {
    padding: 1.2rem 1.5rem;
    font-size: 1rem;
    color: #333;

    p {
      margin: 0;
      line-height: 1.5;
    }

    .highlight-purple {
      font-weight: 600;
      color: #3f2876;
    }

    .highlight-orange {
      font-weight: 600;
      color: #ff6600;
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    padding: 1.5rem;

    button {
      min-width: 25%;
      padding: 0.6rem 1.2rem;
      font-weight: 600;
      color: white;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    .btn-yes {
      background-color: #6c49b9;

      &:hover {
        background-color: #3F2876;
      }
    }

    .btn-no {
      background-color: #ff6600;

      &:hover {
        background-color: #e65c00;
      }
    }
  }
}

// Optional: Dialog container styling
::ng-deep .startConfirmation-custom-dialog .mat-dialog-container {
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  width: 100%;
  box-sizing: border-box;
}