<mat-card>
    <h2 style="text-align: center;">Change Status/ ETIC</h2><br>
    <div class="mainBody" style="display: flex; justify-content: center; flex-wrap: wrap; text-align: center;">
        <h6><span>Status: {{ status }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>ETIC: {{ etic }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Comment: {{ comments }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>ETIC Comments: {{ etic_comments }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Duration: {{ duration }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Start: {{ start }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Station: {{ station }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>MSNs: {{ msns }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Owner: {{ owner }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Contact: {{ contact }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Responsible Mgr: {{ responsible_manager }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <!-- <h6><span>Event ID: {{ eventId }}</span></h6> -->
    </div>
</mat-card><br>

<mat-card>
    <h5 style="margin-left: 10px;">DOA Flight Info</h5><br>
    <div class="mainBody" style="display: flex; justify-content: center; flex-wrap: wrap; text-align: center;">
        <h6><span>Flt No: {{ DOA_Flt_No }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Flt Date: {{ DOA_Flt_Date }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Flt Leg: {{ DOA_Flt_Leg }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Dest: {{ DOA_Flt_Dest }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>ETA: {{ DOA_Flt_ETA }}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <!-- <h6><span>Event ID: {{ eventId }}</span></h6> -->
    </div>
</mat-card><br>

<mat-form-field appearance="outline" class="full-width">
    <mat-label>Comments</mat-label>
    <textarea matInput rows="4" [(ngModel)]="Additional_description" name="Comments"></textarea>
</mat-form-field><br>

<!-- Add Checkbox 1 -->
<mat-checkbox [(ngModel)]="checkFlightReq" name="checkFlightReq">Check Flight Required</mat-checkbox>
        
<!-- Add Checkbox 2 -->
<mat-checkbox [(ngModel)]="MxCrew" name="MxCrew">Mx C/W</mat-checkbox><br>
<br>

<mat-card>
    <div class="mainBody" style="display: flex; justify-content: center; flex-wrap: wrap; text-align: center;">
        <h6><span>Created By: {{eventCreatedBy}}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Created At: {{eventCreatedAt}}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Closed By: {{eventClosedBy}}</span></h6>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h6><span>Closed At: {{eventClosedAt}}</span></h6>
    </div>
</mat-card><br>

<span><button mat-raised-button color="primary" class="spaced-button" (click)="onClick()">Update</button></span>