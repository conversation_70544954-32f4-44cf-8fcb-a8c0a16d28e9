.create-intake-form-container {
  background-color: white;
  display: flex;
  flex-direction: row;
  padding: 5px;
  gap: 0.5rem;
  height: 100%;
  width: 100%;
  border-radius: 10px;
  align-items: flex-start;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.intake-form-drag-box {
  gap: 1%;
  width: 65%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff, #f0f2f5);
  border-radius: 15px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  transition: transform 0.3s ease-in-out;
}

.intake-form-drag-box:hover {
  transform: translateY(-1.5px);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.intake-form-drag-box-title {
  display: flex;
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: white;
  height: 50px;
  align-items: center;
  justify-content: center;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 2px 8px rgba(63, 40, 118, 0.2);
  font: 700 1.2em/1.2em 'Open Sans', sans-serif;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s ease-in-out;
}

.intake-form-drag-box-title:hover {
  transform: translateY(-1.5px);
  box-shadow: 0 4px 12px rgba(63, 40, 118, 0.3);
}

.create-new-question-container {
  width: 97%;
  padding: 5px;
  background: linear-gradient(135deg, #e6e6fa, #d8bfd8);
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-self: center;
  justify-content: center;
}

.create-new-question-container:hover {
  transform: translateY(-1px);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.icon-text-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28%;
  margin: 0 auto;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 2px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.icon-text-container:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  transform: translateY(-1.5px) scale(1.02);
}

.icon-text-container mat-icon {
  margin-right: 6px;
  color: #6c49b9;
  transition: color 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.icon-text-container:hover mat-icon {
  color: #3f2876;
}

.create-new-question {
  color: #6c49b9;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Roboto', sans-serif;
  transition: color 0.3s ease-in-out;
}

.icon-text-container:hover .create-new-question {
  color: #3f2876;
  text-shadow: 0 0 4px rgba(108, 73, 185, 0.2);
}

.intake-form-scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px 12px;
}

.intake-form-questions-list {
  height: 100%;
  background: linear-gradient(135deg, #ffffff, #f5f6fa);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.intake-form-box {
  box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: #B7BBE3;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px 15px;
  transition: all 0.3s ease-in-out;
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.intake-form-box:hover {
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px) scale(1.01);
  background: linear-gradient(135deg, #B7BBE3, #c0c4e7);
}

.intake-form-box:hover .question-text {
  color: #5a3a99;
  text-shadow: 0 0 6px rgba(90, 58, 153, 0.3);
  transform: translateY(-1px);
}

.intake-form-box:hover .question-type {
  color: #4a2a89;
  text-shadow: 0 0 4px rgba(74, 42, 137, 0.2);
  transform: translateY(-1px);
}

.intake-form-box:hover .question-answers {
  color: #444;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.intake-form-box:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 8px;
  z-index: 0;
  animation: glow 1.5s infinite ease-in-out;
}

.question-details {
  width: 85%;
  word-break: break-word;
  overflow-wrap: break-word;
}

.question-text {
  font-weight: 600;
  color: #3f2876;
  margin: 0 0 4px;
  transition: all 0.3s ease-in-out;
  letter-spacing: 0.5px;
  text-transform: capitalize;
  position: relative;
  word-break: break-word;
  overflow-wrap: break-word;
}

.question-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #6c49b9;
  transition: width 0.3s ease-in-out;
}

.question-type {
  font-style: italic;
  color: #6c49b9;
  margin: 0 0 4px;
  transition: all 0.3s ease-in-out;
  font-weight: 400;
  letter-spacing: 0.3px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.question-answers {
  color: #333;
  margin: 0;
  transition: all 0.3s ease-in-out;
  font-weight: 500;
  letter-spacing: 0.2px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #B7BBE3;
  padding: 12px;
  position: absolute;
  top: 0;
  left: 0;
  transform: translate3d(0, 0, 0);
  animation: fadeIn 0.3s ease-in-out;
  z-index: 1000;
  pointer-events: none;
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.cdk-drag-placeholder {
  opacity: 0.6;
  background: #e0e0e0;
  border: 1px dashed #999;
  border-radius: 8px;
  transition: all 0.3s ease-in-out;
}

.cdk-drag-animating {
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.button-container {
  padding: 10px;
  display: flex;
  justify-content: center;
  gap: 15px;
  background: linear-gradient(135deg, #ffffff, #f0f2f5);
  border-radius: 0 0 10px 10px;
  transition: all 0.3s ease-in-out;
}

.button-container:hover {
  background: linear-gradient(135deg, #f0f2f5, #e0e4e8);
  transform: translateY(-2px);
}

.mat-button1 {
  height: 35px;
  padding: 8px 16px;
  border-radius: 8px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  min-width: 90px;
  cursor: pointer;
  border: none;
}

.mat-button1:disabled {
  background-color: #d3d3d3;
  color: #666;
  cursor: not-allowed;
}

.mat-button1:not(:disabled):hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.submit-button .mat-button1 {
  background-color: #6c49b9;
  color: white;

  &:disabled {
    background-color: #b7bbd3;
    color: #999;
    pointer-events: auto;
    cursor: not-allowed;
  }
}

.submit-button .mat-button1:hover {
  background-color: #5a3a99;
  box-shadow: 0 3px 10px rgba(108, 73, 185, 0.3);

  &:disabled {
    background-color: #b7bbd3;
    color: #999;
    pointer-events: auto;
    cursor: not-allowed;
    box-shadow: none !important;
  }
}

.action-button .mat-button1 {
  background-color: #ff6600;
  color: white;
}

.action-button .mat-button1:hover {
  background-color: #e65c00;
  box-shadow: 0 3px 10px rgba(255, 102, 0, 0.3);
}

.divider {
  width: 2px;
  background: #e0e0e0;
  height: 100%;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.previous-questions-container {
  width: 35%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff, #f0f2f5);
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  transition: transform 0.3s ease-in-out;
}

.previous-questions-container:hover {
  transform: translateY(-2px);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.previous-questions-title-container {
  flex-shrink: 0;
}

.previous-questions-title {
  display: flex;
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: white;
  height: 50px;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(63, 40, 118, 0.2);
  font: 700 1em/1em 'Open Sans', sans-serif;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s ease-in-out;
  border-radius: 10px 10px 0 0;
}

.previous-questions-title:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 40, 118, 0.3);
}

.previous-questions-scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.questions-list {
  height: 100%;
  background: linear-gradient(135deg, #ffffff, #f5f6fa);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.intake-form-box-actions {
  display: flex;
  gap: 6px;
  width: 15%;
  justify-content: flex-end;
}

.edit-icon {
  color: #6c49b9;
  transition: transform 0.3s ease-in-out;
  cursor: pointer !important;
}

.edit-icon:hover {
  transform: scale(1.1);
  cursor: pointer !important;
}

.delete-icon {
  color: #d32f2f;
  transition: transform 0.3s ease-in-out;
  cursor: pointer !important;
}

.delete-icon:hover {
  transform: scale(1.1);
  cursor: pointer !important;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes glow {
  0% { opacity: 0.3; }
  50% { opacity: 0.7; }
  100% { opacity: 0.3; }
}

/* Search Container Styles */
.search-container {
  justify-self: center;
  position: relative;
  width: 90%;
  height: 40px;
  margin: 8px 0 0 0;
  background: linear-gradient(135deg, #e6e6fa, #d8bfd8);
  border-radius: 8px;
  padding: 0 15px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.search-container:hover {
  box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 12px;
  transform: translateY(-1px);
}

.search-icon {
  align-content: center;
  position: absolute;
  left: 15px;
  color: #6c49b9;
  font-size: 18px;
  transition: all 0.3s ease;
}

.search-input {
  width: 100%;
  padding: 8px 8px 8px 30px;
  border: none;
  background: transparent;
  font-size: 13px;
  color: #3f2876;
  outline: none;
  transition: all 0.3s ease;
  height: 100%;
}

.search-input::placeholder {
  color: #6c49b9;
  opacity: 0.7;
  font-size: 13px;
}

.search-input:focus {
  padding-left: 32px;
}

.search-input:focus + .search-border-animation {
  width: 100%;
}

.search-border-animation {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6c49b9, #3f2876);
  transition: width 0.4s ease;
}