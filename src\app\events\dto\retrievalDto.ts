export class RetrievalDto {
    mode?: string;
    acn?: number;
    ata?: string;
    eventId?: string;
    station?: string;
    department?: string;
    accessLevel?: string;
    discrepancyNumber?: string;
    discrepancyFilter?: string;
    discrepancyFromDate?: string;
    discrepancyToDate?: string;
    userId?: string;
    tokenId?: string;
    region?: string;
  
    constructor(data?: Partial<RetrievalDto>) {
      this.mode = data?.mode || '';
      this.accessLevel = data?.accessLevel || '';
      this.userId = data?.userId || '';
      this.tokenId = data?.tokenId || '';
    }
  }
  