<h2 mat-dialog-title class="dialog-title">Create Intake Form</h2>
<mat-dialog-content class="dialog-content">
    <div class="dialog-content-row1">
        <mat-form-field class="mat-form-field">
            <mat-label>Role</mat-label>
            <input matInput [(ngModel)]="role" [matAutocomplete]="roleAuto" (input)="onInputChangeForRole($event)"
                placeholder="Select or type a Role">
            <mat-autocomplete #roleAuto="matAutocomplete" (optionSelected)="onRoleChange($event.option.value)">
                @for (role of filteredRoles; track role) {
                <mat-option [value]="role">{{ role }}</mat-option>
                }
                @if (showAddOptionForRole) {
                <mat-option [value]="newRoleValue" (click)="addNewRole()">Add "{{ newRoleValue }}"</mat-option>
                }
            </mat-autocomplete>
        </mat-form-field>
        <mat-form-field class="mat-form-field">
            <mat-label>Event Type</mat-label>
            <input matInput [(ngModel)]="event" [matAutocomplete]="eventAuto" (input)="onInputChange($event)"
                placeholder="Select or type an event">
            <mat-autocomplete #eventAuto="matAutocomplete" (optionSelected)="onEventChange($event.option.value)">
                @for (event of filteredEvents; track event) {
                <mat-option [value]="event">{{ event }}</mat-option>
                }
                @if (showAddOption) {
                <mat-option [value]="newEventValue" (click)="addNewEvent()">Add "{{ newEventValue }}"</mat-option>
                }
            </mat-autocomplete>
        </mat-form-field>
        <mat-form-field class="mat-form-field">
            <mat-label>Fleet</mat-label>
            <input matInput [(ngModel)]="fleet" [matAutocomplete]="fleetAuto" placeholder="Select or type a Fleet"
                (input)="onInputChangeForFleet($event)">
            <mat-autocomplete #fleetAuto="matAutocomplete" (optionSelected)="onFleetChange($event.option.value)">
                @for (fleet of filteredFleets; track fleet) {
                <mat-option [value]="fleet">{{ fleet }}</mat-option>
                }
            </mat-autocomplete>
        </mat-form-field>
    </div>
    <div class="dialog-content-row2">
        <mat-form-field class="mat-form-field full-width">
            <mat-label>Intake Form Name</mat-label>
            <textarea matInput [(ngModel)]="formName" (ngModelChange)="onFormNameChange()"></textarea>
        </mat-form-field>
    </div>
</mat-dialog-content>
<mat-dialog-actions class="dialog-actions">
    <button mat-raised-button class="dialog-button submit-button" [disabled]="submitButtonDisabled"
        (click)="onSubmit()">Submit</button>
    <button mat-raised-button class="dialog-button cancel-button" (click)="onCancel()">Cancel</button>
</mat-dialog-actions>