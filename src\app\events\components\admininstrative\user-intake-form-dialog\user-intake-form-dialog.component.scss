.dialog-title {
    font-family: 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 700;
    color: white;
    background: linear-gradient(135deg, #3f2876, #6c49b9);
    padding: 8px 20px;
    border-radius: 8px 8px 0 0;
    margin: 0;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(63, 40, 118, 0.3);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    display: block;
}

.dialog-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0;
    animation: pulse 2s infinite;
    z-index: 0;
}

.dialog-title:hover,
.dialog-title.field-hovered {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(63, 40, 118, 0.5);
    overflow: hidden;
}

@keyframes pulse {
    0% { opacity: 0; }
    50% { opacity: 0.5; }
    100% { opacity: 0; }
}

.dialog-content {
    align-content: center;
    padding: 20px !important;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(135deg, #ffffff, #f0f2f5);
    border-radius: 0 0 8px 8px;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dialog-content:hover {
    transform: translateY(-2px);
    box-shadow: inset 0 4px 10px rgba(0, 0, 0, 0.15);
}

.dialog-content-row1 {
    display: flex;
    flex-direction: row;
    gap: 15px;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.dialog-content-row1:hover {
    opacity: 0.98;
    transform: scale(1.01);
}

.dialog-content-row2 {
    display: flex;
    flex-direction: row;
    gap: 15px;
    width: 100%;
    transition: all 0.3s ease;
}

.dialog-content-row2:hover {
    transform: scale(1.01);
}

.mat-form-field {
    flex: 1;
    min-width: 0;
    max-width: 100%;
    transition: all 0.3s ease;
    position: relative;
}

.mat-form-field:hover,
.mat-form-field.field-focused,
.mat-form-field.autocomplete-open {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.full-width {
    width: 100%;
}

.dialog-actions {
    padding: 10px 20px;
    display: flex;
    justify-content: center;
    gap: 15px;
    background: linear-gradient(135deg, #ffffff, #f0f2f5);
    border-top: 1px solid #e0e0e0;
    border-radius: 0 0 8px 8px;
    transition: all 0.3s ease;
}

.dialog-actions:hover {
    background: linear-gradient(135deg, #f0f2f5, #e0e4e8);
    transform: translateY(-2px);
}

.dialog-button {
    padding: 10px 20px;
    border-radius: 6px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 100px;
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;
}

.dialog-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    z-index: 0;
}

.dialog-button:hover::before {
    width: 300%;
    height: 300%;
}

.dialog-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 73, 185, 0.3);
}

/* Submit button styling */
.submit-button:not([disabled]) {
    background: linear-gradient(135deg, #6c49b9, #5a3a99);
    color: white;
    position: relative;
    z-index: 1;
}

.submit-button:not([disabled]):hover {
    background: linear-gradient(135deg, #5a3a99, #4a2a89);
    box-shadow: 0 4px 15px rgba(108, 73, 185, 0.4);
}

.submit-button[disabled] {
    background-color: #d3d3d3;
    color: #666;
    cursor: not-allowed;
}

/* Cancel button styling */
.cancel-button {
    background-color: #ff6600 !important;
    color: white !important;
    position: relative;
    z-index: 1;
}

.cancel-button:hover {
    background-color: #e65c00 !important;
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.4);
}

::ng-deep .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input {
    color: #333333;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
}

::ng-deep .mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label {
    color: #6c49b9;
    font-weight: bold;
    transition: color 0.3s ease;
}

::ng-deep .mat-mdc-form-field:hover .mat-mdc-floating-label.mdc-floating-label {
    color: #4a2a89;
}

::ng-deep .mdc-line-ripple {
    background-color: #6c49b9;
}

::ng-deep .mat-mdc-select-value-text {
    color: #333333;
    font-size: 14px;
    font-weight: 400;
}

::ng-deep .mat-mdc-text-field-wrapper {
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled):hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

::ng-deep .mat-mdc-autocomplete-panel {
    border-radius: 6px;
    background: #fff;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

::ng-deep .mat-mdc-option {
    color: #333333;
    font-size: 14px;
    transition: all 0.3s ease;
}

::ng-deep .mat-mdc-option:hover {
    background-color: #e8e9fd;
}

@media (max-width: 768px) {
    .dialog-content-row1 {
        flex-direction: column;
    }

    .mat-form-field {
        width: 100%;
    }

    .dialog-actions {
        flex-direction: column;
        align-items: center;
    }

    .dialog-button {
        width: 100%;
        margin-bottom: 10px;
    }

    .dialog-button:last-child {
        margin-bottom: 0;
    }
}