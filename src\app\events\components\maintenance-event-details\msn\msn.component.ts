import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Input, NgZone, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ClientSideRowModelModule, ColDef, GridApi, GridOptions, Module } from 'ag-grid-community';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { MatDialog } from '@angular/material/dialog';
import { AppLayoutService } from '../../../../app-layout/services/app-layout.service';
import { Subject, takeUntil } from 'rxjs';
import { MaterialShortageNoticeResponseDao } from '../../../dao/msn-list-Dao';
import { CustomMsnCheckboxComponent } from './custom-msn-checkbox/custom-msn-checkbox.component';
import { DetailViewResponseDao, LinkedSelectedMsns } from '../../../dao/detailViewDao';
import { DiscrepanciesList } from '../../../dao/discrepancies-listDao';

@Component({
  selector: 'app-msn',
  standalone: false,
  templateUrl: './msn.component.html',
  styleUrl: './msn.component.scss'
})
export class MsnComponent implements OnInit, OnChanges, OnDestroy {

  @ViewChild('agGrid', { static: false, read: ElementRef }) agGridElement!: ElementRef;
  
  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;
  @Input() acn: string = "";
  @Input() eventValue: string = '';
  @Input() isAddEventSelected: boolean = false;
  @Input() discData: any;

  @Output() selectedMsnData = new EventEmitter<any>();

  private destroy$ = new Subject<void>();

  columnDefs: ColDef[] = [];
  storedDefaultColumnDefs: any;
  filteredTableData: any[] = [];
  rowData: any[] = [];

  gridHeight: number = 37;
  isRowSelected: boolean = false;
  selectedRow: any = null; 
  isSideNavClosed: boolean = false;
  isUpdateEnabled: boolean = false;
  animateTitle = false;
  selectedRowIndex: number | null = null;
  selectedMsn: { eventId: number; ata: string, discNum: string, msn: number }[] = [];

  originalSelectedMsn: any[] = [];
  selectedMsmFromDetailView: LinkedSelectedMsns[] = [];
  updateSelectedMsn: LinkedSelectedMsns[] = [];
  discrepancyData: DiscrepanciesList[] = [];
  shortageNoticeData:MaterialShortageNoticeResponseDao[] = [];
  msnNumbers: string[] = []; 

  gridApi!: GridApi;
  gridColumnApi: any;
  gridOptions: GridOptions = {
    rowBuffer: 10,
    rowHeight: 30, // Ensures uniform row height
    suppressTouch: false,
    suppressColumnVirtualisation: true,
    suppressScrollOnNewData: true,
    suppressAnimationFrame: true,
    animateRows: true,
    headerHeight: 40,
    enableBrowserTooltips: true,
    suppressRowClickSelection: false,
    context: {
      getMatchedMsns: () => this.msnNumbers
    },
    rowStyle: {
      fontSize: '13px', // Smaller font size
      padding: '0px', // Remove row padding
      margin: '0px', // Remove row margin
      border: 'none', // Ensure no row border
    },
    rowClassRules: {
    'linked-row': (params) => params.data?.isLinkedMsn === true
  },
  };  
  
  defaultColDef: ColDef = {
    headerClass: 'center-header',
    headerComponentParams: { menuIcon: true },
    cellStyle: { 
      textAlign: 'center', 
      verticalAlign: 'middle', 
      padding: '0px',
      border: 'none',
      whiteSpace: 'normal',
      wordWrap: 'break-word',
      overflow: 'visible'
    },
    autoHeight: true,
    sortable: true,
    filter: true,
    editable: false,
    resizable: true,
  };  

  modules: Module[] = [ClientSideRowModelModule];

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService,
              private appLayoutService: AppLayoutService,
              private cdRef: ChangeDetectorRef,
              public dialog: MatDialog,
              private ngZone: NgZone
            ) {}

  ngOnInit(): void {
    this.appLayoutService.sideNavClosedObservable$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isClosed) => {
        this.isSideNavClosed = isClosed || false;
        this.setColumnDefs(this.isSideNavClosed);
        this.calculateGridHeight();
        this.adjustGridSizeSmoothly();
      });
  
    if (this.columnDefs.length === 0) {
      this.setColumnDefs(false);
    }
  
    this.calculateGridHeight();
  
    this.maintenanceEventDetailsService.showTitleEffect$.subscribe((show: boolean) => {
      if (show) {
        this.animateTitle = false; // reset
        setTimeout(() => {
          this.animateTitle = true; // trigger reflow to apply animation
        }, 0);
      } else {
        this.animateTitle = false;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") {
        this.getMsnTabledata(this.detailsViewObj);
      } else {
        this.rowData = [];
        this.filteredTableData = [];
      }
    }  
    if (changes['acn'] || changes['eventtype']) {
      this.getAddMsnTabledata(parseInt(this.acn));
      setTimeout(() => {
        document.querySelector('.title')?.classList.add('fill-animation');
      }, 200);
    }

    if (changes['discData']) {
      this.discrepancyData = this.discData || [];;
      this.associateMsnWithDiscrepancies();
    }

  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.calculateGridHeight(); // Recalculate grid height on window resize
    this.setColumnDefs(this.isSideNavClosed);
    this.resizeGridColumns();
  }

 onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    params.api.sizeColumnsToFit();

    setTimeout(() => {
      this.gridApi.applyColumnState({
        state: [{ colId: 'msn', sort: 'asc' }],
        defaultState: { sort: null }
      });
    }, 100);
  }

  setColumnDefs(isSideNavClosed: boolean) {
        let baseColumnWidths = isSideNavClosed
      ? { msn: 7, statusCode: 7, followUpCode: 7, manufPartNbr: 7, coPartNbr: 7, cpnQty: 7, cpnDescription: 8, shiptoSta: 7, shiptoDept: 8, planMetInd: 9, datetimePartNeedBy: 10, requestedBy: 16 }
      : { msn: 7, statusCode: 7, followUpCode: 8, manufPartNbr: 8, coPartNbr: 7, cpnQty: 6.5, cpnDescription: 9.5, shiptoSta: 7.5, shiptoDept: 9.5, planMetInd: 10, datetimePartNeedBy: 10, requestedBy: 10 };

    const totalPercentage = Object.values(baseColumnWidths).reduce((acc, val) => acc + val, 0);
    const gridWidth = this.agGridElement?.nativeElement?.offsetWidth || 800;
    let remainingWidth = gridWidth;

    const generateColumnDefs = (columns: any[]) => {
      return columns.map(column => {
        const percentage = baseColumnWidths[column.field as keyof typeof baseColumnWidths] || 6;
        const columnWidth = (percentage / totalPercentage) * remainingWidth;
        remainingWidth -= columnWidth;

        return {
          ...column,
          flex: percentage,
          filter: column.filter !== undefined ? column.filter : true,
          sortable: column.sortable !== undefined ? column.sortable : true,
          editable: column.editable !== undefined ? column.editable : false,
          resizable: column.resizable !== undefined ? column.resizable : true,
          ...(column.field === 'msn' && {
            cellRenderer: CustomMsnCheckboxComponent,
            valueGetter: (params: any) => params.data?.msn,
            comparator: (a: any, b: any, nodeA: any, nodeB: any) => {
              const checkedA = nodeA.data?.isChecked ?? false;
              const checkedB = nodeB.data?.isChecked ?? false;
              if (checkedA !== checkedB) {
                return checkedA ? -1 : 1;
              }
              return (a ?? '').toString().localeCompare((b ?? '').toString());
            }
          })
        };
      });
    };

    if (this.columnDefs.length === 0) {
      this.maintenanceEventDetailsService.getMsnTableHeaders().subscribe({
        next: (data: any[]) => {
          this.columnDefs = generateColumnDefs(data);
          this.storedDefaultColumnDefs = [...this.columnDefs];
        },
        error: (error) => {
          console.error("Error fetching column definitions:", error);
        }
      });
    } else {
      this.columnDefs = generateColumnDefs(this.storedDefaultColumnDefs);
      this.storedDefaultColumnDefs = [...this.columnDefs];
    }

    this.refreshGrid();
  }

  getAddMsnTabledata(acn: number) {
    this.maintenanceEventDetailsService.getMsnTableData(acn).subscribe({
      next: (response: MaterialShortageNoticeResponseDao[]) => {
        this.shortageNoticeData=response;
        response.forEach(row => {row.isChecked = false;});

        this.filteredTableData = response;
        this.cdRef.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching maintenance discrepancies details header:', error);
      }
    });
  }

  getMsnTabledata(detailsViewObj: DetailViewResponseDao) {
    this.maintenanceEventDetailsService.getMsnTableData(parseInt(detailsViewObj?.eventACN)).subscribe({
      next: (response: MaterialShortageNoticeResponseDao[]) => {
        response.forEach(row => {row.isChecked = false;});

        if (detailsViewObj?.linkedMsns.length > 0) {
          this.originalSelectedMsn = JSON.parse(JSON.stringify(detailsViewObj.linkedMsns));
          this.originalSelectedMsn.forEach((msn: LinkedSelectedMsns) => {msn.selected = true;});
          this.originalSelectedMsn.forEach((msn: LinkedSelectedMsns) => {
            response.forEach((row: MaterialShortageNoticeResponseDao) => {
              row.isChecked = false;
              if (String(row.msn) === String(msn.msn)) {
                row.isChecked = true;
              }
            });
          });
        } else {
          this.originalSelectedMsn = [];
        }
        this.filteredTableData = response;
        this.cdRef.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching maintenance discrepancies details header:', error);
      }
    });
  }

  onCellValueChanged(params: any) {
    if (this.isAddEventSelected) {
      if (params.newValue === true) {
        const existingIndex = this.selectedMsn.findIndex((msn) => msn.msn === params.data.msn);
        if (existingIndex === -1) {
          this.selectedMsn.push({ata: params.data.ata, discNum: params.data.discNum, eventId: this.detailsViewObj?.eventID, msn: params.data.msn});
        }
      } else {
        const index = this.selectedMsn.findIndex((msn) => msn.msn === params.data.msn);
        index != -1 ? this.selectedMsn.splice(index, 1) : null;
      }
      this.filteredTableData.forEach((row) => {row.msn === params.data.msn ? row.isChecked = params.newValue : null;});
      this.selectedMsnData.emit(this.selectedMsn);
    } else {
      this.filteredTableData.forEach((row) => {row.msn === params.data.msn ? row.isChecked = params.newValue : null;});
      if (this.originalSelectedMsn.length > 0) {
        const existingIndex = this.originalSelectedMsn.findIndex((msn) => msn.msn === params.data.msn);
        if (existingIndex === -1) {
          if (params.newValue === true) {
            this.updateSelectedMsn.push({ata: params.data.ata, discNum: params.data.discNum, eventId: this.detailsViewObj?.eventID, msn: params.data.msn});
          } else {
            const index = this.updateSelectedMsn.findIndex((msn) => msn.msn === params.data.msn);
            index != -1 ? this.updateSelectedMsn.splice(index, 1) : null;
          }
        } else {
          this.updateSelectedMsn[existingIndex].selected = params.newValue;
        }
        const foundOriginalSelectedMSnChnage = this.originalSelectedMsn.find((msn: LinkedSelectedMsns) => {!msn.selected});
        this.isUpdateEnabled = this.updateSelectedMsn.length > 0 && !foundOriginalSelectedMSnChnage ? true : false;
      } else {
        if (params.newValue === true) {
          this.updateSelectedMsn.push({ata: params.data.ata, discNum: params.data.discNum, eventId: this.detailsViewObj?.eventID, msn: params.data.msn});
        } else {
          const index = this.updateSelectedMsn.findIndex((msn) => msn.msn === params.data.msn);
          index != -1 ? this.updateSelectedMsn.splice(index, 1) : null;
        }
        this.isUpdateEnabled = this.updateSelectedMsn.length > 0 ? true : false;
      }
    }
  }

  calculateGridHeight(): void {
    const headerHeight = 60;
    const footerHeight = 60;
    const padding = 20;

    this.ngZone.run(() => {
      this.gridHeight = window.innerHeight - headerHeight - footerHeight - padding - 180;
      this.cdRef.detectChanges();
    });
  }

  adjustGridSizeSmoothly() {
    this.ngZone.runOutsideAngular(() => {
      requestAnimationFrame(() => {
        if (this.gridApi) {
          this.resizeGridColumns();
        }
      });
    });
  }

  resizeGridColumns() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
      // this.gridApi.setColumnDefs(this.columnDefs);
    }
  }

  private refreshGrid() {
    setTimeout(() => {
        this.gridApi.setGridOption('columnDefs', this.columnDefs);
        this.gridApi.refreshHeader();
        this.gridApi.redrawRows();
        this.cdRef.detectChanges();
    }, 100);
  }

associateMsnWithDiscrepancies() {
  this.msnNumbers = []; 
  if(this.discrepancyData) {
    this.discrepancyData.forEach(discrepancy => {
  
      if (!discrepancy?.number) {
        console.log("Skipping discrepancy with missing number:", discrepancy);
        this.msnNumbers= [];
        return;
      }

      const matchingMsns = this.shortageNoticeData
        .filter(msn => {
          if (!msn?.discNum) return false;
          
          const formattedDiscNum = this.formatToMinimumFourDigits(msn.discNum);
          console.log("Formatted input number:", formattedDiscNum, "for MSN:", msn.discNum);
          return formattedDiscNum === discrepancy.number;
        })
        .map(msn => msn.msn); 

      matchingMsns.forEach(msnNum => {
        this.msnNumbers.push(String(msnNum));
      });
      this.msnNumbers = Array.from(new Set(this.msnNumbers));
      if (this.msnNumbers.length > 0) {
        let msnData: any[] = [];
        this.msnNumbers.forEach(msn => {
          const matchedRow = this.filteredTableData.find(row => String(row.msn) === msn);
          if (matchedRow) {
            msnData.push({
              eventId: '',
              ata: matchedRow.ata,
              discNum: matchedRow.discNum,
              msn: matchedRow.msn
            });
          }
        });
        this.selectedMsnData.emit(msnData);
      } else {
        this.selectedMsnData.emit([]);
      }

    });
  }
  else{
    console.log("No discrepancies found to associate with MSN.");
    this.msnNumbers = [];
  }
       this.filteredTableData = this.filteredTableData.map(row => {
        const isLinked = this.msnNumbers.includes(String(row.msn));
        return {
          ...row,
          matchedMsns: this.msnNumbers,
          isLinkedMsn: isLinked 
        };
      });

        this.filteredTableData = [...this.filteredTableData.map(row => ({ ...row }))];
        this.gridApi?.applyTransaction({ update: this.filteredTableData });

        this.gridApi?.applyColumnState({
            state: [{ colId: 'msn', sort: 'asc' }],
            applyOrder: true,
            defaultState: { sort: null }
          });
          this.gridApi?.refreshClientSideRowModel('sort');


        this.gridApi?.refreshCells({ force: true });

        this.gridApi?.redrawRows(); 

        this.setColumnDefs(this.isSideNavClosed);
        this.calculateGridHeight();
        this.adjustGridSizeSmoothly();

        
}


  formatToMinimumFourDigits(input: string): string {
    if (/^0+$/.test(input)) {
      return input.slice(-4);
    }
    const noLeadingZeros = input.replace(/^0+/, '');

    if (noLeadingZeros.length < 4) {
      return noLeadingZeros.padStart(4, '0');
    }
    console.log("Formatted input:", noLeadingZeros);
    return noLeadingZeros;
  }



}