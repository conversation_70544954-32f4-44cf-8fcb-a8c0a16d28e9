<div class="stepper-container">
    <mat-horizontal-stepper style="border-radius: 10px;" #stepper [selectedIndex]="activeStep" (selectionChange)="onStepSelectionChange($event)">
      <mat-step label="Event Details">
        <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[0] }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step *ngIf="eventtype !== 'NOTE'" label="Intake Form">
        <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[1] }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step *ngIf="eventtype !== 'NOTE'" label="Discrepancies">
        <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[2] }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step *ngIf="eventtype !== 'NOTE'" label="Comments">
        <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[3] }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step *ngIf="eventtype !== 'NOTE'" label="MSNs">
        <ng-container *ngTemplateOutlet="badge; context: { valid: stepCompletion[4] }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step label="Tub File Notes">
        <ng-container *ngTemplateOutlet="badge; context: { valid: specifyDetailForm.get('tfNote')?.valid }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step label="Reporting Category">
        <ng-container *ngTemplateOutlet="badge; context: { valid: true }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
      <mat-step *ngIf="specifyDetailForm.get('status')?.value === 'AOG' && eventtype !== 'NOTE'" label="NIW Timers">
        <ng-container *ngTemplateOutlet="badge; context: { valid: selectedNiwTimer }"></ng-container>
        <ng-template #badge let-valid="valid">
          <mat-badge [hidden]="!valid" content="✔" color="accent" class="step-badge"></mat-badge>
        </ng-template>
      </mat-step>
    </mat-horizontal-stepper>
  </div>

  <div #scrollContainer class="content-container" (scroll)="onScroll()">
    <form [formGroup]="specifyDetailForm" (ngSubmit)="onSubmit()" class="form-container">
      <!-- Step 1: Event Details -->
      <div #sectionRef class="step-section">
        <p class="step-title">Step 1: Specify Event Details</p>
        <div class="form-card">
          <div class="form-group general-info">
            <span class="section-title">General Info</span>
            <div class="info-fields-container">
              <div class="info-field acn-type-field">
                <span class="label">ACN:</span>
                <span class="highlight">{{acn}}</span>
              </div>
              <div class="info-field acn-type-field">
                <span class="label">Type:</span>
                <span class="highlight">{{aircraftType}}</span>
              </div>
              <mat-form-field appearance="outline" class="form-field" *ngIf="eventtype !== 'NOTE'">
                <mat-label>Station</mat-label>
                <mat-select formControlName="station">
                  @for (st of stationsList; track st) {
                    <mat-option [value]="st">{{st}}</mat-option>
                  }
                </mat-select>
                <mat-error *ngIf="specifyDetailForm.get('station')?.hasError('required')">Station is required</mat-error>
              </mat-form-field>
              <mat-form-field class="adjustwidth2" appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" *ngIf="eventtype == 'OOS'">
                  <mat-option *ngFor="let stat of filteredStatusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
                <mat-select formControlName="status" *ngIf="eventtype == 'TRK'">
                  <mat-option *ngFor="let stat of statusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
                <mat-select formControlName="status" *ngIf="eventtype == 'NOTE'">
                  <mat-option *ngFor="let stat of statusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Display of below 3 fields is hidden as per requirements -->
              <mat-form-field appearance="outline" class="form-field" style="display: none;">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="startDate" [disabled]="true">
                <mat-datepicker-toggle matIconSuffix [for]="picker" [disabled]="true"></mat-datepicker-toggle>
                <mat-datepicker #picker [disabled]="true"></mat-datepicker>
                <mat-error *ngIf="specifyDetailForm.get('startDate')?.hasError('required')">Start Date is required</mat-error>
              </mat-form-field>

              <!-- <mat-form-field appearance="outline" class="form-field etic-field time-field custom-time-field">
                <mat-label>Start Time</mat-label>
                <input matInput [matTimepicker]="pickerS" formControlName="startTime">
                <mat-timepicker-toggle matIconSuffix [for]="pickerS"/>
                <mat-timepicker #pickerS format="24" [interval]="60"/>
              </mat-form-field> -->

              <mat-form-field appearance="outline" class="form-field time-field" style="display: none;">
                <mat-label>Hour</mat-label>
                  <mat-select formControlName="startHour" [disabled]="true">
                  <mat-option *ngFor="let hour of startHours" [value]="hour">{{ hour }}</mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field appearance="outline" class="form-field time-field" style="display: none;">
                <mat-label>Minute</mat-label>
                <mat-select formControlName="startMinute" [disabled]="true">
                  <mat-option *ngFor="let minute of startMinutes" [value]="minute">{{ minute }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <div class="form-group status-etic-container">
            <!-- <div class="form-group acn-status">
              <span class="section-title">ACN Status</span>
              <mat-form-field class="adjustwidth2" style="padding-top: 3%; width: 65%; align-self: center;" appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" *ngIf="eventtype == 'OOS'">
                  <mat-option *ngFor="let stat of filteredStatusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
                <mat-select formControlName="status" *ngIf="eventtype == 'TRK'">
                  <mat-option *ngFor="let stat of statusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
                <mat-select formControlName="status" *ngIf="eventtype == 'NOTE'">
                  <mat-option *ngFor="let stat of statusList" [value]="stat">{{stat}}</mat-option>
                </mat-select>
              </mat-form-field>
            </div> -->

            <div class="form-group etic-details" *ngIf="eventtype === 'OOS'">
              <span class="section-title">ETIC Details</span>
              <div class="etic-details-row">
                <mat-form-field class="adjustwidth2" appearance="outline">
                  <mat-label>ETIC Type</mat-label>
                  <mat-select formControlName="eticttype">
                    <mat-option *ngFor="let etic of filteredEticTypeList" [value]="etic">{{etic}}</mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="form-field etic-field date-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value === 'FIRM' ||
                                      specifyDetailForm.get('eticttype')?.value === 'WA'">
                  <mat-label>Date</mat-label>
                  <input matInput [matDatepicker]="pickeretic" formControlName="eticStartDate">
                  <mat-datepicker-toggle matIconSuffix [for]="pickeretic"></mat-datepicker-toggle>
                  <mat-datepicker #pickeretic></mat-datepicker>
                  <mat-error *ngIf="specifyDetailForm.get('eticStartDate')?.hasError('required')">ETIC Date is required</mat-error>
                </mat-form-field>

                <!-- <mat-form-field appearance="outline" class="form-field etic-field time-field custom-time-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value === 'FIRM' ||
                                      specifyDetailForm.get('eticttype')?.value === 'WA'">
                  <mat-label>Time</mat-label>
                  <input matInput [matTimepicker]="pickerE" formControlName="eticStartTime">
                  <mat-timepicker-toggle matIconSuffix [for]="pickerE"/>
                  <mat-timepicker #pickerE format="24" [interval]="60"/>
                </mat-form-field> -->


                <mat-form-field appearance="outline" class="form-field etic-field time-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value === 'FIRM' ||
                                      specifyDetailForm.get('eticttype')?.value === 'WA'">
                  <mat-label>Time</mat-label>
                  <input matInput type="time" formControlName="eticStartTime" (input)="onEticTimeInputChange()" (change)="onEticTimeInputChange()">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field etic-field reason-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value &&
                                      specifyDetailForm.get('eticttype')?.value !== 'FIRM' &&
                                      specifyDetailForm.get('eticttype')?.value !== 'WA'">
                  <mat-label>Reason</mat-label>
                  <mat-select formControlName="eticNiwReason">
                    <mat-option *ngFor="let niwtimer of filteredNiwTimersList" [value]="niwtimer">{{ niwtimer }}</mat-option>
                  </mat-select>
                </mat-form-field>
                
                <span style="font-weight: bolder;font-size: larger;" *ngIf="specifyDetailForm.get('eticttype')?.value &&
                                      specifyDetailForm.get('eticttype')?.value !== 'FIRM' &&
                                      specifyDetailForm.get('eticttype')?.value !== 'WA'">+</span>

                <mat-form-field appearance="outline" class="form-field etic-field time-needed-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value &&
                                      specifyDetailForm.get('eticttype')?.value !== 'FIRM' &&
                                      specifyDetailForm.get('eticttype')?.value !== 'WA'">
                  <mat-label>Time</mat-label>
                  <input matInput type="number" class="time-needed-input" formControlName="timeNeeded" min="0" step="1" (input)="onTimeNeededInput($event)" (keydown)="preventNegativeInput($event)">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field etic-field time-field small-time-field"
                               *ngIf="specifyDetailForm.get('eticttype')?.value &&
                                      specifyDetailForm.get('eticttype')?.value !== 'FIRM' &&
                                      specifyDetailForm.get('eticttype')?.value !== 'WA'">
                  <mat-label>Units</mat-label>
                  <mat-select formControlName="timeUnits">
                    <mat-option *ngFor="let timeunit of timeUnitList" [value]="timeunit">{{ timeunit }}</mat-option>
                  </mat-select>
                </mat-form-field>

                <!-- Display of below field is hidden as per requirements -->
                <mat-form-field appearance="outline" class="form-field etic-field" style="display: none;">
                  <mat-label>Info</mat-label>
                  <input type="text" matInput formControlName="info"/>
                </mat-form-field>
                <mat-checkbox formControlName="ost" class="etic-checkbox">OST</mat-checkbox>
              </div>
            </div>
            <p *ngIf="eventtype !== 'OOS' && eventtype !== 'DOA'" class="etic-not-available">ETIC Details Not Applicable</p>
          </div>

          <div class="form-group contact-info">
            <span class="section-title">Contact Info (Optional)</span>
            <div class="contact-info-fields">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Owner</mat-label>
                <mat-select formControlName="owner">
                  @for (ow of ownerList; track ow) {
                    <mat-option [value]="ow">{{ow}}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Contact</mat-label>
                <input type="text" matInput formControlName="contact"/>
              </mat-form-field>
              <mat-form-field appearance="outline" class="form-field" *ngIf="eventtype !== 'DOA'">
                <mat-label>Responsible Mgr</mat-label>
                <input type="text" matInput formControlName="respMgr" [matAutocomplete]="auto"/>
                <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onManagerSelect($event)">
                  <mat-option *ngFor="let manager of filteredManagers" [value]="manager">
                    {{ manager }}
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
              <mat-form-field appearance="outline" class="form-field" *ngIf="eventtype !== 'DOA' && specifyDetailForm.get('station')?.value === 'MEM'">
                <mat-label>MEM Desk Contact</mat-label>
                <input type="text" matInput formControlName="memDesk"/>
              </mat-form-field>
            </div>
          </div>

          <div class="form-group other-details" *ngIf="eventtype !== 'NOTE'">
            <span class="section-title">Other Details</span>
            <div class="other-details-fields">
              <mat-form-field appearance="outline" class="form-field full-width">
                <mat-label>Affected Outbound Flight</mat-label>
                <mat-hint>Format: Flight Number | Date - Leg Number | Origin - Destination | Departure - Arrival</mat-hint>
                <mat-select formControlName="aof">
                  @for (af of aofList; track $index) {
                    <mat-option [value]="af">
                      <p class="aof-option">{{af.split("#")[0]}}</p>
                      <p class="aof-subtext">{{af.split("#")[1]}}</p>
                    </mat-option>
                  }
                </mat-select>
                <mat-error *ngIf="specifyDetailForm.get('aof')?.hasError('required')">Affected Flight is required</mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Intake Form -->
      <div #sectionRef *ngIf="eventtype !== 'NOTE'" class="step-section">
        <p class="step-title">Step 2: Intake Form</p>
        <div class="form-card">
          <app-intake-form [isAddEventSelected]="true" [acn]="acn" [eventValue]="eventtype" (addEventIntakeFormData)="setIntakeFormData($event)"></app-intake-form>
        </div>
      </div>

      <!-- Step 3: Discrepancies -->
      <div #sectionRef *ngIf="eventtype !== 'NOTE'" class="step-section">
        <p class="step-title">Step 3: Link Discrepancies</p>
        <div class="form-card">
          <app-discrepancies [isAddEventSelected]="true" [acn]="acn" [eventValue]="eventtype" (addDiscrepanciesData)="setSelectedDiscrepancyData($event)"></app-discrepancies>
        </div>
      </div>

      <!-- Step 4: Comments -->
      <div #sectionRef *ngIf="eventtype !== 'NOTE'" class="step-section">
        <p class="step-title">Step 4: Comments</p>
        <div class="form-card">
          <div class="comment-section">
            <mat-form-field appearance="outline" class="form-field comment-tag">
              <mat-label>Choose Exception/Remark</mat-label>
              <mat-select formControlName="selectedCommentTag">
                @for (st of superCommentExt; track st) {
                  <mat-option [value]="st">{{st}}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" class="form-field comment-text">
              <mat-label>Enter Comment</mat-label>
              <textarea maxlength="100" rows="2" matInput class="text-align-last-left" formControlName="comment"></textarea>
              <div id="comment-required-error" *ngIf="specifyDetailForm.get('comment')?.hasError('required') && specifyDetailForm.get('comment')?.touched">
                <mat-error>Comment is required</mat-error>
              </div>
              <div id="comment-maxlength-error" *ngIf="specifyDetailForm.get('comment')?.hasError('maxlength') && specifyDetailForm.get('comment')?.touched">
                <mat-error>Comment cannot exceed 100 characters</mat-error>
              </div>
            </mat-form-field>
          </div>
        </div>
      </div>

      <!-- Step 5: MSNs -->
      <div #sectionRef *ngIf="eventtype !== 'NOTE'" class="step-section">
        <p class="step-title">Step 5: Linked MSNs</p>
        <div class="form-card">
          <app-msn [isAddEventSelected]="true" [acn]="acn" [eventValue]="eventtype" (selectedMsnData)="setSelectedMsnData($event)" [discData]="selectedDiscrepancyData"></app-msn>
        </div>
      </div>

      <!-- Step 6: Tub File Notes -->
      <div #sectionRef class="step-section">
        <p class="step-title">
          Step {{ eventtype === 'NOTE' ? 2 : (specifyDetailForm.get('status')?.value === 'AOG' ? 6 : 5) }}: Enter Tub File Note
        </p>
        <div class="form-card">
          <mat-form-field appearance="outline" class="form-field full-width">
            <mat-label>New Tub File Note</mat-label>
            <textarea rows="10" [(ngModel)]="newTubFileNote" class="text-align-last-left" (input)="onTubFileNoteInputChange()" matInput formControlName="tfNote"></textarea>
            <mat-error *ngIf="specifyDetailForm.get('tfNote')?.hasError('required')">Tub File Note is required</mat-error>
          </mat-form-field>
        </div>
      </div>

      <!-- Step 7: Reporting Categories -->
      <div #sectionRef class="step-section">
        <p class="step-title">
          Step {{ eventtype === 'NOTE' ? 3 : (specifyDetailForm.get('status')?.value === 'AOG' ? 7 : 6) }}: Reporting Categories
        </p>
        <div class="form-card">
          <app-reporting-categories [eventValue]="eventtype" (formModelChange)="onFormModelUpdate($event)" (forwardRepCategories)="onRecdRepCats($event)"></app-reporting-categories>
        </div>
      </div>

      <!-- Step 8: NIW Timers (Conditional) -->
      <div #sectionRef *ngIf="specifyDetailForm.get('status')?.value === 'AOG' && eventtype !== 'NOTE'" class="step-section">
        <p class="step-title">
          Step {{ eventtype === 'NOTE' ? 4 : (specifyDetailForm.get('status')?.value === 'AOG' ? 8 : 7) }}: Select AOG Niw Timer
        </p>
        <div class="form-card">
          <div class="niw-timers-table-container">
            <table mat-table [dataSource]="niwTimerOptions" class="niw-timers-table mat-elevation-z2">
              <ng-container matColumnDef="timerName">
                <th mat-header-cell *matHeaderCellDef>NIW Timer Name</th>
                <td mat-cell *matCellDef="let timer" (click)="selectNiwTimer(timer)" [ngClass]="{'selected-row': selectedNiwTimer === timer}">
                  {{ timer }}
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="niwTableColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: niwTableColumns;"></tr>
            </table>
            <div class="error-message" *ngIf="niwTimerForm.touched && !selectedNiwTimer">
              Please select an NIW timer
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>