.dialog-title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: white;
  text-align: center;
  padding: 6px 12px; /* Reduced vertical padding */
  margin: 0;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  line-height: 1; /* Ensures no extra vertical spacing */
}

.dialog-container {
  padding: 35px 20px 5px 20px;
}

.section {
  display: flex;
  position: relative;
  border: 1px solid #ccc;
  background-color: #ffffff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  gap: 15px;
  padding: 15px;
}

.section-title {
  position: absolute;
  top: -14px;
  left: 16px;
  background: #ffffff;
  padding: 0 8px;
  font-size: 18px;
  font-weight: 500;
}

.error {
  position: relative;
  display: flex;
  justify-content: center;
  margin: 0;
  font-size: 14px;
  color: red;
  font-weight: 500;
}

.margin-bottom-35 {
  margin-bottom: 35px;
}

.margin-bottom-15 {
  margin-bottom: 15px;
}

mat-radio-group {
  display: block;
  margin-top: 10px;
}

mat-radio-button {
  display: block;
  margin-bottom: 10px;
  color: #6c49b9;
}

mat-checkbox {
  color: #6c49b9;
}

mat-form-field.small-input {
  display: inline-block;
  width: 133px;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.to-text {
  font-size: 14px;
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  padding: 0px 10px 25px 10px;
  gap: 15px;
}

.dialog-actions button:disabled {
  background-color: lightgray !important;
}

/* Buttons */
.action-button {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
}

.save {
  min-width: 85px;
  max-height: 32px;
  border-radius: 10px;
  padding: 2px 10px;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
  transition: 0.3s ease-in-out;
  background-color: #6c49b9 !important;
  color: white !important;
}

.save:hover {
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.close {
  min-width: 85px;
  max-height: 32px;
  border-radius: 10px;
  padding: 2px 10px;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
  transition: 0.3s ease-in-out;
  background-color: #ff6600 !important;
  color: white !important;
}

.close:hover {
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

::ng-deep .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
::ng-deep .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
  border-color: #6c49b9 !important; /* Change to your preferred color */
  background-color: #6c49b9 !important;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper, 
:host ::ng-deep .mat-mdc-form-field-bottom-align::before {
  display: none !important;
}

::ng-deep .mat-internal-form-field > label {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
}

::ng-deep .mat-mdc-radio-button .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle, 
::ng-deep .mat-mdc-radio-button .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
  border-color: #6c49b9 !important;
}
