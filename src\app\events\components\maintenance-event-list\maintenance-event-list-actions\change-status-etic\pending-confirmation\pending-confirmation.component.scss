.dialog-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  background: #f4f7fc;
  font-family: 'Roboto', sans-serif;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  overflow: auto;
  padding: 0 !important;
}

.dialog-header {
  text-align: center;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 12px;
  color: #ffffff;
  background: linear-gradient(135deg, #2a2a72, #6a5acd);
  border-radius: 12px 12px 0 0;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.dialog-header:hover {
  background: linear-gradient(135deg, #1c1c54, #5a4ab5);
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2a2a72;
  text-align: center;
  margin: 8px 0;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  position: relative;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title::after {
  content: '';
  display: block;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #6a5acd, #ff8c00);
  margin: 4px auto;
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scaleX(1); }
  50% { transform: scaleX(1.2); }
  100% { transform: scaleX(1); }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #ffffff;
  border-radius: 10px;
  margin: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.message-section {
  padding: 10px 20px;
  background: linear-gradient(135deg, #fef9f5, #f0f4ff);
  border-radius: 16px;
  border: 1px solid rgba(106, 90, 205, 0.2);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.message-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff8c00, #ff6f61);
  color: #fff;
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 0.95rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.4);
  gap: 10px;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.badge-icon {
  font-size: 20px;
}

.message-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-text strong {
  color: #fff;
  font-weight: 700;
  margin-left: 4px;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 16px;
  background: #f9fafb;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}

.data-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  justify-content: center;
  align-items: stretch;
}

.data-row.first-row {
  justify-content: space-between;
}

.data-row.second-row,
.data-row.third-row {
  justify-content: center;
  gap: 24px;
}

.data-item {
  background: #ffffff;
  border-radius: 10px;
  padding: 10px 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(106, 90, 205, 0.25);
  transition: all 0.3s ease-in-out;
  min-height: 60px;
  text-align: left;
  cursor: default;
}

.data-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(106, 90, 205, 0.2);
}

.data-item .label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #4b4b7c;
  text-transform: uppercase;
  margin-right: 10px;
  white-space: nowrap;
  cursor: default;
}

.data-item .value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #6a5acd;
  background: rgba(106, 90, 205, 0.07);
  padding: 4px 10px;
  border-radius: 6px;
  flex-grow: 1;
  text-align: right;
  word-break: break-word;
}

.comments-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.comment-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.text_area_style {
  height: 80px;
  opacity: 0.6;
  pointer-events: none;
  align-content: center;
  place-items: center;
}

.form-field {
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-wrapper .form-field:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(106, 90, 205, 0.25);
  transition: all 0.3s ease-in-out;
}

.form-field ::ng-deep .mat-form-field-wrapper {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.form-field:hover ::ng-deep .mat-form-field-wrapper {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
}

.form-field ::ng-deep .mat-form-field-outline {
  border: 1px solid rgba(106, 90, 205, 0.25);
  border-radius: 8px;
}

.form-field ::ng-deep .mat-form-field-infix {
  padding: 6px 0;
}

.form-field ::ng-deep .mat-form-field-label {
  color: #475569;
  font-weight: 500;
  font-size: 0.8rem;
}

.form-field:hover ::ng-deep .mat-form-field-label {
  color: #6a5acd;
}

.form-field ::ng-deep textarea {
  font-size: 0.8rem;
  line-height: 1.3;
  background: lavender;
  color: #6a5acd;
  border-radius: 8px;
  padding: 6px;
  resize: none;
}

.form-field ::ng-deep textarea:focus {
  background: #ffffff;
  box-shadow: 0 0 8px rgba(106, 90, 205, 0.4);
  border: 1px solid #6a5acd;
}

.dialog-footer {
  flex-shrink: 0;
  background: #ffffff;
  padding: 8px 12px;
  display: flex;
  gap: 12px;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 12px 12px;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.action-button {
  height: 35px;
  padding: 8px 35px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.yes-button {
  background: linear-gradient(135deg, #6a5acd, #483d8b);
  color: #ffffff;
  border: 1px solid rgba(106, 90, 205, 0.6);
}

.yes-button:hover {
  background: linear-gradient(135deg, #5a4ab5, #3c2f6b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(106, 90, 205, 0.3);
}

.no-button {
  background: linear-gradient(135deg, #ff8c00, #e67e22);
  color: #ffffff;
  border: 1px solid rgba(255, 140, 0, 0.6);
}

.no-button:hover {
  background: linear-gradient(135deg, #e67e22, #d2691e);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
}

.action-button:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.8;
}

mat-error {
  color: #e67e22;
  font-size: 0.8rem;
  margin-top: 6px;
  text-align: center;
  font-weight: 600;
  animation: fadeInError 0.5s ease forwards;
}

@keyframes fadeInError {
  0% { opacity: 0; transform: translateY(6px); }
  100% { opacity: 1; transform: translateY(0); }
}

.compact-note-card {
  width: 100%;
  padding: 12px;
  margin: 4px 0;
  background: linear-gradient(145deg, #ffffff, #f9fafb);
  border: 1px solid rgba(124, 58, 237, 0.25);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: default;
  position: relative;
}

.compact-note-card:hover {
  background: linear-gradient(145deg, #f0f0ff, #ede9fe);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(124, 58, 237, 0.3);
}

.compact-note-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  border-radius: 8px 0 0 8px;
  background: linear-gradient(180deg, #7c3aed, #f59e0b);
  transition: width 0.3s ease;
}

.compact-note-card:hover::before {
  width: 6px;
}

.tf-note-card {
  margin-left: 12px;
  margin-right: 12px;
}

.note-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.note-text {
  font-size: 0.9rem;
  color: #1e3a8a;
  line-height: 1.4;
  font-weight: 400;
  padding: 0 10px;
}

.note-meta-content {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #4b5563;
  background: rgba(124, 58, 237, 0.1);
  padding: 6px 8px;
  border-radius: 6px;
}

.emp-name {
  white-space: nowrap;
  font-weight: 600;
  margin-right: 25px;
  padding-left: 5px;
}

.update-time {
  color: #6b7280;
  font-style: italic;
}