.container {
  min-width: 100%;
  height: auto;
  background-color: whitesmoke;
  padding: 5px;
  transition: margin-left 0.3s ease-in-out, margin-top 0.5s ease-in-out;
}
.dropdown-card {
  width: 99.7%;
  box-shadow: 0 7px 15px 0 rgba(0, 0, 0, .13), 0 1px 4px 0 rgba(0, 0, 0, .11);
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}
.options-container {
  position: relative;
  width: 100%;
  background-color: whitesmoke;
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  text-align: center;
  gap: 25px;
  padding: 5px 0px;
}
.dropdown-container {
  position: relative;
  width: 100%;
  background-color: whitesmoke;
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  text-align: center;
  gap: 25px;
  padding: 5px 10px;
}
.mat-card-content {
  padding: 5px !important;
}
.select-field {
  width: 20%;
  position: relative;
  margin: 8px;
  background-color: lavender;
}
.checkbox {
  position: relative;
  display: flex;
  // margin-bottom: 8px;
}
.button-group {
  display: flex;
  justify-content: space-between;
  // margin-top: 8px;
  gap: 25px;
}

.events-count-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 6px 12px;
  // background-color: #f9f9f9;
  border-radius: 6px;
  // box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Replicate mat-option style */
.custom-mat-option {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  cursor: pointer;
  font-family: Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  user-select: none;
}

.custom-mat-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.custom-mat-option.disabled {
  pointer-events: none;
  opacity: 0.4;
}

.custom-mat-option .mat-mdc-option-pseudo-checkbox {
  margin-right: 12px;
}

.events-count {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.events-count .count {
  margin-left: 6px;
  font-weight: 600;
}

.vertical-separator {
  height: 28px;
  width: 3px;
  background-color: #ccc;
}

.add-acn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 260px;
}

.add-acn input {
  height: 30px;
  padding: 0 10px;
  font-size: 14px;
  font-weight: bolder;
  border: 1px solid transparent;
  border-radius: 10px;
  background: lavender;
  color: #6c49b9;
  text-align: center;
  transition: all 0.25s ease-in-out;
  box-shadow: inset 0 0 0 1px #ccc;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 👇 Gradient on focus */
.add-acn input:focus {
  // background: linear-gradient(135deg, #E6E6FA, #D8BFD8);
  background: lavender;
  color: #6c49b9;
  outline: none;
}

/* 👇 Placeholder font-size */
.add-acn input::placeholder {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

.add-acn button {
  height: 30px;
  border-radius: 10px;
  // background: linear-gradient(135deg, #cc5200, #ff6600) !important;
  background-color: #e65a00;
  color: white;
  min-width: 80px;
  padding: 0 12px;
  font-size: 12px;
  line-height: 1.2;
}

::ng-deep .add-acn button:disabled {
  cursor: not-allowed !important;
}

.add-acn button:disabled {
  height: 30px;
  background-color: lightgray;
  pointer-events: auto;
  cursor: not-allowed !important;
  min-width: 80px;
  padding: 0 12px;
  font-size: 13px;
  line-height: 1.2;
}

::ng-deep .add-acn button:not(:disabled):hover {
  background-color: #e65a00 !important;
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.button-group button {
  font-size: 14px;
  border-radius: 10px;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  padding: 2px 30px;
  color: white !important;
  transition: 0.3s ease;
}

.displayButton:hover {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.resetButton {
  background: linear-gradient(135deg, #cc5200, #ff6600) !important;
  color: white !important;
  padding: 2px 32.5px !important; 
}

.button-group button:hover, .actions-buttons button:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}

.events-summary {
  margin: 5px 0px 10px 0;
  position: relative;
  width: 100%;
  height: 3%;
  padding: 0px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 50;
  transition: transform 0.6s ease-in-out, margin-top 0.5s ease-in-out;
}

.events-count .label {
  font-size: 15px !important;
  color: #6c49b9 !important;
  font-weight: bolder !important;
}

.events-count {
  font-size: 18px !important;
  font-weight: 500 !important;
}

.count {
  font-weight: bolder !important;
  color: #ff6600 !important;
  margin-left: 5px !important;
}

.actions-buttons {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between buttons */
}

/* Button styles */
.actions-buttons button {
  padding: 15px; /* Smaller padding for compact size */
  font-size: 12px;  /* Smaller font size */
  height: 25px;     /* Decrease height for smaller appearance */
  min-width: 80px;  /* Adjust minimum width if needed */
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white !important;
  border-radius: 8px;
}

.actions-buttons button:hover {
  background-color: #6c49b9 !important;
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}


.actions-buttons button:disabled {
  background: lightgray !important;
  color: none !important;
  cursor: not-allowed !important;
}

.detailsButton {
  background-color: #1976d2;
  color: white;
}

.actionsButton {
  background-color: #28a745;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
.events-summary {
  flex-direction: column;
  text-align: center;
  padding: 15px;
}

.actions-buttons {
  margin-top: 10px;
  justify-content: center;
}
}


.actions-buttons {
  display: flex;
  gap: 10px;
}

.actionMenuCard {
  position: relative !important;
  font-size: 14px;
  display: flex;
  text-align: center !important;
}

.actionButtonMenu {
  display: flex;
  justify-content: center !important;
  text-align: center !important;
}

.actionButtonMenu:hover {
  background-color: #6c49b9 !important;
  color: white !important;
}

.actionButtonMenu:disabled {
  background-color: whitesmoke !important;
  color: black !important;
  cursor: not-allowed;
}

.actionMenuCard button:hover {
  background-color: #6c49b9;
  color: white;
}

.actionMenuCard button:disabled {
  cursor: not-allowed !important;
}

.table-container {
  position: relative;
  bottom: 7px;
  flex-grow: 1;
  min-height: 0;
  width: 100%;
  transition: transform 1s ease-in-out, border 1s ease, box-shadow 1s ease, margin-top 1s ease-in-out;
  transition: width 0.3s ease;
  border-radius: 10px !important;
  overflow: hidden !important;
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}

/* Shift Up When Mat-Card is Closed */
.shift-up {
  margin-top: -108px;
  transition: transform 0.6s ease-in-out, border 0.6s ease, box-shadow 0.6s ease, margin-top 0.6s ease-in-out;
}

.shift-up-table {
  transition: transform 0.6s ease-in-out, border 0.6s ease, box-shadow 0.6s ease, margin-top 0.6s ease-in-out;
}


.ag-grid-table {
  position: relative;
  transition: all 0.6 ease-in-out, margin-top 0.6s ease-in-out, height 0.6s ease-in-out;
  width: 100%;
  --ag-selected-row-background-color: #fae4d6;
}

::ng-deep .ag-row.ag-row-selected {
  // background-color: #D2C6E1 !important; // grey purple
  // background-color: #D8C1F1 !important; //lavender purple
  background-color: #fae4d6;
  // background-color: #D2C6E1 !important; // greyish purple
  // background-color: #B7BBE3 !important; // blur purple
}

::ng-deep .ag-grid-table .ag-row-hover {
  cursor: pointer;
  background-color: #fae4d6;
}

::ng-deep .ag-grid-table .ag-row-hover .ag-cell {
  cursor: pointer;
  background-color: #fae4d6;
}

.ag-theme-alpine {
  height: 100%;
  transition: all 0.6 ease-in-out, margin-top 0.5s ease-in-out, height 0.6s ease-in-out;
  transition: width 0.3s ease;
  box-shadow: 0 7px 30px -10px rgba(150,170,180,0.5);
  }

.ag-theme-quartz {
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.ag-theme-alpine.ag-grid-table {
  border-radius: 10px !important;
  overflow: hidden !important;
  overflow-y: hidden !important
}

.ag-header-cell {
    background-color: #333;
    color: #fff;
    font-weight: bold;
    text-align: center;
}

:host ::ng-deep .ag-root-wrapper.ag-layout-normal {
  border-radius: 10px;
}

::ng-deep.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked, 
::ng-deep .mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate {
  border-color: #3F2876 !important;
  background-color: #6c49b9 !important;
}


::ng-deep .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
::ng-deep .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
  border-color: #3F2876 !important;
  background-color: #6c49b9 !important;
}

::ng-deep .ag-header-container, 
::ng-deep .ag-floating-top-container, 
::ng-deep .ag-floating-bottom-container, 
::ng-deep .ag-sticky-top-container, 
::ng-deep .ag-sticky-bottom-container {
  background-color: #B7BBE3 !important;
  // background-color: #D2C6E1 !important;
}

::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
  background-color: transparent !important;
}

/* Remove all cell and row borders */
:ng-deep .ag-theme-alpine .ag-root,
:ng-deep .ag-theme-alpine .ag-header,
:ng-deep .ag-theme-alpine .ag-row,
:ng-deep .ag-theme-alpine .ag-cell {
  border: none !important;
}

/* Remove row gaps */
:ng-deep .ag-theme-alpine .ag-row {
  margin: 0;
  padding: 0;
}

/* Remove header bottom border */
:ng-deep .ag-theme-alpine .ag-header-row {
  border-bottom: none !important;
}

:ng-deep .ag-header-group-cell-label, 
:ng-deep .ag-header-cell-label {
  justify-content: center !important;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper,
::ng-deep .mat-mdc-form-field-bottom-align::before {
  display: none !important;
}

::ng-deep .mat-mdc-select-value-text {
  font-weight: bolder !important;
  color: #6c49b9 !important;
}

.disable-pointer-events {
  pointer-events: auto !important; /* Allow interaction */
}

.menuLabel {
  font-weight: bold;
  color: black;
}



.custom-context-menu {
  position: fixed;
  background: white;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  z-index: 1000;
  padding: 8px 0;
  min-width: 180px;
}

.custom-context-menu button {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  // padding: 10px 16px;
  text-align: left;
  border: none;
  background: none;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
}

.custom-context-menu button:disabled {
  background-color: whitesmoke !important;
  color: black !important;
  cursor: not-allowed !important;
  font-weight: normal !important;
}

.custom-context-menu button:hover {
  background-color: #6c49b9;
  color: white;
}







/* Mat-card transition with animation */
.dropdown-card {
  width: 99.7%;
  z-index: 100;
  box-shadow: 0 7px 15px 0 rgba(0, 0, 0, 0.13), 0 1px 4px 0 rgba(0, 0, 0, 0.11);
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
}

/* Toggle button is directly below the mat-card */
.toggle-btn {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: linear-gradient(135deg, #cc5200, #ff6600) !important;
  position: absolute;
  left: 50%;
  top: 85%;
  width: 8%;
  height: 30%;
  transform: translateX(-50%);
  bottom: -30px;
  transition: transform 0.65s ease-in-out, bottom 0.65s ease-in-out, opacity 0.65s ease-in-out;
  z-index: 90;
}

.cardClosedButtonState {
  top: 90% !important;
}

.toggle-btn:hover{
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.mat-icon {
  position: absolute !important;
  bottom: 0 !important;
  color: white !important;
}

/* When hidden, move everything up */
.hidden .dropdown-card {
  transform: translateY(-150%);
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
}

/* Move toggle button up with the mat-card */
.hidden .toggle-btn {
  width: 8%;
  height: 30%;
  bottom: 0;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  transform: translate(-50%, -370%);
  opacity: 1;
  z-index: 90;
}

::ng-deep .no-padding-dialog .mat-dialog-container {
  padding: 0 !important;
  border-radius: 16px;
}

::ng-deep .ag-cell[col-id="curComment"] {
  justify-content: flex-start !important;
  text-align: left !important;
}

::ng-deep .ag-cell .delete-icon {
  color: red;
  font-weight: bold;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}

::ng-deep .ag-cell .delete-icon {
  color: red;
  font-weight: bold;
  cursor: pointer;
  font-size: 22px;
  display: inline-block;
  transition: all 0.2s ease;
}

::ng-deep .ag-cell .delete-icon:hover {
  color: darkred;
  transform: scale(1.1);
}
