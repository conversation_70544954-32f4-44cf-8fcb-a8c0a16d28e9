import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {

  constructor() { }

  private requestCount = 0;

  spinnerSubject = new BehaviorSubject(false);
  private tableLoading = new BehaviorSubject<boolean>(false);

  isEventListApiCallInitiated = new BehaviorSubject<boolean>(false);
  isEventListApiCallInitiated$ = this.isEventListApiCallInitiated.asObservable();

  // expose the BehaviorSubject as an Observable
  spinnerSubjectObservable$ = this.spinnerSubject.asObservable();
  tableLoadingObservable$ = this.tableLoading.asObservable();

  showSpinner() {
    this.requestCount++;
    this.spinnerSubject.next(true);
  }

  hideSpinner() {
    this.requestCount--;
    if (this.requestCount <= 0) {
      this.requestCount = 0; // Reset to zero if it goes negative
      this.spinnerSubject.next(false);
    }
  }

  setTableLoading(isLoading: boolean) {
    this.tableLoading.next(isLoading);
  }

  setEventListApiCallInitiated(isInitiated: boolean) {
  this.isEventListApiCallInitiated.next(isInitiated);
  }
}
