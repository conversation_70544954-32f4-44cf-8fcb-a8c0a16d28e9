<div class="questions-container">
    <div class="questions-panel">
      <h2 class="questions-header">Previous Questions List</h2>
      <div class="questions-content">
        <mat-card *ngFor="let question of questions; let i = index" class="question-item">
          <mat-card-header>
          <mat-card-title><p style="color: black;">Question:</p>{{ question.questionTxt }}</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <p style="display: flex;flex-direction: row;align-items: center;margin: 0;">Type: {{ question.questionGrp }}</p>
            <div class="answers-list">
              <p style="margin: 0;">Answers:</p>
              <mat-list style="display: flex;flex-direction: row;gap: 10px;">
                <mat-list-item *ngFor="let answer of question.answers" style="padding: 0px;white-space: normal !important; overflow-wrap: break-word;">
                  {{ answer.answerTxt }}
                </mat-list-item>
              </mat-list>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-icon-button color="primary" (click)="editQuestion(question)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteQuestion(question)">
              <mat-icon>delete</mat-icon>
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>