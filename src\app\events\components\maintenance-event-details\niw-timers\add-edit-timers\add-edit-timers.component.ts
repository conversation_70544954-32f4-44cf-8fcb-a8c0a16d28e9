import { Component, Inject, ChangeDetectionStrategy, inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import {provideNativeDateAdapter} from '@angular/material/core';
import moment from 'moment';
import { ToastrMessageService } from '../../../../../app-layout/services/toastr-message.service';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { DateAdapter } from '@angular/material/core';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
    selector: 'app-add-edit-timers',
    templateUrl: './add-edit-timers.component.html',
    styleUrl: './add-edit-timers.component.scss',
    providers: [provideNativeDateAdapter()],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class AddEditTimersComponent {

    timerType:string = '';
    timerName:string = '';
    timerId:string = '';
    eventId:string = '';

    timerData: any = {
      startDate: null,
      startTime: null,
      endDate: null,
      endTime: null
    }; 

    constructor(private dialogRef: MatDialogRef<AddEditTimersComponent>,
                private toastrMessageService: ToastrMessageService,
                @Inject(MAT_DIALOG_DATA) public data: any,
                private maintenanceEventDetailsService: MaintenanceEventDetailsService,
                private dateAdapter: DateAdapter<Date>
              ) {
                this.dateAdapter.setLocale('en-US');
              }

    ngOnInit() {
      this.timerName = this.data.timerName;
      this.timerId = this.data.timerId;
      this.eventId = this.data.eventId;
      this.timerType = this.data.type;
      if(this.data.startDateTime != null && this.data.endDateTime != null) {
        this.formatDateTime();
      }
    }

    formatDateTime() {
      let startDateTime = this.data.startDateTime.split(' ');
      let endDateTime = this.data.endDateTime.split(' ');
      this.timerData.startDate = moment(startDateTime[0], 'DD/MMM/YY').toDate();
      this.timerData.endDate = moment(endDateTime[0], 'DD/MMM/YY').toDate();

      let startTimeData = startDateTime[1].split(':').map(Number);
      let endTimeData = endDateTime[1].split(':').map(Number);

      let startTime = new Date();
      startTime.setHours(startTimeData[0], startTimeData[1], 0, 0);
      startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      this.timerData.startTime = startTime;

      let endTime = new Date();
      endTime.setHours(endTimeData[0], endTimeData[1], 0, 0);
      endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      this.timerData.endTime = endTime;
    }

    saveNiwTimer() {
      let data = {
        'mode':'NIW_TIMERS',
        'flag': this.timerType.toUpperCase(),
        'user_id': this.data.userId,
        'token_id': this.data.userId,
        'event_active':true,
        'niw_timer_data':{
              'eventTimersPk': {
                'eventId': this.eventId,
                'creationDtTm': this.timerType.toUpperCase() == 'EDIT' ? this.data.creationTime.split(" ")[0]+ "T" + this.data.creationTime.split(" ")[1] : ''
              },
              'timerId': this.timerId,
              'timerStartDtTm': `${moment(this.timerData['startDate']).format('YYYY-MM-DD')}T${this.formatTime(this.timerData['startTime'])}`,
              'timerStopDtTm': `${moment(this.timerData['endDate']).format('YYYY-MM-DD')}T${this.formatTime(this.timerData['endTime'])}`,
              'lastUpdateDtTm': this.timerType.toUpperCase() == 'EDIT' ?this.data.lastUpdate.split(" ")[0]+ "T" + this.data.lastUpdate.split(" ")[1]: ''
          }
      }
      this.maintenanceEventDetailsService.updateNiwTimers(data).subscribe((result) => {
        this.timerData = { startDate: null, startTime: null, endDate: null, endTime: null};
        this.closeDialog(result, this.timerType);
      });
    }

    formatTime(date: any) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    }

    closeDialog(data: any = null, timerType: any = null) {
      if(data != null && data['UPDATED_TIMER_DETAILS'].length != 0) {
        this.toastrMessageService.success(`Timer ${timerType}ed Successfully!`);
      } else if (data != null && data['UPDATED_TIMER_DETAILS'].length == 0) {
        this.toastrMessageService.error(`Timer ${timerType} Failed`);
      }
      this.dialogRef.close(data);
    }

}
