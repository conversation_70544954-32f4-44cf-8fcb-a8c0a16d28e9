import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SessionStorageKeys } from '../../events/constants/sessionStorageKeys';
import { MaintenanceEventListService } from '../../events/services/maintenance-event-list.service';
import { UserAddedAcn } from '../../events/constants/userAddedAcn';

@Injectable({
  providedIn: 'root'
})
export class MainService {

  private detailsTabsUpdated: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  detailsTabUpdatedObservable$ = this.detailsTabsUpdated.asObservable();

  private moveScrollToAcn: BehaviorSubject<string> = new BehaviorSubject<string>('');
  moveScrollToAcnObservable$ = this.moveScrollToAcn.asObservable();

  private removeSelectedAcnTab: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  removeSelectedAcnTabObservable$ = this.removeSelectedAcnTab.asObservable();

  private refreshAcnTabData: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  refreshAcnTabDataObservable$ = this.refreshAcnTabData.asObservable();

  constructor(private http: HttpClient, private maintenanceEventListService: MaintenanceEventListService) {}

  addAcnTabDataToSessionStorage(item: any): void {
    let sessionStorageValue = this.getAcnTabDataFromSessionStorage() || [];
    const itemName = item?.name?.toLowerCase()?.trim();
    const exists = sessionStorageValue.some(existing => existing?.name?.toLowerCase()?.trim() === itemName);

    if (!exists) {
      sessionStorageValue.push(item);
      sessionStorageValue = this.sortBasedOnAscOrder(sessionStorageValue);
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, sessionStorageValue);
      this.triggerUpdateDetailsTabs(true);
    }
  }

  setAcnNameForTabInSessionStorage(acn: string, eventId: string, eventType: string): void {
    let sessionStorageValue = this.getAcnTabDataFromSessionStorage();
    if (sessionStorageValue && acn) {
      const selectedTab = sessionStorageValue.find((value: any) => value.selected);
      const selectedTabIndex = sessionStorageValue.findIndex((value: any) => value.selected);
      if (selectedTab) {
        selectedTab.name = `ACN - ${acn}`;
        selectedTab.eventId = eventId;
        selectedTab.eventType = eventType;
        selectedTab.userAdded = true;
        sessionStorageValue[selectedTabIndex] = selectedTab;
      }
      sessionStorageValue = this.sortBasedOnAscOrder(sessionStorageValue);
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, sessionStorageValue);
      this.triggerUpdateDetailsTabs(true);
    }
  }

  setAcnTabsDatainSessionStorage(data: any) {
    const uniqueData = [...new Map(data.map((item: any) => [item.name.toLowerCase(), item])).values()];
    this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, uniqueData);
    this.triggerUpdateDetailsTabs(true);
  }

  sortBasedOnAscOrder(sessionStorageValue: any): any {
    return sessionStorageValue.sort((a: any, b: any) => a.name.localeCompare(b.name));
  }

  setSelectedTabDataToSessionStorage(item: any) {
    const sessionStorageValue = this.getAcnTabDataFromSessionStorage();
    if (sessionStorageValue) {
      sessionStorageValue.forEach((value: any) => (value.selected = value.name.toLowerCase() === item.name.toLowerCase()));
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, sessionStorageValue);
      this.triggerUpdateDetailsTabs(true);
      this.moveScrollToSelectedAcn(item.name);
    }
  }

  deleteDetailsTabDataFromSessionStorage(item: any) {
    let sessionStorageValue = this.getAcnTabDataFromSessionStorage();
    if (sessionStorageValue) {
      sessionStorageValue = sessionStorageValue.filter((value: any) => value.name.toLowerCase() !== item.name.toLowerCase());
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, sessionStorageValue);
      this.deleteUserAddedEventListAcnInStorage(item.name.split(' - ')[1]?.trim(), item.eventId, item.eventType);
      this.triggerUpdateDetailsTabs(true);
    }
  }

  deleteUserAddedEventListAcnInStorage(acn: string, eventId: string, eventType: string): void {
    const userAddedAcnList = this.getItem<UserAddedAcn[]>(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN) || [];
    const index = userAddedAcnList.findIndex((value: UserAddedAcn) => value.acn === acn && value.eventId === eventId && value.eventType === eventType);
    if (index !== -1) {
      userAddedAcnList.splice(index, 1);
      this.setItem(SessionStorageKeys.USER_ADDED_EVENT_LIST_ACN, userAddedAcnList);
      this.maintenanceEventListService.refreshEventListTableData(acn);
    }
  }

  deselectSelectedTabInSessionStorage() {
    const sessionStorageValue = this.getAcnTabDataFromSessionStorage();
    if (sessionStorageValue) {
      sessionStorageValue.forEach((value: any) => (value.selected = false));
      this.setItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA, sessionStorageValue);
      this.triggerUpdateDetailsTabs(true);
      this.deselectSelectedAcnTabFromList();
    }
  }

  clearAllTabsInSessionStorage() {
    sessionStorage.removeItem(SessionStorageKeys.ACN_DETAILS_TAB_DATA);
    this.triggerUpdateDetailsTabs(true);
  }

  getUserDetailsFromStorage(): any {
    return this.getItem<any>(SessionStorageKeys.LOGGED_USER_DETAILS);
  }

  getAcnTabDataFromSessionStorage() {
    return this.getItem<any[]>(SessionStorageKeys.ACN_DETAILS_TAB_DATA) || [];
  }

  deselectSelectedAcnTabFromList() {
    this.removeSelectedAcnTab.next(true);
  }

  triggerUpdateDetailsTabs(value: boolean) {
    this.detailsTabsUpdated.next(value);
  }

  moveScrollToSelectedAcn(acn: string) {
    this.moveScrollToAcn.next(acn);
  }

  refreshDetailsAcnTabData(value: boolean) {
    this.refreshAcnTabData.next(value);
  }

  private setItem(key: string, value: any): void {
    if (value !== null && value !== undefined) {
      sessionStorage.setItem(key, JSON.stringify(value));
    }
  }

  private getItem<T>(key: string): T | null {
    const storedValue = sessionStorage.getItem(key);
    if (!storedValue) return null;
    try {
      return JSON.parse(storedValue) as T;
    } catch (error) {
      console.error(`Error parsing sessionStorage key: ${key}`, error);
      return null;
    }
  }

}
