<div class="container">
    <div *ngIf="!isAddEventSelected" class="header-container">
        <p  class="title" [ngClass]="{ 'addTitle': isAddEventSelected, 'header-title': !isAddEventSelected, 'fill-animation': animateTitle }"> MSNs</p>
        <div *ngIf="!isAddEventSelected" class="button-container" [class.show]="true">
          <button [disabled]="!isUpdateEnabled" mat-raised-button class="button">Update</button>
        </div>
    </div>
  
    <div class="table-container">
      <ag-grid-angular
        #agGrid
        id="agGridTable"
        class="ag-theme-alpine ag-grid-table"
        [style.height.px]="gridHeight"
        [rowData]="filteredTableData"
        [columnDefs]="columnDefs"
        [gridOptions]="gridOptions"
        [domLayout]="'normal'"
        [rowSelection]="'single'"
        [autoHeaderHeight]="true"
        (cellValueChanged)="onCellValueChanged($event)"
        (gridReady)="onGridReady($event)"
        [modules]="modules">
      </ag-grid-angular>
    </div>
  </div>