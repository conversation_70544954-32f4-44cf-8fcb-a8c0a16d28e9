.dialog-title-bar {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: #ffffff;
  padding: 12px 24px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialog-title-text {
      font-size: 1.25rem;
      font-weight: 600;
      flex-grow: 1;
      text-align: center;
    }

    .close-icon {
      color: red;
      font-size: 24px;
      cursor: pointer;

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.niw-timer-dialog {
  padding: 15px 30px;
  align-content: center;

  .label {
    font-weight: 500;
    margin-bottom: 16px;
  }

  .field-row {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-bottom: 20px;

    .form-field {
      flex: 1;
    }
  }
}

    .timer-label {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;
      font-weight: bold;

      .label {
        font-size: 15px;
        color: #6c49b9;
      }

      .timer-name {
        font-size: 18px;
        color: #ff6600;
        margin-left: 0.5rem;
      }
    }

  .dialog-actions {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: center;

    button {
      min-width: 18%;
      font-weight: 600;
      border-radius: 10px;
      padding: 0.5rem 1rem;
      color: white;
      transition: background 0.3s ease;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .btn-purple {
      background-color: #6c49b9;

      &:hover {
        background-color: #3F2876;
      }
    }
  }

.niw-timer-button {
  padding: 6px 24px;
  font-weight: 500;
  border-radius: 4px;
}