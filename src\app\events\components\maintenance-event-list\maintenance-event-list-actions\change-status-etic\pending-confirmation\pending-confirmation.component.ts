import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TubFileNotesResponseDto } from '../../../../../dto/TubFileNotesResponseDto';

@Component({
  selector: 'app-pending-confirmation',
  standalone: false,
  templateUrl: './pending-confirmation.component.html',
  styleUrl: './pending-confirmation.component.scss'
})
export class PendingConfirmationComponent {

  currentComment: string = '';
  newComment: string = '';
  sortOrder: 'asc' | 'desc' = 'desc'; // Default sort order  
  tubfilenotesresponse: TubFileNotesResponseDto[] = [];
  latestTubFileEntry: TubFileNotesResponseDto | null = null;

  constructor(
    public dialogRef: MatDialogRef<PendingConfirmationComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { selectedRow: any, tubfilenote: TubFileNotesResponseDto[] },
  ) {
    this.currentComment = data.selectedRow?.curComment || '';
    this.newComment = data.selectedRow?.newComment || '';
    this.tubfilenotesresponse = [...data.tubfilenote].sort((a, b) => {
      const dateA = new Date(a.lastUpdateDtTm).getTime();
      const dateB = new Date(b.lastUpdateDtTm).getTime();
      return this.sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
    });
    this.latestTubFileEntry = this.tubfilenotesresponse[0];
  }

  onYes(): void {
    this.dialogRef.close({ modify: true });
  }

  onNo(): void {
    this.dialogRef.close({ modify: false });
  }

}
