.footer {
    background-color: #7f7b7d; /* Header background color */
    display: flex;
    justify-content: center;
    position: relative;
    height: 100%;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    padding: 0.1% 0;
  }
  
  .nav-bar {
    display: flex;
    align-items: center; /* Center vertically */
    justify-content: space-between; /* Space between logo and links */
  }  

  ul {
    list-style-type: none; /* Remove default list styles */
    display: flex; /* Horizontal layout */
    margin: 0;
    padding: 0;
  }
  
  .nav-item {
    margin-right: 20px; /* Spacing between items */
  }
  
  .nav-item.selected-page .link {
    font-weight: bold; /* Highlight selected page */
    text-decoration: underline; /* Underline selected page */
  }
  
  .link {
    text-decoration: none; /* Remove underline */
    color: #ffffff; /* Change text color to white for contrast */
  }
  
  .link:hover {
    color: #fc6404; /* Color change on hover */
  }
  
  #nav-bar-info {
    margin-left: auto; /* Push user info to the right */
    background-color: #fc6404; /* Username section background color */
    padding: 5px 10px; /* Padding for the username section */
    border-radius: 5px; /* Rounded corners */
    color: #ffffff; /* Change text color to white for contrast */
  }
  
  