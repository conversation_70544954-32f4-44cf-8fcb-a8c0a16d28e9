.custom-checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
}
.custom-checkbox {
  position: relative;
  display: flex;
  width: 16px;
  height: 16px;
  cursor: pointer;
  appearance: none; /* Remove default checkbox styling */
  border: 2px solid rgba(0, 0, 0, 0.4);
  background-color: white;
  border-radius: 3px; /* Optional: rounded corners */
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.custom-checkbox:checked {
  background-color: #6c49b9;
  border-color: #6c49b9;
}

.custom-checkbox:checked::before {
  content: '✔';
  font-size: 14px;
  color: white; /* White tick */
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Styles (add to your component.scss or global styles) */
.clickable-link {
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
}

::ng-deep .mat-dialog-container {
  transition: height 0.3s ease;
}

::ng-deep .custom-msn-dialog .mat-dialog-container {
  padding: 0;
  max-height: 600px !important;        // Only cap the height
  overflow: hidden auto !important;    // Allow scroll when needed
  width: 95vw !important;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}