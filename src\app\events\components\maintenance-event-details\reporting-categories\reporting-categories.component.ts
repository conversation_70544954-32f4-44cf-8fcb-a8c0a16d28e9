import { Component, SimpleChanges, Input, Output, EventEmitter } from '@angular/core';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';

@Component({
  selector: 'app-reporting-categories',
  standalone: false,
  templateUrl: './reporting-categories.component.html',
  styleUrl: './reporting-categories.component.scss'
})
export class ReportingCategoriesComponent {

  @Input() eventValue: string = '';

  @Output() formModelChange = new EventEmitter<any>();

  @Output() forwardRepCategories = new EventEmitter<any>();

  eventType: string = '';

  eventId: string = '';

  eventMode: string = 'REPORT_CATEGORIES';

  levelMapByIdAndName: any = {};

  levelMapByIdAndNameOpt: any = {};

  requestCatPayload : any = {
    "eventType": "",
    "eventId": this.eventId,
    "mode" : this.eventMode,
    "accessLevel" : "90",
    "userId" : "5945348",
    "tokenId" : "1234567"
  }

  updateCatpayload : any = {
    "mode": this.eventMode,
    "access_level":"90",
    "user_id":"5945348",
    "token_id":"5678",
    "event_active":true,
    "report_categories_data": []
  };

  reportingCategoriesTemplate : any = {
    "eventId": this.eventId,
    "levelOneId": "",
    "levelTwoId": "",
    "updatedLevelOneId": "",
    "updatedLevelTwoId": "",
    "isModified": true,
    "lastUpdatedTime": "",
  };

  formModel: any = {
    'Mx_Type': '',
    'DOA_Reasons': {},
    'Initial_Reason': '',
    'Departure_Prob': '',
    'Delay': '',
    'ETIC_Driver': '',
    'Miscellaneous': {}
  };

  oldDataformModel: any;

  reportingTypes: any = {
    'Mx_Type' : { 'name' : 'Mx Type', 'category' : 'radio', 'values' : []},
    'Departure_Prob' : { 'name' : 'Departure Prob', 'category' : 'radio', 'values' : []},
    'Initial_Reason' : { 'name' : 'Initial Reason', 'category' : 'radio', 'values' : []},
    'DOA_Reasons' : { 'name' : 'DOA_Reasons', 'category' : 'checkbox', 'values' : []},
    'ETIC_Driver' : { 'name' : 'ETIC_Driver', 'category' : 'radio', 'values' : []},
    'Miscellaneous' : { 'name' : 'Miscellaneous', 'category' : 'checkbox', 'values' : []},
    'Delay' : { 'name' : 'Delay', 'category' : 'radio', 'values' : []}
  }

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService) {}

  ngOnInit() {
    if(this.eventValue == '') {
      this.getReportingCategories(this.requestCatPayload);
    } else {
      this.requestCatPayload['eventType'] = this.eventValue;
      this.getReportingCategories(this.requestCatPayload);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") { 
        this.eventId = this.detailsViewObj['eventID'].toString();
        this.eventType = this.detailsViewObj['eventType'];
        this.requestCatPayload['eventId'] = this.eventId;
        this.requestCatPayload['eventType'] = this.eventType;
        this.reportingCategoriesTemplate['eventId'] = this.eventId;
        this.getReportingCategories(this.requestCatPayload);
      }
    }

    if (changes['eventValue']) {
      this.requestCatPayload['eventType'] = this.eventValue;
      this.getReportingCategories(this.requestCatPayload);
    }
  }

  getReportingCategories(data: any) {
    this.maintenanceEventDetailsService.getReportingCategories(this.requestCatPayload['eventId'], this.requestCatPayload['eventType']).subscribe((result) => {
      this.forwardRepCategories.emit(result);
      this.processCategories(result);
    })
  }

  emitFormChanges() {
    this.formModelChange.emit(this.formModel);
  }

  processCategories(result: any) {
    Object.keys(this.reportingTypes).forEach(key => {
      this.reportingTypes[key]['values'] = [];
    });
    let prop = "reportingCategories";
    let prop2 = "reportingCategoryKeys";
    if(!result.hasOwnProperty(prop)){
      prop = "REPORT_CATEGORIES";
    }
    if(!result.hasOwnProperty(prop2)){
      prop = "REPORT_CATEGORIES_KEY_VALUES";
    }
    let reportCats = result[prop];
    for(let i of reportCats) {
      this.reportingTypes[i['level1Name']]['values'].push(i['level2Name']);
      if(i['level1Name'] == 'DOA_Reasons' || i['level1Name'] == 'Miscellaneous') {
        this.formModel[i['level1Name']][i['level2Name']] = false;
      }
      this.levelMapByIdAndName[i['level1Id']] = i['level1Name'];
      this.levelMapByIdAndNameOpt[i['level2Name']] = i['level2Id'];
    }
    this.levelMapByIdAndNameOpt[""] = "";
    if(result.hasOwnProperty('reportingCategoryKeys') || result.hasOwnProperty('REPORT_CATEGORIES_KEY_VALUES')) {
      let selectedCats = result[prop2];
      this.oldDataformModel = [ ...selectedCats ];
      for(let i of selectedCats) {
        if(this.levelMapByIdAndName[i['level1Id']] == 'DOA_Reasons' || this.levelMapByIdAndName[i['level1Id']] == 'Miscellaneous') {
          this.formModel[this.levelMapByIdAndName[i['level1Id']]][i['level2Name']] = true;
        } else {
          this.formModel[this.levelMapByIdAndName[i['level1Id']]] = i['level2Name'];
        }
      }
    }
  }

  saveCategories() {
    if(this.eventValue != '') {
      return;
    }
    this.updateCatpayload['report_categories_data'] = [];
    Object.keys(this.formModel).forEach(key => {
      if(key == 'DOA_Reasons' || key == 'Miscellaneous') {
        Object.keys(this.formModel[key]).forEach(key2 => {
          let flag = false;
          for(let i = 0; i < this.oldDataformModel.length;i++) {
            if(this.oldDataformModel[i]['level2Id'] == this.levelMapByIdAndNameOpt[key2]) {
              flag = true;
              if(this.formModel[key][key2] == false) {
                let reportingCategoriesData = { ...this.reportingCategoriesTemplate };
                reportingCategoriesData['levelOneId'] =  this.getKeyByValue(this.levelMapByIdAndName, key);
                reportingCategoriesData['levelTwoId'] = this.getFromOldData(this.getKeyByValue(this.levelMapByIdAndName, key), 'level2Id', key, key2);
                reportingCategoriesData['updatedLevelOneId'] = this.getKeyByValue(this.levelMapByIdAndName, key);
                reportingCategoriesData['updatedLevelTwoId'] = "";
                reportingCategoriesData['lastUpdatedTime'] = this.getFromOldData(this.getKeyByValue(this.levelMapByIdAndName, key), 'lastUpdatedDtTm', key, key2);
                this.updateCatpayload['report_categories_data'].push(reportingCategoriesData);
              }
            }
          }
          if(flag == false && this.formModel[key][key2] == true) {
            let reportingCategoriesData = { ...this.reportingCategoriesTemplate };
            reportingCategoriesData['levelOneId'] =  this.getKeyByValue(this.levelMapByIdAndName, key);
            reportingCategoriesData['levelTwoId'] = "";
            reportingCategoriesData['updatedLevelOneId'] = this.getKeyByValue(this.levelMapByIdAndName, key);
            reportingCategoriesData['updatedLevelTwoId'] = this.levelMapByIdAndNameOpt[key2];
            this.updateCatpayload['report_categories_data'].push(reportingCategoriesData);
          }
        })
      } else {
        let reportingCategoriesData = { ...this.reportingCategoriesTemplate };
        reportingCategoriesData['levelOneId'] =  this.getKeyByValue(this.levelMapByIdAndName, key);
        reportingCategoriesData['levelTwoId'] = this.getFromOldData(this.getKeyByValue(this.levelMapByIdAndName, key), 'level2Id');
        reportingCategoriesData['updatedLevelOneId'] = this.getKeyByValue(this.levelMapByIdAndName, key);
        reportingCategoriesData['updatedLevelTwoId'] = this.levelMapByIdAndNameOpt[this.formModel[key]];
        reportingCategoriesData['lastUpdatedTime'] = this.getFromOldData(this.getKeyByValue(this.levelMapByIdAndName, key), 'lastUpdatedDtTm');
        this.updateCatpayload['report_categories_data'].push(reportingCategoriesData);
      }
      this.updateCatpayload['report_categories_data'] = this.updateCatpayload['report_categories_data'].filter((rec: any) => {
        if(rec['levelTwoId'] != rec['updatedLevelTwoId']) {
          return rec;
        }
      });
    });
    this.maintenanceEventDetailsService.updateReportingCategories(this.updateCatpayload).subscribe((result) => {
      // result = {'data': result}
      this.processCategories(result);
    });
  }

  getKeyByValue(object: any, value: any) {
    return Object.keys(object).find(key => object[key] === value);
  }

  getFromOldData(id: any, searchField: any, l1Name = "null", l2Name = "null") {
    for(let i = 0; i < this.oldDataformModel.length;i++) {
      if(this.oldDataformModel[i]['level1Id'] == id) {
        if((l1Name == "DOA_Reasons" || l1Name == "Miscellaneous")) {
          if(this.oldDataformModel[i]['level2Name'] == l2Name) {
            return this.oldDataformModel[i][searchField];
          } 
        } else {
          return this.oldDataformModel[i][searchField];
        }
      }
    }
    return "";
  }
}
