.container {
  margin: 0px !important;
  padding: 0px !important;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-icon {
  background: #3F2876 !important;
  padding: 25% 30%;
  transition: transform 0.5s ease-in-out, all 0.5s ease-in-out;
}

.main {
  height: 100%; /* Remaining height after the header */
  display: flex;
  flex-direction: row;
  flex: 1; /* Makes this section take up remaining space between the header and footer */
  // overflow: auto !important;
}

.headerSection {
  height: 18%;
}

.headerSectionDiabled {
  height: 18%;
}

.headerSectionEnabled{
  height: 8%;
}

.mat-drawer {
  border-radius: 0px;
}

.footer {
  height: 35px; /* Adjust based on your footer's content */
  background-color: #ccc; /* Example styling for visibility */
  text-align: center;  
}

.sidenav-container {
  height: 100%;
  display: flex;
  flex: 1;
}

.sidenav {
  width: 23%; /* Default width when expanded */
  max-width: 320px;
  display: flex;
  flex-direction: column;
  background:  #e0e0e0;
  color: white;
  transition: width 0.5s ease-in-out; /* Only width transitions */
  // transition: width 0.5s ease-in-out, transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  overflow: hidden;
  position: relative;
  height: 100%;
}

.sidenav.collapsed {
  width: 70px; /* Fixed width when collapsed, matching icon-section */
  transition: width 0.5s ease-in-out; /* Smooth width transition */
}

.sidenav:not(.collapsed) {
  transition: width 0.5s ease-in-out, transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.sidenav-inner {
  display: flex;
  flex-direction: row; /* Ensure horizontal layout */
  height: 80%;
  transition: none; /* Remove unnecessary transitions here */
}

.app-section-nav-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-sidenav-inner, .app-sidenav-inner {
  display: flex;
  height: fit-content;
  transition: all 0.5s ease-in-out;
}

.menuIcon {
  position: relative;
  color: #ffffff !important;
}

/* Selected state for mat-list-item */
.selectedIcon .menuIcon {
  color: #ff6600 !important; /* Orange color for selected icon */
}

.temporaryDisabled .menuIcon {
  color: #ffffff !important; /* Keep disabled icons white */
  opacity: 0.8;
}

/* Optional: Hover effect for non-selected, non-disabled items */
.mat-list-item:not(.temporaryDisabled):not(.selected):hover .menuIcon {
  color: #ff6200 !important; /* Slightly darker orange on hover for non-selected items */
}

.app-menu-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.icon-section {
  width: 70px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
  align-items: center;
  background: #3F2876; /* Optional background color for the icon section */
  flex-shrink: 0; /* Prevent shrinking */
  transition: none; /* No transitions for icon-section */

}
.user-section {
  width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #7f7b7d;
}

.username-section {
  width: 95%;
  height: 80%;
  margin: 0 5px;
  padding: 0 5px;
  display: flex;
  align-self: center;
  align-items: center;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}

.user-icon-section, .app-icon-section {
  border-radius: 10px !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px !important;
}

.app-icon-section:hover {
  background-color:  #6c49b9 !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 20px;
  height: 90vh;
  cursor: pointer;
  transition: transform 0.5s ease-in-out;
}

.userName {
  font-size: 15px;
  line-height: 1.2;
  font-weight: bold;
  text-align: center;
  color: white;
  margin: 0 5px;
  padding: 4px 8px;
}

.menu-text-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
  // transition: all 0.5s ease-in-out, opacity 0.5s ease 0.5s;
  transition: width 0.5s ease-in-out, opacity 0.5s ease-in-out; /* Smooth collapse */
  width: auto; /* Default width when open */
}

.menu-text-section mat-list-item {
  width: 95%;
  place-self: center;
  margin-bottom: 10px !important;
}

.username-text-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #3F2876;
  overflow: hidden;
  transition: all 0.5s ease-in-out, opacity 0.5s ease 0.5s;
}

.sidenav.collapsed .menu-text-section {
  width: 0; /* Collapse completely */
  opacity: 0; /* Fade out */
  transition: width 0.5s ease-in-out, opacity 0.5s ease-in-out;
  // overflow: visible !important;; /* Prevent clipping */
}

.sidenav.collapsed .sidenav-inner {
  height: 82% !important;
  // transition: all 0.5s ease-in-out; /* Ensure smooth transition */
}

.sidenav.collapsed .menu-text {
  transition: all 0.5s ease-in-out;
}

.mdc-list-item__primary-text {
  color: black !important;
}

.test {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 35%;
  background-color: #D2C6E1;
  border-radius: 3px;
  box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}

// .test:hover {
//   transform: scale(1.05);
// }

.chevron_icon {
  display: flex;
  position: relative;
  right: 4px;
  color: #ffffff !important;
  transition: transform 0.5s ease-in-out;
}
/* Hover effect for list items */
.mat-list-item:hover {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
}

.sidenav.collapsed .chevron_icon {
  transform: rotate(180deg);
}

.sidenav, .mat-list-item, .toggle-icon {
  transition: all 0.5s ease-in-out; /* Smoother interaction */
}

.event-content {
  padding: 5px 5px;
  flex: 1;
  justify-content: flex-start;
  height: 100%;
  position: relative !important;
  // overflow: scroll;
  // overflow-x: hidden;
  overflow: hidden;
}

.disableOverflow {
  overflow: hidden !important;
}

// .toggle-icon:hover {
//   transform: scale(1.05);
// }


.toggle-icon mat-icon {
  color: rgb(255, 255, 255);
  font-size: 28px;
  transition: transform 0.5s ease-in-out;
}

.mat-list-item {
  color: black;
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: center;
  height: 50px !important;
  transition: all 0.5s ease, background-color 0.8s ease, opacity 0.5s ease-in-out;
  opacity: 1 !important;

  mat-icon {
    font-size: 24px;
  }

  .menu-text {
    margin-left: 10px;
    font-size: 14px; /* Adjust font size for better alignment */
    line-height: 50px; 
    white-space: nowrap;
    transition: all 0.5s ease;
  }
}

.mat-list-item.hidden {
  opacity: 1 !important;
  transition: all 0.5s ease;
  pointer-events: none; /* Prevent interaction */
}

.menu-text {
  color: black;
}

.sidenav-content {
  flex: 1;
  margin-left: 0 !important;
  position: relative !important;
  transition: margin-left 0.5s ease-in-out;
}

.sidenav-content.full-width {
  margin-left: 0;
  position: relative !important;
}

.content {
  display: flex; /* Enables flexbox */
  align-items: center; /* Aligns items vertically */
  height: 97.5%; /* Ensure full height */
}


/* Expansion panel base styles */
.expansion-panel {
  width: 95%;
  border: 0;
  border-radius: 0;
  margin: 10px 0;
  place-self: center;
  background-color: white !important;
  color: black !important;
  transition: background-color 0.3s ease-in-out;
}

/* Hover effect */
.expansion-panel:hover,
.expansion-panel-header:hover {
  background: rgba(0, 0, 0, 0.1);
  background-color: #B7BBE3 !important;
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;

  .expansion-panel-title {
    font-weight: 500 !important;
    color: black !important;
  }
}

.mat-expansion-panel.mat-expanded {
  width: 95%;
  justify-self: center;
  background-color:#B7BBE3  !important;
  border-radius: 15px;
  transition: all 0.3s ease-in-out;

  .expansion-panel-header {
    font-size: 16px;
    gap: 10px !important;
    background-color: #B7BBE3 !important;
    color: black !important;
  }

  .expansion-panel-title {
    color: black !important;
    font-weight: 500 !important;
  }

}

.expansion-panel-header {
  font-size: 16px !important;
  gap: 10px !important;
  padding: 10px !important;
}

.expansion-panel-title {
  color: black;
}

// .expansion-panel .mat-expansion-panel-content {
//   max-height: calc(20vh - 48px); /* Adjusts height, accounting for the header */
//   overflow-y: auto; /* Enables scrolling inside the panel */
// }

/* Fixed "Add ACN" Button */
.fixed-add-button {
  position: sticky;
  top: 0;
  align-items: center;
  // padding: 10px 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}

/* Fixed Button */
.acn-header {
  display: flex;
  justify-content: flex-end;
  // padding: 10px;
  background-color:  #de9c9c;
}

/* Scrollable ACN List */
.acn-list {
  max-height: 17rem;
  overflow-y: auto;
  overflow-x: hidden;
  // padding-right: 5px;
  scrollbar-width: thin;
  scrollbar-color: #de9c9c transparent;
}

/* Customize scrollbar for WebKit browsers */
.acn-list::-webkit-scrollbar {
  width: 6px;
}

.acn-list::-webkit-scrollbar-thumb {
  background-color: #de9c9c;
  border-radius: 3px;
}

.acn-list::-webkit-scrollbar-track {
  background: transparent;
}

.small-fab {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white;
  width: 50%;
  height: 25px;
  font-size: 12px;
  border-radius: 10px;
  display: flex;
  float: right;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  border: none;
  outline: none;
  position: relative;
  /* margin-left: 10px; */
  margin-bottom: 5px;
  margin-right: 10px;
}

.small-fab:hover {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: scale(1.1);
}

.small-fab:active {
  background: #de9c9c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: scale(0.95);
}

.small-fab mat-icon {
  font-size: 20px; /* Adjusted for perfect center alignment */
  display: flex;
  align-items: center;
  justify-content: center;
}

.acn-list {
  padding: 0;
  // margin-top: 10px;
}

.menu-section-nav-list {
  padding: 17.5px 0 !important;
}

mat-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  opacity: 1 !important;
  transition: background 0.3s ease;
}

mat-list-item:hover {
  background-color:#B7BBE3;
  border-radius: 15px;
  cursor: pointer;

  .menu-text {
    color: black !important;
    font-weight: 500 !important;
  }
}

.selected {
  background: linear-gradient(135deg, #cc5200, #ff6600) !important;
  color: white !important;
  font-weight: bold;
}

.delete-button {
  position: absolute;
  padding: 0;
  right: 5%;
  top: 5%;
  color: red;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.2s ease-in-out;
  font-size: 16px;
  width: 20%;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

mat-list-item:hover .delete-button {
  opacity: 1 !important;
}

.delete-button:hover {
  color: darkred;
  transform: scale(1.1);
}

/* Ensure open behavior works */
.openBehaviour {
  width: auto; /* Full width when open */
  opacity: 1 !important; /* Visible when open */
}

.mat-acn-list-item {
  width: 75% !important;
  margin: 5px 0;
  height: auto !important;
  position: relative;
  display: flex;
  justify-self: center;
  padding: 2px 0;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
}

.mat-acn-list-item:hover {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  transform: scale(1.05);
}

.acn-name {
  position: relative;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  place-content: center;
}

.selected-tab {
  background-color: #B7BBE3 !important;
  border-radius: 15px !important;
  font-weight: bold;
  transition: background-color 0.3s ease-in-out;

  .menu-text {
    font-weight: 500 !important;
    color: black !important;
  }

  .expansion-panel,
  .expansion-panel-header {
    background: rgba(0, 0, 0, 0.1);
    background-color: #B7BBE3 !important;
    border-radius: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    .expansion-panel-title {
      font-weight: 500 !important;
      color: black !important;
    }
  }
}
.temporaryDisabled {
  opacity: 0.8 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}
.temporaryMatListDisabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}
.not-allowed-cursor {
  cursor: not-allowed !important;
  pointer-events: none !important;
}
.display-center {
  font-weight: 500;
  display: flex;
  place-self: center;
}

::ng-deep .mat-expansion-panel-animations-enabled .mat-expansion-indicator {
  position: relative;
  right: 5px;
}

::ng-deep .expansion-panel.mat-expanded {
  background-color: rgb(200, 150, 150) !important; /* Change background when opened */
  border-radius: 0 0 20px 20px;
  transition: all 0.3s ease-in-out; /* Smooth transition */
}

::ng-deep .mat-expansion-panel-header:not([aria-disabled=true]) {
  background-color: rgb(175, 146, 146);
}

::ng-deep .mat-expansion-panel:not([class*=mat-elevation-z]) {
  box-shadow: none !important;
  background-color: white !important;
  background: white !important;
  color: white !important;
  // fill: white !important;
}

::ng-deep .mat-expansion-indicator svg {
  fill: white !important;
  display: var(--mat-expansion-header-indicator-display, inline-block) !important;
}

::ng-deep .expansion-panel-header.mat-expansion-panel-header {
  background-color: white !important;
}

::ng-deep .expansion-panel-header.mat-expansion-panel-header:hover {
  background-color:#FF6200;
}

::ng-deep .mat-expansion-panel-body {
  padding: 0px 25px 5px !important;
}

::ng-deep .custom-notification-popover.popover {
  width: 25vw !important;
  max-width: 25vw !important;
  height: 55vh !important;
}

// ::ng-deep .mat-expansion-panel.mat-expanded > .mat-expansion-panel-content-wrapper {
//   max-height: 20rem !important;
//   overflow: auto !important;
// }


/* Expansion panel header - centered by default */
::ng-deep .mat-expansion-panel-header {
  padding: 10px !important;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center; /* Center content by default */
  transition: justify-content 0.3s ease-in-out; /* Smooth layout shift */
}

/* Title styling - compact and centered */
::ng-deep .mat-expansion-panel-header .mat-expansion-panel-header-title {
  // color: black !important;
  display: flex;
  font-weight: 500;
  justify-content: center;
  width: fit-content; /* Only as wide as the text */
  text-align: center; /* Center the text */
  margin: 0 auto; /* Center within the flex container */
  transition: margin 0.3s ease-in-out, text-align 0.3s ease-in-out; /* Smooth shift */
}

/* Hide the expansion indicator by default */
::ng-deep .mat-expansion-indicator {
  opacity: 0; /* Hidden by default */
  transition: opacity 0.3s ease-in-out;
}

/* On hover, shift title left and show indicator */
::ng-deep .expansion-panel-header:hover {
  justify-content: space-between; /* Spread content on hover */
}

::ng-deep .expansion-panel-header:hover .mat-expansion-panel-header-title {
  margin: 0; /* Remove centering */
  text-align: left; /* Shift text to left */
  margin-left: 0; /* Align left */
}

::ng-deep .expansion-panel-header:hover .mat-expansion-indicator {
  opacity: 1; /* Show indicator on hover */
}

/* Style the indicator */
::ng-deep .mat-expansion-indicator svg {
  fill: white !important;
  width: 20px;
  height: 20px;
}

/* Expanded state */
::ng-deep .mat-expansion-panel.mat-expanded .expansion-panel-header {
  justify-content: space-between; /* Keep layout when expanded */
}

::ng-deep .mat-expansion-panel.mat-expanded .mat-expansion-panel-header-title {
  margin: 0;
  text-align: left; /* Keep left-aligned when expanded */
}

.has-unread {
  color: #ff6600;
}

.has-read {
  color: white;
}

.custom-notification-popover {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: black;
  max-width: 420px;
  max-height: 60vh;
  overflow: hidden;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  z-index: 1050;

  &.bs-popover-top .popover-arrow::before,
  &.bs-popover-top .popover-arrow::after {
    border-top-color: #3F2876;
  }
  &.bs-popover-bottom .popover-arrow::before,
  &.bs-popover-bottom .popover-arrow::after {
    border-bottom-color: #3F2876;
  }
  &.bs-popover-end .popover-arrow::before,
  &.bs-popover-end .popover-arrow::after {
    border-right-color: #3F2876;
  }
  &.bs-popover-start .popover-arrow::before,
  &.bs-popover-start .popover-arrow::after {
    border-left-color: #3F2876;
  }
}

.notification-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 50vh;
}

.notification-header {
  font-size: 1rem;
  font-weight: bold;
  padding: 8px 5px;
  text-align: center;
  border-radius: 10px;
  color: #ffffff;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  position: sticky;
  top: 0;
  z-index: 2;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-list {
  overflow-y: auto;
  padding: 10px 5px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(63, 40, 118, 0.5) transparent;
}

.notification-item {
  background: linear-gradient(135deg, #d9ccf2, #e6ddfa); // lighter version of your theme
  border-left: 4px solid #ff6600;
  border-radius: 6px;
  padding: 0.75rem;
  color: #000000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  transition: background-color 0.25s ease, transform 0.2s;

  &:hover {
    background: linear-gradient(135deg, #cbb7ff, #dacbfa);
    transform: translateY(-2px);
    cursor: pointer;
  }
}

.notification-body {
  font-size: 14px;
  line-height: 1.5;
  color: #000000;
  white-space: normal;
  word-break: break-word;
}

.notification-timestamp {
  font-size: 12px;
  color: #ff6600;
  margin-top: 0.4rem;
  text-align: right;
}

.font-weight-bold {
  font-weight: bold !important;
}

.notification-list::-webkit-scrollbar {
  width: 8px; /* wider for better visibility */
}

.notification-list::-webkit-scrollbar-thumb {
  background-color: rgba(63, 40, 118, 0.5); /* purple with opacity */
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.notification-list::-webkit-scrollbar-track {
  background: transparent;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background: linear-gradient(135deg, #f9f9f9, #eaeaea);
  border: 2px dashed #ccc;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #555;
  text-align: center;
  margin: 20px 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  transition: all 0.3s ease;
  max-width: 100%;
}

.no-data-message:hover {
  background: linear-gradient(135deg, #ededed, #dcdcdc);
  border-color: #999;
  color: #333;
  transform: scale(1.01);
}

.no-data-message {
  animation: fadeIn 0.4s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

::ng-deep .badge-icon-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

::ng-deep .badge-icon-wrapper .mat-badge-content {
  position: absolute;
  top: 10px !important;
  right: 15px !important;
  z-index: 1000;
  font-size: 0.7rem;
  font-weight: 600;
  width: 18px !important;
  height: 18px !important;
  line-height: 18px;
  padding: 0 4px;
  background-color: red !important;
  color: white;
  border-radius: 50%;
  box-shadow: 0 0 0 2px #2b2b2b;
  display: flex;
  align-items: center;
  justify-content: center;
}



.notification-section-title {
  font-size: 12px;
  font-weight: 700;
  color: #3F2876;
  padding: 5px 20px;
  text-transform: uppercase;
  border-bottom: 1px solid #ccc;
  background-color: #f4f4f8;
  /* margin-bottom: 0.5rem; */
  border-radius: 5px;
}

.notification-item.read {
  opacity: 0.8;
  background: linear-gradient(135deg, #f2f2f2, #dddddd);
  border-left: 4px solid #ccc;

  &:hover {
    background: linear-gradient(135deg, #eaeaea, #d5d5d5);
    cursor: default;
  }
}

/* Ensure tooltip works correctly in both collapsed and expanded sidenav states */
::ng-deep .mat-tooltip.tooltip {
  background: #333 !important;
  color: #fff !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important; /* Ensure tooltip is above other elements */
  margin-left: 10px !important; /* Space from icon */
  transition: opacity 0.3s ease, transform 0.3s ease !important;
  transform: translateY(0) !important;
}

/* Arrow for tooltip */
::ng-deep .mat-tooltip.tooltip::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: #333;
}

/* Adjust tooltip position when sidenav is collapsed */
::ng-deep .mat-sidenav.collapsed ~ .mat-tooltip.tooltip {
  margin-left: 15px !important; /* Extra margin to account for collapsed sidenav */
}

/* Smooth hover effect for the icon */
.app-icon-section .menuIcon {
  transition: color 0.3s ease;
}

.app-icon-section .menuIcon:hover {
  color: #1976d2; /* Material Design blue for hover effect */
}

/* Ensure tooltip stays visible longer */
::ng-deep .mat-tooltip.tooltip {
  opacity: 0;
  transform: translateY(5px) !important;
}

::ng-deep .mat-tooltip.tooltip.show {
  opacity: 1;
  transform: translateY(0) !important;
}

/* Increase tooltip hide delay */
::ng-deep .mat-tooltip {
  transition-delay: 0.1s !important; /* Slight delay to prevent premature hiding */
}