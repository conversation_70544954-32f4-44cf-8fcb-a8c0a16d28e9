import { Component } from '@angular/core';

@Component({
  selector: 'app-custom-maintenance-event-list-etic',
  templateUrl: './custom-maintenance-event-list-etic.component.html',
  styleUrl: './custom-maintenance-event-list-etic.component.scss',
  standalone: false
})
export class CustomMaintenanceEventListEticComponent {

  params: any;
  eticDate: any;

  agInit(params: any): void {
    this.params = params;
    this.eticDate = `${params.data.eticDateTime}${params.data.eticText ? params.data.eticText : ''}`;
  }

  refresh(params: any): boolean {
    this.params = params;
    return true;
  }

}
