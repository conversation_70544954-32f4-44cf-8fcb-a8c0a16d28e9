import { answers } from "./answersDto";

export class questions{
    questionId: number;
    questionTxt: string;
    questionGrp:string;
    required: boolean ;
    constructor(data: Partial<questions> = {}) {
        this.required = data.required || false;
        this.questionId = data.questionId || 0;
        this.questionTxt = data.questionTxt || '';
        this.questionGrp = data.questionGrp || '';
    }
    answers: Array<answers> = [];
}