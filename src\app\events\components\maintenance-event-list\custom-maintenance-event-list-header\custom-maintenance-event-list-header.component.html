<div class="ag-cell-label-container" role="presentation">
  <div class="ag-header-cell-label" role="presentation">

    <!-- Filter Icon (Hidden for settings column) -->
    <span 
      #filterIcon
      *ngIf="params.column.getColId() !== 'settings'"
      class="ag-filter-icon ag-icon ag-icon-filter" 
      [ngClass]="{ 'filter-active': isFilterActive, 'filter-hover': !isFilterActive }" 
      (click)="onFilterRequested($event)">
    </span>

    <!-- Header Text & Sort Icon (Hidden for settings column) -->
    <span class="ag-header-cell-text" (click)="onSortRequested($event)">
      {{ displayHeaderName }}
      <span *ngIf="sortDirection && params.column.getColId() !== 'settings'" class="ag-sort-icon">
        <span *ngIf="sortDirection === 'asc'" class="ag-icon ag-icon-asc"></span>
        <span *ngIf="sortDirection === 'desc'" class="ag-icon ag-icon-desc"></span>
      </span>
    </span>

  </div>
</div>