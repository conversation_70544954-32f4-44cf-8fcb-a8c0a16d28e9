import { Component, Input } from '@angular/core';
import { ICellEditorAngularComp, ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellEditorParams, ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'app-custom-link-downingitem',
  standalone: false,
  templateUrl: './custom-link-downingitem.component.html',
  styleUrl: './custom-link-downingitem.component.scss'
})
export class CustomLinkDowningitemComponent implements ICellRendererAngularComp, ICellEditorAngularComp {

  @Input() value: boolean = false;
  @Input() isDisabled: boolean = false;
  @Input() checkboxColor: string = '#967878';

  private params!: ICellRendererParams | ICellEditorParams;

  agInit(params: ICellRendererParams | ICellEditorParams): void {
    this.params = params;
    this.value = params.value || false;
    this.checkboxColor = (params.colDef?.cellEditorParams?.color || '#967878') as string;

    if (params.colDef?.field === 'downingItem') {
      this.isDisabled = !params.data?.link;
    }
    if (params.colDef?.field === 'copyToManagerNotes') {
      this.isDisabled = !params.data?.link;
    }
    if (params.colDef?.field === 'copyToComment') {
      this.isDisabled = !params.data?.link;
    }
  }

  refresh(params: ICellRendererParams | ICellEditorParams): boolean {
    this.agInit(params);
    return true;
  }

  onChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    const field = this.params.colDef?.field;
    this.value = checked;

    if (field === 'link') {
      if (!checked) {
        this.params.data.downingItem = this.params.data.copyToManagerNotes = this.params.data.copyToComment = false;
      }
      this.params.node.setDataValue(field, checked);

      // Refresh the same row to update downingItem disabled state
      // this.params.api.refreshCells({ rowNodes: [this.params.node], columns: ['downingItem'], force: true });
      this.params.api.refreshCells({ rowNodes: [this.params.node], columns: ['downingItem', 'copyToManagerNotes', 'copyToComment'] });
    }

    if (field === 'downingItem' && this.params.data.link) {
      this.params.node.setDataValue(field, checked);
    }

    if (field === 'copyToManagerNotes' && this.params.data.link) {
      this.params.node.setDataValue(field, checked);
    }

    if (field === 'copyToComment' && this.params.data.link) {
      this.params.node.setDataValue(field, checked);
    }

    // Dispatch to parent
    // if (this.params.api && this.params.column && this.params.node) {
    //   this.params.api.stopEditing();
    //   const event = {
    //     type: 'cellValueChanged',
    //     column: this.params.column,
    //     colDef: this.params.colDef,
    //     data: this.params.node.data,
    //     newValue: checked,
    //     oldValue: !checked,
    //     node: this.params.node
    //   };
    //   this.params.api.dispatchEvent(event);
    // }
  }

  getValue(): any {
    return this.value;
  }

  afterGuiAttached?(): void {
    const input = document.querySelector('.custom-checkbox') as HTMLInputElement;
    input?.focus();
  }

}
