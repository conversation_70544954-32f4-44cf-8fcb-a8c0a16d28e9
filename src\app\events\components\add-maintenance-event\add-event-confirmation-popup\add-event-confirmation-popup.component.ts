import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-add-event-confirmation-popup',
  standalone: false,
  templateUrl: './add-event-confirmation-popup.component.html',
  styleUrl: './add-event-confirmation-popup.component.scss'
})
export class AddEventConfirmationPopupComponent implements OnInit {

    ELEMENT_DATA: any = [];
  isEventCreated: boolean = false;
  creationResponse: string = "Unexpected Error Occurred!";
  acnValue: string = '';
  returnBack: boolean = false;
  showOptions: boolean = false;
  yesOrNo: boolean = false;
  displayedColumns: string[] = ['type', 'station', 'start', 'status', 'comment'];
  dataSource = this.ELEMENT_DATA;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<AddEventConfirmationPopupComponent>,
    private router: Router
  ) {
    data.message.event === 'NA' ? this.dialogRef.disableClose = true : this.dialogRef.disableClose = false;
    this.dialogRef.backdropClick().subscribe(() => {
      data.message.event != 'NA' ? this.dialogRef.close("CANCEL") : null;    
    });
  }

  ngOnInit(): void {
    if (this.data.event != 'NA') {
      if (this.data.message['ERROR'].endsWith("return.") || this.data.message['ERROR'].endsWith("return")) {
        this.returnBack = true;
        if (this.data.message['ACTION_REQUIRED_EVENTS'] != undefined) {
          for (let i of this.data.message['ACTION_REQUIRED_EVENTS']) {
            this.ELEMENT_DATA.push({
              'type': i['type'],
              'station': i['station'],
              'start': i['startDateTime'],
              'status': i['status'],
              'comment': i['curComment']
            });
          }
        }
        if (this.data.message['UNREVIEWED_EVENTS'] != undefined) {
          for (let i of this.data.message['UNREVIEWED_EVENTS']) {
            this.ELEMENT_DATA.push({
              'type': i['type'],
              'station': i['station'],
              'start': i['startDateTime'],
              'status': i['status'],
              'comment': i['curComment']
            });
          }
        }
      } else if (this.data.message['ERROR'].endsWith("?")) {
        this.yesOrNo = true;
        for (let i of this.data.message['OVERRIDABLE_EVENTS']) {
          this.ELEMENT_DATA.push({
            'type': i['type'],
            'station': i['station'],
            'start': i['startDateTime'],
            'status': i['status'],
            'comment': i['curComment']
          });
        }
      } else {
        for (let i of this.data.message['CONVERTABLE_EVENTS']) {
          this.ELEMENT_DATA.push({
            'type': i['type'],
            'station': i['station'],
            'start': i['startDateTime'],
            'status': i['status'],
            'comment': i['curComment']
          });
        }
        this.showOptions = true;
      }
      this.dataSource = this.ELEMENT_DATA;
    } else {
      if (this.data.message.hasOwnProperty('SUCCESS')) {
        this.isEventCreated = true;
        this.acnValue = this.data.message.hasOwnProperty("ADDED_EVENT_DATA")
          ? this.data.message['ADDED_EVENT_DATA']['ACN']
          : '';
        this.creationResponse = "Successfully created event for ACN";
      } else if (this.data.message.hasOwnProperty('ERROR')) {
        this.isEventCreated = false;
        this.creationResponse = this.data.message['ERROR'];
      }
      this.returnBack = true;
    }
  }

  handleButton(value: any) {
    if (value === 'DETAILS') {
      this.dialogRef.close("");
      this.router.navigate(['/maintenance-event-details'], { queryParams: { acn: this.acnValue } });
    } else if (value === 'LIST') {
      this.dialogRef.close("");
      this.router.navigate(['/maintenance-event-list']);
    } else if (value === 'ADD_ANOTHER') {
      this.dialogRef.close("ADD_NEW_EVENT");
    } else {
      this.closeDialog(value);
    }
  }

  closeDialog(data: any) {
    this.dialogRef.close(data);
  }

}
