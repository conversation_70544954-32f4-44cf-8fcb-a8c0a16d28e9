export class SessionStorageKeys {
    static readonly OKTA_TOKEN_STORAGE = 'okta-token-storage'
    static readonly LOGGED_USER_DETAILS = 'logged-user-details';
    static readonly SELECTED_EVENTS = 'selectedEvents';
    static readonly SELECTED_FLEETS = 'selectedFleets';
    static readonly SELECTED_REGIONS = 'selectedRegions';
    static readonly SELECTED_STATIONS = 'selectedStations';
    static readonly POWER_PLANT_CHECKED = 'powerPlantChecked';
    static readonly SIDENAV_CLOSED = 'sideNavClosed';
    static readonly MENU_OPTIONS_CLOSED = 'isOptionsClosed';
    static readonly SIDENAV_MENULIST_CLOSED = 'sideNav&ListMenuClosedStatus';
    static readonly ACN_DETAILS_TAB_DATA = 'acnDetailsTabData'
    static readonly EVENT_LIST_COLUMNS = 'event-list-columns-options';
    static readonly EVENT_LIST_SETTINGS = 'event-list-columns-settings-options';
    static readonly DISCREPANCY_TABLE_FILTER = 'DetailsDiscrepanciesTableFilter';
    static readonly USER_ADDED_EVENT_LIST_ACN = 'userAddedEventListAcns';
    static readonly MSN_TABLECOLUMNS = 'msnTableColumns';
    static readonly MSN_SHIPPING_TABLECOLUMNS = 'msnShippingTableColumns';
    static readonly MSN_COMMENTS_TABLECOLUMNS = 'msnCommentsTableColumns';
}

export interface SelectedValues {
    selectedEvents: any[];
    selectedFleets: any[];
    selectedRegion: string | null;
    selectedStation: string | null;
    powerPlantChecked: boolean;
}