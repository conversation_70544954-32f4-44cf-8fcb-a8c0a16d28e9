<h2 mat-dialog-title class="dialog-title">
    Discrepancies Text ( ATA:&nbsp;&nbsp;<span class="highlight-text">{{ rowData.ata }}</span>,&nbsp;Type:&nbsp;&nbsp;<span class="highlight-text">{{ rowData.discType }}</span>,&nbsp;WRI:&nbsp;&nbsp;<span class="highlight-text">{{ rowData.number }}</span> )
</h2>
  
<mat-dialog-content class="dialog-content">
  <div class="detail-box preserve-whitespace">
    {{ discrepancyDetails?.join('\n') }}
  </div>
</mat-dialog-content>
  
<mat-dialog-actions class="dialog-actions">
      <button mat-raised-button class="action-button close" (click)="close()" cdkFocusInitial>Close</button>
</mat-dialog-actions>
  