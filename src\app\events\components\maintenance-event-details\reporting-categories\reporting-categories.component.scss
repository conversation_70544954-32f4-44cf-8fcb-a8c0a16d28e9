.niw-timer-heading {
    background: #7f7b7d;
    padding: 1% 1%;
    border-radius: 7px;
}

.apply1Pading {
    padding: 0.5rem;
}

.outer-div {
    margin-top: 0.5rem;
    display: grid;
    gap: 0.75rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.outer-div > div {
    width: 100%;
    margin-bottom: 0;
}

.category-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}


mat-card {
    height: fit-content;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    margin-bottom: 0;
}

mat-card-header {
    padding-bottom: 0.5rem !important;
}

mat-card-content {
    padding-top: 0 !important;
    padding-bottom: 0.75rem !important;
}

mat-card-title {
    font-size: medium !important;
    font-weight: bold !important;
    color: #7f7b7d !important;
    margin-bottom: 0 !important;
}


.box-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    align-items: start;
}

.box-container mat-radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    align-items: start;
}

.box2-container mat-radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    align-items: start;
}


.applyAdditionalSpacing {
    padding: 0.5rem 0.75rem 0.25rem 0.75rem;
}


.gap1-container {
    gap: 1rem;
}

.gap2-container mat-radio-group {
    gap: 0.5rem;
}

.gap2-container {
    gap: 0.5rem;
}

.gap3-container mat-radio-group {
    gap: 0.75rem;
}

.gap4-container mat-radio-group {
    gap: 0.25rem;
}

.gap1-container mat-radio-group {
    gap: 1rem;
}

.heading-1 {
    margin-bottom: 0px;
    color: white;
    font-weight: bold;
    font-size: medium;
}


mat-radio-button,
mat-checkbox {
    margin: 0.125rem 0;
    word-break: break-word;
    line-height: 1.2;
    display: flex;
    align-items: center;
}

::ng-deep mat-radio-button .mdc-form-field,
::ng-deep mat-checkbox .mdc-form-field {
    align-items: center !important;
    gap: 0.5rem;
}

::ng-deep mat-radio-button .mdc-radio,
::ng-deep mat-checkbox .mdc-checkbox {
    flex-shrink: 0;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

::ng-deep mat-radio-button .mdc-form-field > label,
::ng-deep mat-checkbox .mdc-form-field > label {
    line-height: 1.2;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
}

::ng-deep mat-radio-button .mdc-radio__background,
::ng-deep mat-checkbox .mdc-checkbox {
    vertical-align: middle;
}


::ng-deep .mdc-radio__background .mdc-radio__inner-circle {
    border-color: #6c49b9 !important;
}

::ng-deep .mdc-radio__background .mdc-radio__outer-circle {
    border-color: #7f7b7d !important;
}


::ng-deep .mdc-checkbox__checkmark-path {
    stroke: #ffffff !important;
    stroke-width: 2px !important;
}

::ng-deep .mdc-checkbox__background {
    border-color: #7f7b7d !important;
}

::ng-deep .mdc-checkbox--checked .mdc-checkbox__background {
    background-color: #6c49b9 !important;
    border-color: #6c49b9 !important;
}

::ng-deep .mdc-checkbox__checkmark {
    color: #ffffff !important;
}

::ng-deep .mdc-checkbox--checked .mdc-checkbox__checkmark-path {
    stroke: #ffffff !important;
    stroke-dasharray: 29.7833385 !important;
    stroke-dashoffset: 0 !important;
}

::ng-deep .mdc-checkbox__mixedmark {
    background-color: #ffffff !important;
}


::ng-deep .mdc-checkbox .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
    opacity: 1 !important;
    transform: scaleX(1) scaleY(1) !important;
}

::ng-deep .mdc-checkbox .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background {
    background-color: #6c49b9 !important;
    border-color: #6c49b9 !important;
}

::ng-deep .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path {
    stroke-dashoffset: 0 !important;
}


::ng-deep .mdc-checkbox {
    width: 18px !important;
    height: 18px !important;
}

::ng-deep .mdc-checkbox .mdc-checkbox__background {
    width: 18px !important;
    height: 18px !important;
    border-radius: 2px !important;
}

// ::ng-deep .mdc-checkbox .mdc-checkbox__checkmark {
//     width: 18px !important;
//     height: 18px !important;
// }

::ng-deep .mdc-checkbox .mdc-checkbox__checkmark-path {
    stroke-width: 3.12px !important;
}

/* Hover and focus states */
::ng-deep .mdc-checkbox:hover .mdc-checkbox__background {
    border-color: #6c49b9 !important;
}

::ng-deep .mdc-checkbox .mdc-checkbox__native-control:focus ~ .mdc-checkbox__background {
    border-color: #6c49b9 !important;
}


::ng-deep .mdc-checkbox--disabled .mdc-checkbox__background {
    border-color: #cccccc !important;
    background-color: #f5f5f5 !important;
}


::ng-deep .mdc-checkbox__checkmark {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    opacity: 0;
    transition: opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
}

::ng-deep .mdc-checkbox--checked .mdc-checkbox__checkmark,
::ng-deep .mdc-checkbox--indeterminate .mdc-checkbox__checkmark {
    opacity: 1 !important;
    transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
}


::ng-deep .mdc-checkbox__checkmark svg {
    display: block !important;
}


@media (max-width: 768px) {
    .outer-div {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        margin-top: 0.25rem;
    }

    .box-container,
    .box-container mat-radio-group,
    .box2-container mat-radio-group {
        grid-template-columns: 1fr;
        gap: 0.25rem;
    }

    .applyAdditionalSpacing {
        padding: 0.25rem 0.5rem 0.125rem 0.5rem;
    }

    mat-card-content {
        padding: 0.5rem !important;
    }

    mat-card-header {
        padding-bottom: 0.25rem !important;
    }

    mat-radio-button,
    mat-checkbox {
        margin: 0.125rem 0;
    }
}

@media (max-width: 480px) {
    .apply1Pading {
        padding: 0.25rem;
    }

    .outer-div {
        gap: 0.375rem;
        margin-top: 0.125rem;
        grid-template-columns: 1fr;
    }

    .applyAdditionalSpacing {
        padding: 0.125rem 0.375rem 0.125rem 0.375rem;
    }

    mat-card-title {
        font-size: small !important;
    }

    mat-card-content {
        padding: 0.375rem !important;
    }

    mat-card-header {
        padding-bottom: 0.125rem !important;
    }

    mat-radio-button,
    mat-checkbox {
        margin: 0.0625rem 0;
        font-size: 0.9rem;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .outer-div {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1rem;
    }

    .box-container,
    .box-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .box2-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    }
}

@media (min-width: 1200px) {
    .outer-div {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 1.5rem;
    }

    .box-container,
    .box-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .box2-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (min-width: 1600px) {
    .outer-div {
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
        gap: 2rem;
    }

    .box-container,
    .box-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .box2-container mat-radio-group {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
}
