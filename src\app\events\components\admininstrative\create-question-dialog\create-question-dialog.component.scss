:host {
  display: flex;
  flex-direction: column;
  min-width: 20rem;
  max-width: 90vw;
  min-height: auto;
  max-height: 90vh;
  padding: 0;
  background: linear-gradient(135deg, #ffffff, #f0f2f5);
  border-radius: 15px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
  transition: all 0.3s ease-in-out;
  animation: slideIn 0.4s ease-out forwards;
  overflow: hidden;
}

:host:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

h2[mat-dialog-title] {
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: white;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 2px 8px rgba(63, 40, 118, 0.2);
  font: 700 1.2em/1.2em 'Open Sans', sans-serif;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease-in-out;
  flex-shrink: 0;
}

h2[mat-dialog-title]:hover {
  transform: translateY(-1.5px);
  box-shadow: 0 4px 12px rgba(63, 40, 128, 0.3);
}

mat-dialog-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: auto;
}

mat-form-field {
  width: 100%;
  margin-bottom: 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

mat-form-field:hover {
  box-shadow: 0 3px 10px rgba(108, 73, 185, 0.2);
  transform: translateY(-1px);
}

mat-form-field:focus-within {
  box-shadow: 0 0 0 3px rgba(108, 73, 185, 0.3);
}

mat-form-field textarea,
mat-form-field input {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  color: #3f2876;
}

.radio-group {
  display: flex;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
  padding: 10px;
  background: linear-gradient(135deg, #e6e6fa, #d8bfd8);
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
  margin-bottom: 15px;
  transition: all 0.3s ease-in-out;
  min-width: 100%;
}

.radio-group:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

mat-radio-button {
  color: #6c49b9;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  transition: all 0.3s ease-in-out;
  min-width: 4rem;
}

mat-radio-button:hover {
  color: #3f2876;
  transform: scale(1.02);
}

.required-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  background: linear-gradient(135deg, #e6e6fa, #d8bfd8);
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
  margin: 5px;
  transition: all 0.3s ease-in-out;
}

.required-checkbox-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

mat-checkbox {
  color: #6c49b9;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  transition: all 0.3s ease-in-out;
}

mat-checkbox:hover {
  color: #3f2876;
  transform: scale(1.02);
}

.answer-tiles {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.answer-tile {
  display: inline-flex;
  align-items: center;
  background: #b7bbe3;
  color: #3f2876;
  padding: 8px 15px;
  margin: 5px;
  border-radius: 20px;
  box-shadow: 0 6px 12px -2px rgba(50, 50, 93, 0.25), 0 3px 7px -3px rgba(0, 0, 0, 0.3);
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  max-width: calc(100% - 10px);
  word-break: break-word;
}

.answer-tile:hover {
  background: linear-gradient(135deg, #b7bbe3, #c0c4e7);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px) scale(1.01);
}

.answer-tile::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  z-index: 0;
  animation: bloom 2s infinite ease-in-out;
}

.answer-tile:hover::before {
  width: 200%;
  height: 200%;
}

.question-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #e6e6fa, #d8bfd8);
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.24);
  transition: all 0.3s ease-in-out;
}

.question-input-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.question-input-container mat-form-field {
  flex: 1;
  margin-bottom: 0;
}

.mic-button {
  color: #6c49b9;
  transition: all 0.3s ease-in-out;
  margin-top: 8px;
}

.mic-button:hover {
  color: #3f2876;
  transform: scale(1.1);
}

.mic-button[disabled] {
  color: #b7bbd3;
  cursor: not-allowed;
}

.listening-indicator {
  background: linear-gradient(135deg, #e8eaf6, #d1d4e0);
  color: #3f2876;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 8px;
  margin-top: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

.listening-indicator:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.error-message {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #d32f2f;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 8px;
  margin-top: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

.error-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

mat-dialog-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 10px;
  background: linear-gradient(135deg, #ffffff, #f0f2f5);
  border-radius: 0 0 10px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;
  flex-shrink: 0;
  transition: all 0.3s ease-in-out;
}

mat-dialog-actions:hover {
  background: linear-gradient(135deg, #f0f2f5, #e0e4e8);
  transform: translateY(-1px);
}

mat-dialog-actions button {
  height: 35px;
  padding: 8px 16px;
  border-radius: 8px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  min-width: 90px;
  border: none;
  position: relative;
  overflow: hidden;
}

mat-dialog-actions button[color="primary"] {
  background-color: #6c49b9;
  color: white;
}

mat-dialog-actions button[color="primary"]:disabled {
  background-color: #b7bbd3;
  color: #9a8cc7;
  cursor: not-allowed;
}

mat-dialog-actions button[color="primary"]:hover:not(:disabled) {
  background-color: #5a3a99;
  box-shadow: 0 3px 10px rgba(108, 73, 185, 0.3);
}

mat-dialog-actions button[color="warn"] {
  background-color: #ff6600;
  color: white;
}

mat-dialog-actions button[color="warn"]:hover {
  background-color: #e65c00;
  box-shadow: 0 3px 10px rgba(255, 102, 0, 0.3);
}

mat-dialog-actions button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: 0;
}

mat-dialog-actions button:hover::before {
  width: 300%;
  height: 300%;
}

p {
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  color: #3f2876;
  margin: 10px 0;
  transition: all 0.3s ease-in-out;
}

p:hover {
  color: #6c49b9;
  text-shadow: 0 0 4px rgba(108, 73, 185, 0.2);
}

@media screen and (max-width: 600px) {
  :host {
    min-width: 18rem;
    max-width: 95vw;
  }

  mat-radio-button {
    font-size: 12px;
    min-width: 3.5rem;
  }

  .radio-group {
    gap: 8px;
    padding: 8px;
  }

  .answer-tile {
    font-size: 13px;
    padding: 6px 12px;
  }

  mat-form-field textarea,
  mat-form-field input {
    font-size: 13px;
  }
}

@media screen and (max-width: 400px) {
  :host {
    min-width: 16rem;
  }

  mat-radio-button {
    font-size: 11px;
    min-width: 3rem;
  }

  .radio-group {
    gap: 6px;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bloom {
  0% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.8);
  }
}