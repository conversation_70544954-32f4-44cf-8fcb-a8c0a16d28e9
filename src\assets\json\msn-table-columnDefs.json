[{"headerName": "MSN", "field": "msn", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Status Code", "field": "statusCode", "tooltipField": "statusCode", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Follow Up", "field": "followUpCode", "tooltipField": "followUpCode", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "MPN", "field": "manufPartNbr", "tooltipField": "manufPartNbr", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "CPN", "field": "coPartNbr", "tooltipField": "coPartNbr", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "CPN Qty", "field": "cpnQty", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "CPN Description", "field": "cpnDescription", "tooltipField": "cpnDescription", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Ship To Station", "field": "shiptoSta", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Ship To Department", "field": "shiptoDept", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Plan Method Indicator", "field": "planMetInd", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Date/Time Part Need By", "field": "datetimePartNeedBy", "tooltipField": "datetimePartNeedBy", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}, {"headerName": "Requested By", "field": "requestedBy", "tooltipField": "requestedBy", "filter": true, "sortable": true, "resizable": true, "autoHeight": true, "cellStyle": {"white-space": "normal", "word-wrap": "break-word", "overflow": "visible"}}]