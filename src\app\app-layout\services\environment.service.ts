import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpHeaders } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {

  private readonly msnTableHeadersUrl = 'assets/json/msn-table-columnDefs.json';
  private readonly maintenanceEventListTableHeadersUrl = 'assets/json/maintenance-event-List-columnDefs.json';
  private readonly maintenanceEventDetailsDiscrepanciesTableColumnDefsURL = 'assets/json/maintenance-event-details-discrepancies-columnDefs.json';
  private readonly fleetsJson = 'assets/json/fleet-vlaues.json';
  private readonly eventJson = 'assets/json/event-values.json';

  constructor() { }

  get getEventJson(): any {
    return `${this.eventJson}`;
  }

  get getFleetsJson(): any {
    return `${this.fleetsJson}`;
  }

  get getMsnTableHeaders(): any {
    return `${this.msnTableHeadersUrl}`;
  }

  get getMaintenanceEventListTableHeaders(): any {
    return `${this.maintenanceEventListTableHeadersUrl}`;
  }

  get getMaintenanceEventDetailsDiscrepanciesTableHeaders(): any {
    return `${this.maintenanceEventDetailsDiscrepanciesTableColumnDefsURL}`;
  }

  get getUserPreferences(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.preferences.preferencesEndpoint}${environment.api.endpoints.preferences.getUserPreferences}`;
  }

  get saveUserPreferences(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.preferences.preferencesEndpoint}${environment.api.endpoints.preferences.saveUserPreferences}`;
  }

  get addEvent(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.addEvent}`;
  }

  get validateAcnData(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.addEvent}`;
  }

  get getFlightDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.flightLegDetails}`;
  }

  get getManagerDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.managersList}`;
  }

  get getMsnTableData(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.msnDetails}`;
  }

  get getMsnshippingInfo(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.msnShippingInfo}`;
  }

  get getMsnshippingDetail(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.msnShippingDetail}`;
  }

  get getReportingCategories(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.reportingCategories}`;
  }

  get updateReportingCategories(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.updateEndpoint}${environment.api.endpoints.update.reportingCategories}`;
  }

  get getNiwTimers(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.niwtimers}`;
  }

  get getAllNiwTimers(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.allniwtimers}`;
  }

  get updateNiwTimers(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.updateEndpoint}${environment.api.endpoints.update.niwTimersUpdate}`;
  }

  get getMaintenanceEventList(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.eventListView}`;
  }

  get getRegionsList(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.regions}`;
  }

  get getStationsListFromRegion(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.station}`;
  }

  get getDetailViewData(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.detailView}`;
  }

  get getFlightChecks(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.flightChecks}`;
  }

  get getDetailHeaderDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}`;
  }

  get getDiscrepancyTableLData(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.openDiscrepancies}`;
  }

  get getDiscrepancyDetailInfo(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.discrepancyDetailView}`;
  }

  get getEventFlightEticDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.flightEticInfo}`;
  }

  get updateMainDetailScreenEventDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.updateEndpoint}${environment.api.endpoints.update.event_detail}`;
  }

  get updateDiscrepancies(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.updateEndpoint}${environment.api.endpoints.update.discrepancyUpdate}`;
  }

  get getTubFileNotes(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.tubFileNotes}`;
  }

  get updateTubFileNotes(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.tfNote}`;
  }

  get sendEmail(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.sendEmail}`;
  }


  get getDoaFormDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.detailView}`;
  }

  get updateDoaFormDetails(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.update.updateEndpoint}`;
  }

  get closeEvent(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.close}`;
  }

  get getDssPermissions(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.permissions.getActions}`;
  }

  get getAcnCache(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.acnCache}`;
  }

  get getUserInfo(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.user}`;
  }

  get httpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa(`${environment.api.auth.username}:${environment.api.auth.password}`)
      })
    };
  }

  get getDiscrepancyUpdttxt(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.retrieval.retrievalEndpoint}${environment.api.endpoints.retrieval.discrepancyUpdatedText}`;
  }



  get createIntakeForm(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.create}`;
  }

  get getAllUserIntakeForms(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.getAllUserIntakeForms}`;
  }

  get updateIntakeForm(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.updateIntakeForm}`;
  }

  get getRoleAndEventType(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.getRoleAndEventType}`;
  }

  get deleteUserIntakeForm(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.deleteUserIntakeForm}`;
  }

  get deleteQuestion(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.deleteQuestion}`;
  }

  get getAllQuestions(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.getAllQuestions}`;
  }

  get updateQuestion(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.updateQuestion}`;
  }

  get addNewQuestion(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.addNewQuestion}`;
  }

  get getUserIntakeForm(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.getUserIntakeForm}`;
  }

  get getIntakeForms(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.userIntakeForm.getIntakeForms}`;
  }

  get getChangeStatusEtic(): any {
    return `${environment.api.baseUrl}${environment.api.extension}${environment.api.endpoints.listActions.changestatus}`;
  }
}