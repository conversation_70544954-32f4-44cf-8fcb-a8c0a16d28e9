export class AddEventData {

    public static eventSections: any = {
        'OOS': ['Event Details', 'Discrepancies', 'Tub File Notes', 'Reporting Categories'],
        'TRK': ['Event Details', 'Discrepancies', 'Tub File Notes', 'Reporting Categories'],
        'NOTE': ['Event Details', 'Tub File Notes', 'Reporting Categories'],
        'DOA' : ['Discrepancies', 'DOA Flight', 'Add DOA Event', 'Tub File Notes', 'Reporting Categories']
    };

    public static events: string[] = ['OOS', 'TRK', 'NOTE', 'DOA'];

    public static owners: string[] = ['H10', 'AV10', 'LINE', 'HVMX', 'VNMX', 'H530'];

    public static eventStatus: string[] = ['AOG', 'DWN', 'HMD', 'HMO', 'HMX', 'NAS', 'NOS', 'TRK', 'INF'];

    public static addEventForm: any = {
            "eventId": "",
            "changeType": 1,
            "reviewChangeType": 2,
            "groupId": "",
            "groupTitle": "",
            "acn": "",
            "eventType": "",
            "userId": "U123",
            "tokenId": "Token123",
            "accessLevel": "90",
            "aircraftType": "",
            "station": "",
            "startDateTime": "",
            "status": "",
            "eticDateTime": "",
            "eticInfo": "",
            "eticComment": "",
            "affectedFlightNumber": "",
            "affectedFlightDate": "",
            "affectedFlightLegNumber": "",
            "contactInfoOwner": "",
            "contactInfoContact": "",
            "addTubFile": true,
            "addLinkedDiscrepancy": false,
            "employeeName": "SIVA",
            "destination": "",
            "additionalDescription": "",
            "equipmentType": "",
            "requestStatus": "",
            "tubFileNote": [],
            "isDOAEvent": false,
            "isNewComment": true,
            "isInTransit": false,
            "checkFlightRequired": false,
            "submitChange": false,
            "isAffectedFlightDeleted": false,
            "isPendingRequest": false,
            "eticEnteredInError": false,
            "modifyPendingEvent": false,
            "cancelPendingEvent": false,
            "displayReportingCategories": false,
            "isDOAComplied": false,
            "doaCompliedMaintenance": false,
            "isReportingCategoryApplicable": false,
            "discrepancyList": [],
            "reportingCategoriesKeys": [],
            "tfNotesList": [],
            "msnData":[],
            "convertedDateTime": "",
            "createdDateTime": "",
            "reviewedDateTime": "",
            "changedDateTime": "",
            "closedDateTime": "",
            "cancelledDateTime": "",
            "managerNote": "",
            "superUpdateRequired": false,
            "serverError": null,
            "overrideRequest": false,
            "overrideEventId": 0,
            "continueAddingEvent": false,
            "showTubFileNotes": false,
            "showDiscrepancyCard": false,
            "empDepartment": "",
            "addNewEvent": false,
            "reopenEvent": false,
            "resMgrId": "",
            "memDeskContact": "",
            "OST": "",
    }
}