import { Component, OnInit } from '@angular/core';
import { UserLoginService } from '../../services/user-login.service';
import { ActivatedRoute } from '@angular/router';

@Component({
    selector: 'app-layout',
    templateUrl: './layout.component.html',
    styleUrl: './layout.component.scss',
    standalone: false
})
export class LayoutComponent implements OnInit {

    authReady$: any;
    isAuthenticated$: any; 

    constructor(
        private userService: UserLoginService,
        private route: ActivatedRoute) {}

    async ngOnInit() {
        this.authReady$ = this.userService.isAuthReady();
        this.isAuthenticated$ = this.userService.isAuthenticated();
    }

}
