import { Component } from '@angular/core';

@Component({
  selector: 'app-custom-maintenance-event-list-alerts',
  templateUrl: './custom-maintenance-event-list-alerts.component.html',
  styleUrl: './custom-maintenance-event-list-alerts.component.scss',
  standalone: false
})
export class CustomMaintenanceEventListAlertsComponent {

  leftValue: string = '';
  rightValue: string = '';

  agInit(params: any): void {
    this.leftValue = params.data.requestStatus == "C" ? params.data.newStatus : "";
    this.rightValue = params.data.requestStatus == "C" ? params.data.lastUpdateDateTime.split(" ")[1].substring(0, 5) : "";
  }

  refresh(params: any): boolean {
    this.agInit(params);
    return true;
  }

}
