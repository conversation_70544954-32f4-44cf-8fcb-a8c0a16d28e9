import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { ToastrMessageService } from '../services/toastr-message.service';

@Injectable()
export class ErrorInterceptorService implements HttpInterceptor {
  
  constructor(private dialog: MatDialog, private toastrMessageService: ToastrMessageService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      tap(() => {}),
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'An error occurred';
        let errorTitle = 'Error';

        if (error.error instanceof ErrorEvent) {
          // Client-side error
          errorMessage = error.error.message;
        } else {
          // Server-side error
          switch (error.status) {
            case 400:
              errorTitle = 'Bad Request';
              errorMessage = error?.error?.error ? error?.error?.error : 'The request was invalid';
              this.toastrMessageService.error(errorMessage);
              break;
            case 401:
              errorTitle = 'Unauthorized';
              errorMessage = 'Please log in to continue';
              this.toastrMessageService.error(errorMessage);
              break;
            case 403:
              errorTitle = 'Forbidden';
              errorMessage = 'You don\'t have permission to access this resource';
              break;
            case 404:
              errorTitle = 'Not Found';
              errorMessage = 'The requested resource was not found';
              break;
            case 500:
              errorTitle = 'Server Error';
              errorMessage = 'Something went wrong on our end for ' + error?.error?.path; 
              this.toastrMessageService.error(errorMessage);
              break;
            case 503:
              errorTitle = 'Service Unavailable';
              errorMessage = 'Please try again later';
              break;
            default:
              errorTitle = `Error ${error?.status}`;
              errorMessage = error?.message;
          }
        }

        // Open a Material Dialog with the error message
        // this.dialog.open(ConfirmationDialogComponent, {
        //   width: '400px',
        //   panelClass: 'center-dialog-content',
        //   data: {
        //     title: errorTitle,
        //     message: errorMessage,
        //     ok: 'Dismiss',
        //     no: '',
        //     singleButtonDialog: true
        //   }
        // });

        return throwError(() => error);
      })
    );
  }
}