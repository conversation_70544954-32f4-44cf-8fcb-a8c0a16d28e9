/* Existing CSS remains unchanged */
.admin-container {
  height: 95%;
  margin: 1.5% 0.5rem;
  background-color: #f3f4f6;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.admin-header {
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: #fff;
  width: 100%;
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: sticky;
  top: 0;
  z-index: 10;
  cursor: default;
}

.header-content {
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
}

.header-content h1 {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  flex: 1;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.admin-header:hover .header-content h1 {
  transform: scale(1.05);
}

.admin-main {
  flex-grow: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.section {
  margin-bottom: 2rem;
  text-align: center;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #3f2876;
  margin-bottom: 1rem;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(90deg, #6c49b9, #3f2876);
  color: white;
  border-radius: 10px 10px 0 0;
  display: inline-block;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: default;
}

.section-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 3s infinite;
}

.section-title:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

@keyframes shine {
  0% { left: -100%; }
  20% { left: 100%; }
  100% { left: 100%; }
}

.card-grid {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.card-grid.three-cards,
.card-grid.two-cards {
  justify-content: center;
}

.admin-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  width: 280px;
  height: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.4s ease;
  cursor: pointer;
  transform: scale(1);
}

.admin-card:hover {
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.admin-card:hover .card-title {
  color: white;
}

.admin-card:hover .card-description {
  color: #ff6600;
  font-weight: bold;
}

.admin-card:hover .card-icon {
  color: #ff6600 !important;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.card-icon {
  font-size: 2.5rem;
  color: #6c49b9;
  transition: color 0.3s ease;
}

.card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #6c49b9;
  transition: color 0.3s ease;
}

.card-description {
  position: relative;
  top: 10px;
  font-size: 0.8rem;
  color: #6b7280;
  padding: 0.5rem;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.admin-card:focus-within,
.admin-card:focus-visible {
  outline: 2px solid #6c49b9;
  outline-offset: 2px;
}

.mat-icon {
  width: fit-content;
  height: fit-content;
}

@media (max-width: 768px) {
  .card-grid {
    flex-direction: column;
    align-items: center;
  }

  .admin-card {
    width: 90%;
  }

  .admin-header {
    padding: 0.25rem 0;
  }

  .header-content h1 {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1rem;
    padding: 0.25rem 1rem;
  }
}

/* New CSS for enhanced UI and interactivity */
.admin-container {
  // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15), 0 personally4px 12px rgba(0, 0, 0, 0.1);
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  border: 1px solid rgba(108, 73, 185, 0.1);
}

.admin-main {
  background: linear-gradient(180deg, #f9fafb 0%, #e5e7eb 100%);
}

.admin-card {
  border: 1px solid rgba(108, 73, 185, 0.2);
  transition: transform 0.4s ease, box-shadow 0.4s ease, background 0.4s ease;
}

.admin-card:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 4px rgba(108, 73, 185, 0.3);
}

.admin-card:hover .card-icon {
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.card-title {
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.02em;
}

.card-description {
  font-size: 0.85rem;
  line-height: 1.4;
  max-width: 90%;
}

.admin-card:focus-within,
.admin-card:focus-visible {
  outline: 3px solid #6c49b9;
  outline-offset: 4px;
  box-shadow: 0 0 0 6px rgba(108, 73, 185, 0.3);
}

.section {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity:1; transform: translateY(0); }
}

.card-content {
  justify-content: center;
  gap: 0.75rem;
}

@media (max-width: 1024px) {
  .admin-card {
    width: 260px;
    height: 200px;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .admin-container {
    padding: 0.5vh;
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  }

  .admin-main {
    padding: 0.5rem;
  }

  .section {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 480px) {
  .admin-card {
    width: 100%;
    max-width: 300px;
    height: 180px;
  }

  .card-icon {
    font-size: 2rem;
  }

  .card-title {
    font-size: 0.95rem;
  }

  .card-description {
    font-size: 0.75rem;
    top: 8px;
  }
}

/* Additional interactive CSS enhancements */

/* Glow effect on card hover */
.admin-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 8px rgba(108, 73, 185, 0.5);
}

/* Ripple effect on card click */
.admin-card {
  position: relative;
  overflow: hidden;
}

.admin-card::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.admin-card:active::after {
  width: 200%;
  height: 200%;
}

/* Smoother background transition */
.admin-card {
  transition: transform 0.4s ease, box-shadow 0.4s ease, background 0.6s ease-in-out;
}

/* Icon bounce animation */
.admin-card:hover .card-icon {
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(5px); }
  100% { transform: translateY(0); }
}

/* Card content fade effect */
.card-content {
  transition: opacity 0.3s ease;
}

.admin-card:hover .card-content {
  opacity: 0.9;
}

/* Enhanced focus animation */
.admin-card:focus-within,
.admin-card:focus-visible {
  transform: scale(1.02);
  transition: transform 0.3s ease, outline 0.3s ease, box-shadow 0.3s ease;
}