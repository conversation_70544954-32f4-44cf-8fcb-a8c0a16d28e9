import { Component } from '@angular/core';
import { questions } from '../../dto/questionsDto';
import { UserIntakeForm } from '../../dto/UserIntakeFormDto';
import { Subscription } from 'rxjs';
import { Router, RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AdministrativeService } from '../../services/administrative.service';
import { AdmininstrativeSharedService } from './admininstrative-shared.service';
import { UserIntakeFormDialogComponent } from './user-intake-form-dialog/user-intake-form-dialog.component';
import { answers } from '../../dto/answersDto';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-admininstrative',
  standalone: false,
  templateUrl: './admininstrative.component.html',
  styleUrl: './admininstrative.component.scss'
})
export class AdmininstrativeComponent {

private userRoleEventType: any;
  private allQuestions: Array<questions> = [];
  private allUserIntakeForms: Array<UserIntakeForm> = [];
  private userIntakeFormsSubscription!: Subscription;

  constructor(
    public router: Router,
    public dialog: MatDialog,
    public administrativeService: AdministrativeService,
    private admininstrativeSharedService: AdmininstrativeSharedService
  ) {
    this.admininstrativeSharedService.refreshUserIntakeForms();
  }

  ngOnInit(): void {
    console.log('AdmininstrativeComponent initialized');
    this.fetchUserRoleEventType();
    this.fetchAllQuestion();
    this.subscribeToUserIntakeForms();
  }

  ngOnDestroy(): void {
    if (this.userIntakeFormsSubscription) {
      this.userIntakeFormsSubscription.unsubscribe();
    }
  }

  private subscribeToUserIntakeForms(): void {
    this.userIntakeFormsSubscription = this.admininstrativeSharedService.userIntakeForms$.subscribe(forms => {
      this.allUserIntakeForms = [...forms];
      console.log('All intake forms updated via shared service:', this.allUserIntakeForms);
    });
    this.admininstrativeSharedService.refreshUserIntakeForms();
  }

  private fetchUserRoleEventType(): void {
    this.administrativeService.getRoleAndEventType().subscribe({
      next: (data) => {
        console.log('Administrative data loaded:', data);
        this.userRoleEventType = data;
      },
      error: (error) => {
        console.error('Error loading administrative data:', error);
      }
    });
  }

  private fetchAllQuestion(): void {
    this.administrativeService.getAllQuestions().subscribe({
      next: (data: any[]) => {
        console.log('Questions data loaded:', data);
        this.allQuestions = data.map(questionData => {
          const question = new questions(questionData);
          if (questionData.answers && Array.isArray(questionData.answers)) {
            question.answers = questionData.answers.map((answerData: any) => new answers(answerData));
          }
          return question;
        });
        console.log('Mapped allQuestions:', this.allQuestions);
      },
      error: (error) => {
        console.error('Error loading questions:', error);
      }
    });
  }

  openUserIntakeFormDialog(): void {
    console.log('Opening User Intake Form Dialog');
    const dialogRef = this.dialog.open(UserIntakeFormDialogComponent, {
        width: '40vw', /* Dialog visible at 50% width */
        maxWidth: '100vw',
        height: '45vh',
        disableClose: true,
        autoFocus: false,
        data: { userRoleEventType: this.userRoleEventType }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.router.navigate(['/create-intake-form'], {
            queryParams: {
                intakeFormDetails: JSON.stringify(result),
                allQuestions: JSON.stringify(this.allQuestions)
            }
        });
      }
    });
  }

  editUserIntakeFormDialog(): void {
    if (this.allUserIntakeForms.length > 0) {
      this.router.navigate(['/edit-intake-form'], {
        queryParams: {
          allUserIntakeForms: JSON.stringify(this.allUserIntakeForms),
          allQuestions: JSON.stringify(this.allQuestions)
        }
      });
    }else{
      Swal.fire('Info', 'There are no previous intake forms to show.', 'info');
    }
  }

  editQuestions(): void {
    if (this.allQuestions.length > 0) {
      this.router.navigate(['/questions-list'], {
        queryParams: {
          allQuestions: JSON.stringify(this.allQuestions)
        }
      });
    }
  }

  navigateToTimer(): void {
    console.log('Navigating to NIW Timer Administration');
    this.router.navigate(['/niw-timer']);
  }

  navigateToReporting(): void {
    console.log('Navigating to Reporting Categories');
    this.router.navigate(['/reporting-categories']);
  }

}
