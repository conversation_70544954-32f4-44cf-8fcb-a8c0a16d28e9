.header-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% - 10px); /* Matches maintenance component width */
  margin: 5px auto;
  padding: 1rem;
  background: #B7BBE3; /* Reintroduced background color */
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 12px;

  &.header-centered {
    min-height: 98.5%;
    position: relative;

    .header-card {
      background: #ffffff;
      border-radius: 20px;
      border: 4px solid #6c49b9;
      box-shadow: 0 16px 40px rgba(63, 40, 118, 0.35);
      padding: 3rem;
      position: relative;
      overflow: hidden;
      animation: float 4s ease-in-out infinite, fadeInScale 0.8s ease-out forwards;

      &:hover {
        transform: scale(1.03);
        box-shadow: 0 20px 48px rgba(63, 40, 118, 0.4);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 8px;
        background: linear-gradient(90deg, #6c49b9, #ff6600);
        border-radius: 20px 20px 0 0;
      }
    }
  }

  &.header-normal {
    padding: 0;
    height: 140px; /* Slightly increased for better balance */
    align-items: center;
    border-bottom: 5px solid #6c49b9;
    animation: slideToTop 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    box-shadow: 0 4px 12px rgba(63, 40, 118, 0.2);

    .header-card {
      width: 100%;
      max-width: calc(100% - 10px); /* Matches maintenance component width */
      background: transparent;
      box-shadow: none;
      border: none;
      padding: 0;
    }
  }
}

.header-title {
  width: 100%;
  background: linear-gradient(135deg, #2a2a72, #6a5acd);
  color: #ffffff;
  text-align: center;
  padding: 2px 0;
  font-size: 1rem;
  font-weight: 800;
  text-transform: uppercase;
  border-radius: 12px 12px 0 0;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 1.2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
  }
}

.note-text {
  color: #3F2876;
  font-weight: 700;
  font-size: 1.1rem;
  text-align: center;
  margin: 0.75rem 0 4rem 0;
  padding: 1rem;
  border-radius: 10px;
  background: #f8f7ff;
  border: 3px solid #6c49b9;
  box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
  animation: fadeInUp 0.6s ease-in-out;
  letter-spacing: 0.5px;
}

.note-container {
  width: calc(100% - 10px); /* Matches maintenance component width */
  margin: 1.5rem auto;
  text-align: center;
  background: #B7BBE3; /* Added background color */
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(63, 40, 118, 0.2);
}

.header-form {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem; /* Increased gap for better spacing */
  flex-wrap: nowrap;
}

.input-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  flex-wrap: nowrap;
}

.header-input {
  width: 240px; /* Slightly wider for prominence */

  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 14px;
      background: #ffffff;
      border: 3px solid #6c49b9;
      transition: all 0.4s ease;
      padding: 0.75rem;
      animation: pulseBorder 2s ease-in-out infinite;
    }

    &.mat-focused .mat-mdc-text-field-wrapper {
      border-color: #3F2876;
      box-shadow: 0 0 0 6px rgba(108, 73, 185, 0.35);
      transform: scale(1.02);
      animation: none;
    }

    .mat-mdc-select-value,
    .mdc-text-field__input {
      color: #3F2876 !important;
      font-weight: 700;
      font-size: 1.1rem;
    }

    .mat-mdc-form-field-error {
      color: #cc5200;
      font-size: 0.9rem;
      animation: shake 0.3s ease-in-out;
    }
  }
}

.button-group {
  display: flex;
  gap: 3rem;
  align-items: center;
}

.header-button {
  padding: 0.85rem 2rem;
  border-radius: 10px;
  font-weight: 700;
  font-size: 0.9rem;
  transition: all 0.4s ease;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  color: #ffffff;
  height: 45px;
  line-height: 45px;

  &.save-button {
    background-color: #6c49b9;
    color: #ffffff;
    padding: 10px 15px;

  &disabled {
      background: #cccccc !important;
      opacity: 0.7;
      pointer-events: auto;
      cursor: not-allowed !important;
      transform: none;
      box-shadow: none;
    }

    &:hover:not(.disabled) {
      background: linear-gradient(135deg, #6c49b9, #3F2876);
      transform: translateY(-1px);
      box-shadow: 0 10px 28px rgba(63, 40, 118, 0.6);
    }
  }

  &.reset-button {
    background: linear-gradient(135deg, #cc5200, #ff6600);

    &:hover:not(.disabled) {
      background: linear-gradient(135deg, #ff6600, #cc5200);
      transform: translateY(-1px);
      box-shadow: 0 10px 28px rgba(204, 82, 0, 0.6);
    }
  }

  &.disabled {
    background: #cccccc !important;
    opacity: 0.7;
    pointer-events: auto;
    cursor: not-allowed !important;
    transform: none;
    box-shadow: none;
  }
}

@keyframes fadeInScale {
  0% {
    transform: scale(0.92) translateY(-40px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes slideToTop {
  0% {
    transform: translateY(80vh); /* Reduced initial translation for smoother effect */
    opacity: 0.6;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(-8px);
    box-shadow: 0 12px 32px rgba(63, 40, 118, 0.3);
  }
  50% {
    transform: translateY(0);
    box-shadow: 0 18px 44px rgba(63, 40, 118, 0.35);
  }
  100% {
    transform: translateY(-8px);
    box-shadow: 0 12px 32px rgba(63, 40, 118, 0.3);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(25px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseBorder {
  0% {
    border-color: #6c49b9;
    box-shadow: 0 0 0 0 rgba(108, 73, 185, 0.3);
  }
  50% {
    border-color: #3F2876;
    box-shadow: 0 0 0 5px rgba(108, 73, 185, 0.4);
  }
  100% {
    border-color: #6c49b9;
    box-shadow: 0 0 0 0 rgba(108, 73, 185, 0.3);
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@media (max-width: 768px) {
  .header-container.header-centered .header-card {
    width: 95%;
    padding: 2rem;
  }

  .header-form {
    flex-direction: column;
    gap: 1.5rem;
  }

  .input-group {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-input {
    width: 100%;
    max-width: 320px;
  }

  .button-group {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-title {
    font-size: 1.4rem; /* Slightly smaller for mobile */
  }
}

.mat-label {
  font-weight: bold;
}

.form-field ::ng-deep .mat-select-value,
.form-field ::ng-deep input,
.form-field ::ng-deep textarea {
  background-color: lavender;
  color: #6a5acd !important;
  justify-items: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
}

::ng-deep .mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input {
  background-color: lavender;
  color: #6a5acd !important;
  text-align-last: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
}

::ng-deep .mat-mdc-select-value {
  background-color: lavender;
  color: #6a5acd !important;
  justify-items: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
  text-align-last: center;
}

.custom-addEvent-confirmation-dialog-container {
  width: 50vw !important;
  min-width: 50vw !important;
  max-width: 90vw !important;
  position: static;
  margin: 0 auto;
  box-sizing: border-box;
}