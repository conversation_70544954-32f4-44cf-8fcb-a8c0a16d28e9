import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Input, NgZone, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ClientSideRowModelModule, ColDef, GridApi, GridOptions, Module, RowDoubleClickedEvent } from 'ag-grid-community';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { RetrievalDto } from '../../../dto/retrievalDto';
import { MatDialog } from '@angular/material/dialog';
import { DiscrepanciesDetailDialogComponent } from './discrepancies-detail-dialog/discrepancies-detail-dialog.component';
import { AppLayoutService } from '../../../../app-layout/services/app-layout.service';
import { concatMap, Observable, Subject, takeUntil } from 'rxjs';
import { DiscrepanciesFilterDialogComponent } from './discrepancies-filter-dialog/discrepancies-filter-dialog.component';
import { CustomFilterIconComponent } from './custom-filter-icon/custom-filter-icon.component';
import { MetsEventUpdateEntity } from '../../../dto/maintenance-event-update-dto';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { DiscrepanciesList, OpenDiscrepanciesResponseDao } from '../../../dao/discrepancies-listDao';
import { CustomLinkDowningitemComponent } from './custom-link-downingitem/custom-link-downingitem.component';
import { UpdateDiscrepancyDto } from '../../../dto/updateDiscrepancyDto';
import { ToastrMessageService } from '../../../../app-layout/services/toastr-message.service';
import { DiscrepancyTxtReq, linkedDiscrepancyUpdTxt } from '../../../dao/discrepancyUpdTxt';
import { MaintenanceEventDetailsSharedService } from '../maintenance-event-details-shared.service';

@Component({
  selector: 'app-discrepancies',
  templateUrl: './discrepancies.component.html',
  styleUrl: './discrepancies.component.scss',
  standalone: false
})

export class DiscrepanciesComponent implements OnInit, OnChanges, OnDestroy {

  @ViewChild('agGrid', { static: false, read: ElementRef }) agGridElement!: ElementRef;

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;
  @Input() acn: string = "";
  @Input() eventValue: string = "";
  @Input() isAddEventSelected: boolean = false;

  @Output() addDiscrepanciesData = new EventEmitter<any>();
  @Output() detailsDiscrepanciesData = new EventEmitter<any>();

  private destroy$ = new Subject<void>();

  columnDefs: ColDef[] = [];
  storedDefaultColumnDefs: any;
  filteredTableData: any[] = [];
  rowData: any[] = [];
  previousOriginalSelectedDiscrepancies: any[] = [];
  previousSelectedDiscrepancies: any[] = [];
  updatedDiscrepancies: any[] = [];
  discrepancyTxtreq: DiscrepancyTxtReq[] = [];

  addedDiscrepancies: any[] = [];

  eventLinkedDiscrepancies: DiscrepanciesList[] = [];
  gridHeight: number = 37;
  isRowSelected: boolean = false;
  selectedRow: any = null;
  isSideNavClosed: boolean = false;
  isUpdateEnabled: boolean = false;
  animateTitle: boolean = false;
  selectedRowIndex: number | null = null; // Track selected row

  updateDiscrepanciesDto: MetsEventUpdateEntity = new MetsEventUpdateEntity()

  gridApi!: GridApi;
  gridColumnApi: any;
  gridOptions: GridOptions = {
    rowBuffer: 10,
    rowHeight: 30, // Ensures uniform row height
    suppressTouch: false,
    stopEditingWhenCellsLoseFocus: true,
    suppressColumnVirtualisation: true,
    suppressScrollOnNewData: true,
    suppressAnimationFrame: true,
    animateRows: true,
    headerHeight: 40,
    enableBrowserTooltips: true,
    suppressRowClickSelection: false,
    context: {
      onSettingsClick: this.openFilterDiscrepancyDialog.bind(this)
    },
    rowStyle: {
      fontSize: '13px', // Smaller font size
      padding: '0px', // Remove row padding
      margin: '0px', // Remove row margin
      border: 'none', // Ensure no row border
    },
    // Ensure all rows are rendered
    ensureDomOrder: true,
    // Increase pagination or disable it to handle large datasets
    pagination: false, 
  };

  defaultColDef: ColDef = {
    headerClass: 'center-header',
    headerComponentParams: { menuIcon: true },
    cellStyle: {
      textAlign: 'center',
      verticalAlign: 'middle',
      padding: '0px',  // Remove cell padding
      border: 'none',  // Remove cell borders
      whiteSpace: 'normal',
      wordWrap: 'break-word',
      overflow: 'visible'
    },
    sortable: true,
    filter: true,
    editable: false,
    resizable: true,
  };

  modules: Module[] = [ClientSideRowModelModule];

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private appLayoutService: AppLayoutService,
    private cdRef: ChangeDetectorRef,
    private toastrService: ToastrMessageService,
    public dialog: MatDialog,
    private ngZone: NgZone,
    private maintenanceEventDetailsSharedService: MaintenanceEventDetailsSharedService,
  ) { }

  ngOnInit(): void {
    // this.appLayoutService.sideNavClosedObservable$
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe((isClosed) => {
    //     setTimeout(() => {
    //       this.isSideNavClosed = isClosed || false;
    //       this.setColumnDefs(this.isSideNavClosed);
    //       this.calculateGridHeight();
    //       this.adjustGridSizeSmoothly();
    //     }, 100);
    // });
    if (this.columnDefs.length === 0) {
      this.setColumnDefs(false);
    }
    this.calculateGridHeight();
    this.maintenanceEventDetailsService.showTitleEffect$.subscribe((show: boolean) => {
      if (show) {
        this.animateTitle = false; // reset
        setTimeout(() => {
          this.animateTitle = true; // trigger reflow to apply animation
        }, 0);
      } else {
        this.animateTitle = false;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") {
        this.getDiscrepanciesTableData(this.detailsViewObj);
      } else {
        this.rowData = [];
        this.filteredTableData = [];
      }
    }

    if (changes['acn'] || changes['eventtype']) {
      this.getAddDiscrepanciesTableData();
      setTimeout(() => {
        document.querySelector('.title')?.classList.add('fill-animation');
      }, 200);
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.calculateGridHeight(); // Recalculate grid height on window resize
    this.setColumnDefs(this.isSideNavClosed);
    this.resizeGridColumns();
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    params.api.sizeColumnsToFit();
  }

  onRowSelected(event: any) {
    this.selectedRow = event.api.getSelectedRows()[0];
    this.selectedRow != undefined && this.selectedRow != null ? this.isRowSelected = true : this.isRowSelected = false;
    this.detailsDiscrepanciesData.emit({
      rowSelected: this.selectedRow != null && this.selectedRow != undefined ? true : false,
      updateEnabled: this.isUpdateEnabled,
    });
  }

  setColumnDefs(isSideNavClosed: boolean) {

    let baseColumnWidths = this.getBaseColumnWidths();

    if (this.columnDefs.length === 0) {
      this.maintenanceEventDetailsService.getMaintenanceEventDetailsDiscrepanciesTableHeaders().subscribe({
        next: (data: any[]) => {

          data = this.isAddEventSelected
            ? data.filter(col => col.field !== 'priority' && col.field !== 'timeRemaining')
            : data.filter(col => col.field !== 'copyToComment' && col.field !== 'copyToManagerNotes');

          this.columnDefs = data.map((column) => ({
            ...column,
            // flex: baseColumnWidths[column.field as keyof typeof baseColumnWidths] || 1,
            // minWidth: baseColumnWidths[column.field as keyof typeof baseColumnWidths] || 1,
            filter: column.filter ?? true,
            sortable: column.sortable ?? true,
            editable: column.editable ?? false,
            resizable: column.resizable ?? true,
            lockPosition: column.field === 'settings' ? true : false,
            headerComponent: column.field === 'settings' ? CustomFilterIconComponent : undefined,
            ...(column.field === 'link' && {
              cellRenderer: CustomLinkDowningitemComponent,
              cellEditor: CustomLinkDowningitemComponent,
              cellEditorParams: { color: '#6c49b9' },
              editable: true,
            }),
            ...(column.field === 'downingItem' && {
              cellRenderer: CustomLinkDowningitemComponent,
              cellEditor: CustomLinkDowningitemComponent,
              cellEditorParams: { color: '#6c49b9' },
              editable: true,
            }),
            ...(column.field === 'copyToComment' && {
              cellRenderer: CustomLinkDowningitemComponent,
              cellEditor: CustomLinkDowningitemComponent,
              cellEditorParams: { color: '#6c49b9' },
              editable: true,
            }),
            ...(column.field === 'copyToManagerNotes' && {
              cellRenderer: CustomLinkDowningitemComponent,
              cellEditor: CustomLinkDowningitemComponent,
              cellEditorParams: { color: '#6c49b9' },
              editable: true,
            }),      
          }));

          

          this.storedDefaultColumnDefs = [...this.columnDefs];
          this.refreshGrid();
        },
        error: (error) => {
          console.error('Error fetching column definitions:', error);
        },
      });
    } else {
      this.columnDefs = this.storedDefaultColumnDefs.map((column: any) => ({
        ...column,
        // flex: baseColumnWidths[column.field as keyof typeof baseColumnWidths] || 1,
        // minWidth: baseColumnWidths[column.field as keyof typeof baseColumnWidths] || 1,
      }));
      this.refreshGrid();
    }
  }

  getBaseColumnWidths(): { [key: string]: number } {
    const isClosed = this.isSideNavClosed;
    const isAddEvent = this.isAddEventSelected;


    if (isAddEvent) {
      return isClosed
        ? { link: 5, ata: 5, number: 7, displayOpenDate: 8, discType: 11, copyToComment: 7, copyToManagerNotes: 7, downingItem: 10, text: 30, eventType: 10, settings: 5 }
        : { link: 8.5, ata: 8, number: 7.5, displayOpenDate: 7, discType: 11, copyToComment: 7, copyToManagerNotes: 7, downingItem: 9.5, text: 20.5, eventType: 12, settings: 2 };
    } else {
      return isClosed
        ? { link: 5.5, ata: 6.5, number: 8.3, displayOpenDate: 8.3, discType: 7, downingItem: 9, priority: 7, timeRemaining: 9.2, text: 27.5, eventType: 9.6, settings: 2 }
        : { link: 6.5, ata: 8, number: 10, displayOpenDate: 11.5, discType: 8.5, downingItem: 8.5, priority: 8.5, timeRemaining: 9, text: 17, eventType: 10.5, settings: 2 };
    }
  
    // if (isAddEvent) {
    //   return isClosed
    //     ? { link: 7.5, ata: 8.05, number: 9.15, displayOpenDate: 10.3, discType: 9, downingItem: 9.5, text: 34.5, eventType: 10, settings: 2 }
    //     : { link: 8.5, ata: 10, number: 12.5, displayOpenDate: 14, discType: 11, downingItem: 9.5, text: 20.5, eventType: 12, settings: 2 };
    // } else {
    //   return isClosed
    //     ? { link: 5.5, ata: 6.5, number: 8.3, displayOpenDate: 8.3, discType: 7, downingItem: 9, priority: 7, timeRemaining: 9.2, text: 27.5, eventType: 9.6, settings: 2 }
    //     : { link: 6.5, ata: 8, number: 10, displayOpenDate: 11.5, discType: 8.5, downingItem: 8.5, priority: 8.5, timeRemaining: 9, text: 17, eventType: 10.5, settings: 2 };
    // }
  }


  onRowDoubleClick(event: RowDoubleClickedEvent) {
    this.openDiscrepanciesDetailDialog();
  }

  getAddDiscrepanciesTableData() {
    this.maintenanceEventDetailsService.getDiscrepanciesTableList(parseInt(this.acn), null).subscribe({
      next: (response: OpenDiscrepanciesResponseDao) => {

        this.previousSelectedDiscrepancies = this.previousOriginalSelectedDiscrepancies = this.updatedDiscrepancies = [];

        response.discrepancyList.forEach((row: any) => {
          row.link = row.downingItem = false;
        });

        this.rowData = [...response.discrepancyList];
        this.rowData.forEach((row: any) => {
          row.displayOpenDate = row.openDate != null ? this.formatOpenDate(row.openDate) : null;
          row.closed = row.closed === null ? false : row.closed;
          row.copyToComment = row.copyToManagerNotes = false;
        });

        this.filteredTableData = this.rowData;
        this.cdRef.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching maintenance discrepancies details header:', error);
      }
    });
  }

  getDiscrepanciesTableData(detailsViewObj: DetailViewResponseDao) {
    this.maintenanceEventDetailsService.getDiscrepanciesTableList(parseInt(detailsViewObj?.eventACN), detailsViewObj?.eventID).subscribe({
      next: (response: OpenDiscrepanciesResponseDao) => {

        this.previousSelectedDiscrepancies = this.previousOriginalSelectedDiscrepancies = this.updatedDiscrepancies = [];

        response.linkedDiscrepancyList.forEach((row: any) => {
          row.isDowningItemPreviouslySelected = row.downingItem ? true : false;
        });

        this.eventLinkedDiscrepancies = response.linkedDiscrepancyList;
        if (response.discrepancyList.length > 0) {
          this.maintenanceEventDetailsSharedService.setLinkedDiscrepancies(response.discrepancyList.slice(0,3));
        }
        if (response.discrepancyList.length > 0) {
          this.discrepancyTxtreq = response.discrepancyList.slice(0,3).map((row: DiscrepanciesList) => ({
            ata: row.ata,
            discNumber: row.number,
            acn: detailsViewObj?.eventACN
        }));
        // if (this.eventLinkedDiscrepancies.length > 0) {
        //   this.maintenanceEventDetailsSharedService.setLinkedDiscrepancies(this.eventLinkedDiscrepancies);
        // }
        // if (this.eventLinkedDiscrepancies.length > 0) {
        //   this.discrepancyTxtreq = this.eventLinkedDiscrepancies.map((row: DiscrepanciesList) => ({
        //     ata: row.ata,
        //     discNumber: row.number,
        //     acn: detailsViewObj?.eventACN
        //   }));
          this.maintenanceEventDetailsService.getDiscrepancyUpdtTxts(this.discrepancyTxtreq).subscribe({
            next: (linkedDiscrepancyUpdTxt: linkedDiscrepancyUpdTxt[]) => {
              this.maintenanceEventDetailsSharedService.setLinkedDsicrpancyUpdtTxt(linkedDiscrepancyUpdTxt);
              // linkedDiscrepancyUpdTxt.forEach((row: linkedDiscrepancyUpdTxt) => {
              //   const ata = row.ata.trim();
              //   const discNumber = row.dscrNumber.trim();
              //   const dscrpTxts = row.dscrpTxts.map(txt => ({
              //     actualTxt: txt.actualTxt,
              //     formattedTxt: txt.formattedTxt
              //   }));
              //   const existingRow = this.eventLinkedDiscrepancies.find(r => r.ata.trim() === ata && r.number.trim() === discNumber);
              //   if (existingRow) {
              //     existingRow.dscrpTxts = dscrpTxts;
              //   }
              // });
            },
            error: (error) => {
            }
          });
        }

        // Create a deep copy to store the original values
        this.previousSelectedDiscrepancies = JSON.parse(JSON.stringify([...response.linkedDiscrepancyList]));
        this.previousSelectedDiscrepancies.forEach((row: any) => { row.isModified = true });
        this.previousOriginalSelectedDiscrepancies = JSON.parse(JSON.stringify(response.linkedDiscrepancyList)); // Deep copy

        this.rowData = JSON.parse(JSON.stringify([...response.linkedDiscrepancyList, ...response.discrepancyList]));
        this.rowData.forEach((row: any) => {
          row.displayOpenDate = row.openDate != null ? this.formatOpenDate(row.openDate) : null;
          row.closed = row.closed === null ? false : row.closed;
          row.copyToComment = row.copyToManagerNotes = false;
        });

        this.filteredTableData = this.rowData;
        this.maintenanceEventDetailsService.getDiscrepanciesFilterFromSessionStorage() != null ? this.filterTableData(this.maintenanceEventDetailsService.getDiscrepanciesFilterFromSessionStorage()) : null;
        this.cdRef.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching maintenance discrepancies details header:', error);
      }
    });
  }

  updateDiscrepancies() {
    const totalUpdatedDiscrepancies = [...this.previousSelectedDiscrepancies, ...this.updatedDiscrepancies];
    totalUpdatedDiscrepancies.forEach((row: any) => { row.isModified = true });
    const updateDiscrepancyDto: UpdateDiscrepancyDto = {
      access_level: '90',
      user_id: this.maintenanceEventDetailsService.getEmployeeIdFromStorage().toString(),
      event_discrepancy_data: this.restructureDiscrepanciesDto(totalUpdatedDiscrepancies)
    };

    this.maintenanceEventDetailsService.updateDiscrepancies(updateDiscrepancyDto).pipe(
      concatMap(() => {
        // Wrap actual getDiscrepanciesTableData's logic in a custom Observable
        return new Observable<void>((observer) => {
          this.maintenanceEventDetailsService.getDiscrepanciesTableList(
            parseInt(this.detailsViewObj?.eventACN),
            this.detailsViewObj?.eventID
          ).subscribe({
            next: (response: OpenDiscrepanciesResponseDao) => {
              this.previousSelectedDiscrepancies = this.previousOriginalSelectedDiscrepancies = this.updatedDiscrepancies = [];

              response.linkedDiscrepancyList.forEach((row: any) => {
                row.isDowningItemPreviouslySelected = row.downingItem ? true : false;
              });

              this.eventLinkedDiscrepancies = response.linkedDiscrepancyList;

              this.previousSelectedDiscrepancies = JSON.parse(JSON.stringify([...response.linkedDiscrepancyList]));
              this.previousSelectedDiscrepancies.forEach((row: any) => { row.isModified = true; });
              this.previousOriginalSelectedDiscrepancies = JSON.parse(JSON.stringify(response.linkedDiscrepancyList));

              this.rowData = JSON.parse(JSON.stringify([...response.linkedDiscrepancyList, ...response.discrepancyList]));
              this.rowData.forEach((row: any) => {
                row.displayOpenDate = row.openDate != null ? this.formatOpenDate(row.openDate) : null;
                row.closed = row.closed === null ? false : row.closed;
                row.copyToComment = row.copyToManagerNotes = false;
              });

              this.filteredTableData = this.rowData;

              const storedFilter = this.maintenanceEventDetailsService.getDiscrepanciesFilterFromSessionStorage();
              if (storedFilter != null) {
                this.filterTableData(storedFilter);
              }

              this.cdRef.detectChanges();

              observer.next(); // Only emit after everything is done
              observer.complete();
            },
            error: (error) => {
              console.error('Error fetching discrepancies table:', error);
              observer.error(error); // Propagate the error
            }
          });
        });
      })
    ).subscribe({
      next: () => {
        this.isUpdateEnabled = false;
        this.detailsDiscrepanciesData.emit({ rowSelected: false, updateEnabled: false });
        this.toastrService.success("Successfully updated discrepancies for ACN - " + this.detailsViewObj.eventACN);
      },
      error: (error) => {
        console.error('Update or refresh failed:', error);
      }
    });
  }

  openDiscrepanciesDetailDialog() {
    const dialogRef = this.dialog.open(DiscrepanciesDetailDialogComponent, {
      width: '50%',       // Responsive width
      maxWidth: '50vw',   // Prevents it from exceeding the viewport
      disableClose: true,
      data: { rowData: this.gridApi.getSelectedRows()[0], acn: this.isAddEventSelected ? parseInt(this.acn) : parseInt(this.detailsViewObj.eventACN), eventId: this.detailsViewObj.eventID }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
      }
    });
  }

  openFilterDiscrepancyDialog() {
    const dialogRef = this.dialog.open(DiscrepanciesFilterDialogComponent);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.filterTableData(result);
      }
    });
  }

  filterTableData(result: any) {
    const selectedDiscrepanciesTypeList = this.getselectedDiscrepancyTypes(result.discrepancies);
    let filteredData = []
    if (result.selectedTimeFrame === 'all') {
      filteredData = this.rowData.filter(item => {
        return selectedDiscrepanciesTypeList.includes(item.discType != null ? item.discType.trim() : "");
      });
    } else if (result.selectedTimeFrame === 'days') {
      const today = new Date();
      const startDate = new Date();
      startDate.setDate(today.getDate() - result.daysInput); // Calculate N days before today

      filteredData = this.rowData.filter(item => {
        const openDate = new Date(item.openDate);
        return (openDate >= startDate && openDate <= today) && selectedDiscrepanciesTypeList.includes(item.discType != null ? item.discType.trim() : "");
      });
    } else if (result.selectedTimeFrame === 'timePeriod') {
      const startDate = new Date(result.fromDate);
      const endDate = new Date(result.toDate);
      endDate.setHours(23, 59, 59, 999); // Set end date to 23:59:59.999

      filteredData = this.rowData.filter(item => {
        const openDate = new Date(item.openDate);
        return (openDate >= startDate && openDate <= endDate) && selectedDiscrepanciesTypeList.includes(item.discType != null ? item.discType.trim() : "");
      });
    } else {
      this.filteredTableData = this.rowData;
    }
    return this.filteredTableData = [...this.eventLinkedDiscrepancies, ...filteredData];
  }

  onCellValueChanged(event: any): void {
    const { data, colDef, column, oldValue, newValue, node } = event;

    // Handle 'link' field changes
    if (colDef.field === 'link') {
      if (!newValue && data.downingItem) {
        data.downingItem = false;
        node.setDataValue('downingItem', false);
        node.setDataValue('copyToManagerNotes', false);
        node.setDataValue('copyToComment', false);
      }
      // Refresh only the affected row and column
      this.gridApi?.refreshCells({ rowNodes: [node], columns: ['downingItem', 'copyToManagerNotes', 'copyToComment'], force: true });
    }

    // Handle 'downingItem' field changes
    if (colDef.field === 'downingItem' && newValue) {
      // Uncheck downingItem for all other rows
      this.gridApi?.forEachNode((rowNode) => {
        if (rowNode !== node && rowNode.data.downingItem) {
          rowNode.setDataValue('downingItem', false);
        }
      });
      // Refresh the current row to reflect the change
      this.gridApi?.refreshCells({ rowNodes: [node], columns: ['downingItem'], force: true });
    }

    if (!this.isAddEventSelected) {
      const foundOrinalDowingItemObject = this.previousSelectedDiscrepancies.findIndex((row) => row.isDowningItemPreviouslySelected == true);
      // Handle discrepancy updates
      if ((column.getColId() === 'link' || column.getColId() === 'downingItem')) {
        let newDiscrepancy = JSON.parse(JSON.stringify(this.rowData.find((rowData) => rowData.ata.trim() === data.ata.trim())));
        if (!newDiscrepancy) return; // Exit if no matching discrepancy is found

        // Update previousSelectedDiscrepancies
        const prevIndex = this.previousSelectedDiscrepancies.findIndex((d) => d.ata === newDiscrepancy.ata);
        if (prevIndex !== -1) {
          newDiscrepancy = { ...this.previousSelectedDiscrepancies[prevIndex] };
          if (column.getColId() === 'link') {
            newDiscrepancy.link = newValue;
            newDiscrepancy.isModified = true;
            newDiscrepancy.isLinkModified = !newValue;
          } else if (column.getColId() === 'downingItem') {
            if (newValue) {
              newDiscrepancy.downingItem = newValue;
              newDiscrepancy.isDowningItem = newValue;
              // Reset downingItem for other discrepancies
              this.updatedDiscrepancies.forEach((d) => (d.downingItem = d.isDowningItem = false));
              this.previousSelectedDiscrepancies.forEach((d) => (d.downingItem = d.isDowningItem = false));
              foundOrinalDowingItemObject !== -1 ? this.previousSelectedDiscrepancies[foundOrinalDowingItemObject].isDowningModified = newDiscrepancy.ata != this.previousOriginalSelectedDiscrepancies[foundOrinalDowingItemObject].ata ? true : false : null;
            } else {
              newDiscrepancy.downingItem = newValue;
              newDiscrepancy.isDowningItem = newValue;
            }
          } else if (column.getColId() === 'copyToManagerNotes') {
            newDiscrepancy.copyToManagerNotes = newValue;
          } else if (column.getColId() === 'copyToComment') {
            newDiscrepancy.copyToComment = newValue;
          }
          this.previousSelectedDiscrepancies[prevIndex] = { ...newDiscrepancy };
        } else {
          // Update or add to updatedDiscrepancies
          const updatedIndex = this.updatedDiscrepancies.findIndex((d) => d.ata === newDiscrepancy.ata);
          if (updatedIndex !== -1) {
            newDiscrepancy = { ...this.updatedDiscrepancies[updatedIndex] };
            if (!newDiscrepancy.link && !newDiscrepancy.downingItem) {
              this.updatedDiscrepancies.splice(updatedIndex, 1); // Remove if both link and downingItem are false
            } else {
              if (column.getColId() === 'link') {
                newDiscrepancy.link = newValue;
                newDiscrepancy.isModified = true;
                newDiscrepancy.isLinkModified = !newValue;
              } else if (column.getColId() === 'downingItem') {
                newDiscrepancy.downingItem = newValue;
                newDiscrepancy.isDowningItem = newValue;
                if (newValue) {
                  this.updatedDiscrepancies.forEach((d) => (d.downingItem = d.isDowningItem = false));
                  this.previousSelectedDiscrepancies.forEach((d) => (d.downingItem = d.isDowningItem = false));
                  foundOrinalDowingItemObject !== -1 ? this.previousSelectedDiscrepancies[foundOrinalDowingItemObject].isDowningModified = newDiscrepancy.ata != this.previousOriginalSelectedDiscrepancies[foundOrinalDowingItemObject].ata ? true : false : null;
                }
              } else if (column.getColId() === 'copyToManagerNotes') {
                newDiscrepancy.copyToManagerNotes = newValue;
              } else if (column.getColId() === 'copyToComment') {
                newDiscrepancy.copyToComment = newValue;
              }
              this.updatedDiscrepancies[updatedIndex] = { ...newDiscrepancy };
            }
          } else {
            // Add new discrepancy
            if (column.getColId() === 'link') newDiscrepancy.isModified = true;
            if (column.getColId() === 'downingItem') newDiscrepancy.isDowningItem = newValue;
            if (column.getColId() === 'copyToManagerNotes') newDiscrepancy.copyToManagerNotes = newValue;
            if (column.getColId() === 'copyToComment') newDiscrepancy.copyToComment = newValue;
            this.updatedDiscrepancies.push({ ...newDiscrepancy });
          }
        }
        // Refresh the row to reflect changes
        this.gridApi?.refreshCells({ rowNodes: [node], force: true });
        this.updateIsUpdateEnabled();
        this.detailsDiscrepanciesData.emit({
          rowSelected: this.selectedRow != null ? true : false,
          updateEnabled: this.isUpdateEnabled,
        });
      }
    } else {
      // Handle added discrepancies
      if ((column.getColId() === 'link' || column.getColId() === 'downingItem')) {
        const foundIndex = this.addedDiscrepancies.findIndex((d) => d.ata === data.ata);
        data.isModified = true;
        data.isDowningItem = column.getColId() === 'downingItem' ? newValue : data.isDowningItem;
        if (newValue) {
          if (foundIndex === -1) {
            this.addedDiscrepancies.push({ ...data });
          } else {
            this.addedDiscrepancies[foundIndex] = { ...data };
          }
        } else {
          if (!data.link && !data.downingItem && foundIndex !== -1) {
            this.addedDiscrepancies.splice(foundIndex, 1);
          } else if (foundIndex !== -1) {
            this.addedDiscrepancies[foundIndex] = { ...data };
          }
        }
      } else if (column.getColId() === 'copyToManagerNotes') {
        const foundIndex = this.addedDiscrepancies.findIndex((d) => d.ata === data.ata);
        if (foundIndex !== -1) {
          this.addedDiscrepancies[foundIndex].copyToManagerNotes = newValue;
        } else {
          this.addedDiscrepancies.push({ ...data, copyToManagerNotes: newValue });
        }
      } else if (column.getColId() === 'copyToComment') {
        const foundIndex = this.addedDiscrepancies.findIndex((d) => d.ata === data.ata);
        if (foundIndex !== -1) {
          this.addedDiscrepancies[foundIndex].copyToComment = newValue;
        } else {
          this.addedDiscrepancies.push({ ...data, copyToComment: newValue });
        }
      }
      this.addDiscrepanciesData.emit({
        addedDiscrepancies: this.addedDiscrepancies,
        isDiscrepancySelected: this.addedDiscrepancies.length > 0 && this.addedDiscrepancies.some((d) => d.downingItem),
      });
    }
    this.gridApi?.refreshCells({ rowNodes: [node], force: true });
  }

  updateIsUpdateEnabled() {
    // Step 1: Check if any item in updatedDiscrepancies has downingItem = true
    if (this.updatedDiscrepancies.some(d => d.downingItem === true)) {
      this.isUpdateEnabled = true;
      return;
    }

    // Step 2: Compare previousSelectedDiscrepancies with previousOriginalSelectedDiscrepancies
    const areSame = JSON.stringify(this.previousSelectedDiscrepancies) === JSON.stringify(this.previousOriginalSelectedDiscrepancies);

    if (areSame) {
      this.isUpdateEnabled = false;
      return;
    }

    // Step 3: Check if any downingItem changed
    let isDowningUpdatedToTrue = false;
    let isDowningUpdatedToFalse = false;

    for (let i = 0; i < this.previousSelectedDiscrepancies.length; i++) {
      const prev = this.previousOriginalSelectedDiscrepancies.find(d => d.eventId === this.previousSelectedDiscrepancies[i].eventId);
      if (prev) {
        if (!prev.downingItem && this.previousSelectedDiscrepancies[i].downingItem) {
          isDowningUpdatedToTrue = true;
        } else if (prev.downingItem && !this.previousSelectedDiscrepancies[i].downingItem) {
          isDowningUpdatedToFalse = true;
        }
      }
    }

    // If downingItem updated to true, enable isUpdateEnabled
    if (isDowningUpdatedToTrue) {
      this.isUpdateEnabled = true;
      return;
    }

    // If downingItem updated to false, check if any downingItem = true still exists in previousSelectedDiscrepancies
    const stillHasDowningTrue = this.previousSelectedDiscrepancies.some(d => d.downingItem === true);

    this.isUpdateEnabled = stillHasDowningTrue;
  }

  restructureDiscrepanciesDto(response: any[]) {
    return response.map(item => {
      const { displayOpenDate, copyToManagerNotes, copyToComment, text, message, modified, linkModified, downingModified, downingItem, isDowningItemPreviouslySelected, ...rest } = item;
      return rest;
    });
  }

  formatOpenDate(dateString: string): string {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const dateObj = new Date(dateString);
    const day = dateObj.getDate().toString().padStart(2, '0'); // Ensures two-digit format
    const month = months[dateObj.getMonth()]; // Gets month abbreviation
    const year = dateObj.getFullYear().toString().slice(-2); // Gets last two digits of the year

    return `${day}${month}${year}`;
  }

  getselectedDiscrepancyTypes(discrepancies: any) {
    let selectedDiscrepancyTypeValues = [];
    if (discrepancies.pdis) selectedDiscrepancyTypeValues.push('pdis'.toUpperCase());
    if (discrepancies.smis) selectedDiscrepancyTypeValues.push('smis'.toUpperCase());
    if (discrepancies.lmpi) selectedDiscrepancyTypeValues.push('lmpi'.toUpperCase());
    if (discrepancies.ierr) selectedDiscrepancyTypeValues.push('ierr'.toUpperCase());
    if (discrepancies.mdis) selectedDiscrepancyTypeValues.push('mdis'.toUpperCase());
    if (discrepancies.mtsi) selectedDiscrepancyTypeValues.push('mtsi'.toUpperCase());
    if (discrepancies.ndis) selectedDiscrepancyTypeValues.push('ndis'.toUpperCase());
    return selectedDiscrepancyTypeValues;
  }

  calculateGridHeight(): void {
    const headerHeight = 60;
    const footerHeight = 60;
    const padding = 20;

    this.ngZone.run(() => {
      this.gridHeight = window.innerHeight - headerHeight - footerHeight - padding - 180;
      this.cdRef.detectChanges();
    });
  }

  adjustGridSizeSmoothly() {
    this.ngZone.runOutsideAngular(() => {
      requestAnimationFrame(() => {
        if (this.gridApi) {
          this.resizeGridColumns();
        }
      });
    });
  }

  resizeGridColumns() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
      this.gridApi.refreshHeader();
      this.gridApi.redrawRows();
      this.gridApi.sizeColumnsToFit();
    }
  }

  private refreshGrid() {
    setTimeout(() => {
      this.gridApi.setGridOption('columnDefs', this.columnDefs);
      this.gridApi.refreshHeader();
      this.gridApi.redrawRows();
      this.cdRef.detectChanges();
    }, 100);
  }

}