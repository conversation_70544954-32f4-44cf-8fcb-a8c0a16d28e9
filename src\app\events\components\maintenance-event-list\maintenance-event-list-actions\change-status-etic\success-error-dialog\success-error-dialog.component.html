<div class="outer-container" [ngClass]="{'success-bg': isSuccess, 'error-bg': !isSuccess}">
  <div class="dialog-container">
    <div class="zoom-container">
      <mat-dialog-content class="dialog-content">
        <div class="message-container">
          <div class="icon-wrapper" [ngClass]="{'success-icon': isSuccess, 'error-icon': !isSuccess}">
            <svg *ngIf="isSuccess" class="custom-icon" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
              <circle cx="12" cy="12" r="12" fill="none" stroke="currentColor" stroke-width="0.5" class="starburst"/>
            </svg>
            <svg *ngIf="!isSuccess" class="custom-icon" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" fill="currentColor"/>
              <path d="M12 3l1.5 4.5 4.5 0-3.5 2.5 1.5 4.5-3.5-2.5-3.5 2.5 1.5-4.5-3.5-2.5 4.5 0z" fill="none" stroke="currentColor" stroke-width="0.5" class="starburst"/>
            </svg>
            <svg *ngIf="isSuccess" class="timer-ring" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="45" fill="none" stroke="#27ae60" stroke-width="6" stroke-dasharray="283" stroke-dashoffset="0">
                <animate attributeName="stroke-dashoffset" from="0" to="283" dur="3s" fill="freeze"/>
              </circle>
            </svg>
          </div>
          <p class="message-text" [ngClass]="{'success-text': isSuccess, 'error-text': !isSuccess}" [innerHTML]="formattedMessage"></p>
        </div>
      </mat-dialog-content>

      <mat-dialog-actions *ngIf="!isSuccess" class="dialog-actions">
        <button mat-raised-button class="action-button" (click)="onClose()">OK</button>
      </mat-dialog-actions>
    </div>
  </div>
</div>