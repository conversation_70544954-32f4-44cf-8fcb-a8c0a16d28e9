// src/app/app.module.ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CommonModule } from '@angular/common';
import { OktaAuthModule } from '@okta/okta-angular';
import { ToastrModule } from 'ngx-toastr';
import { ComponentsModule } from './shared/components.module';
import { MaterialModule } from './shared/material.module';
import { CoreModule } from './shared/core.module';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    CommonModule,
    OktaAuthModule,
    ToastrModule.forRoot({
      timeOut: 2500,
      closeButton: true,
      progressBar: true,
      progressAnimation: 'increasing',
      positionClass: 'toast-top-right',  // Change as per your need
      preventDuplicates: true,
    }),
    AppRoutingModule,
    ComponentsModule,
    MaterialModule,
    CoreModule,
    FormsModule,
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}