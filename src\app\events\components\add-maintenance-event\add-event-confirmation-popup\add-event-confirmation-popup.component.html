<div *ngIf="data.event != 'NA'" class="dialog-content">
    <div class="inner-container">
        <div class="header">
            <span>Active Events Requiring Action</span>
        </div>
        <div class="content">
            <span class="error-message">{{data.message['ERROR']}}</span>
            <div class="table-container">
                <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 mat-table-custom">
                    <ng-container matColumnDef="type">
                        <th mat-header-cell *matHeaderCellDef> Type </th>
                        <td mat-cell *matCellDef="let element"> {{element.type}} </td>
                    </ng-container>
                    <ng-container matColumnDef="station">
                        <th mat-header-cell *matHeaderCellDef> Station </th>
                        <td mat-cell *matCellDef="let element"> {{element.station}} </td>
                    </ng-container>
                    <ng-container matColumnDef="start">
                        <th mat-header-cell *matHeaderCellDef> Start </th>
                        <td mat-cell *matCellDef="let element"> {{element.start}} </td>
                    </ng-container>
                    <ng-container matColumnDef="status">
                        <th mat-header-cell *matHeaderCellDef> Status </th>
                        <td mat-cell *matCellDef="let element"> {{element.status}} </td>
                    </ng-container>
                    <ng-container matColumnDef="comment">
                        <th mat-header-cell *matHeaderCellDef> Comment </th>
                        <td mat-cell *matCellDef="let element"> {{element.comment}} </td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
            </div>
        </div>
        <div *ngIf="yesOrNo" class="button-container">
            <button class="button-style yes-button" mat-raised-button (click)="handleButton('YES')">Yes</button>
            <button class="button-style no-button" mat-raised-button (click)="handleButton('NO')">No</button>
        </div>
        <div *ngIf="showOptions" class="button-container">
            <button class="button-style option-button" mat-raised-button (click)="handleButton('CONTINUE')">Continue to Add Event</button>
            <button class="button-style option-button" mat-raised-button (click)="handleButton('CONVERT')">Convert to {{data.event}} Event</button>
        </div>
        <div *ngIf="returnBack" class="button-container">
            <button class="button-style option-button" mat-raised-button (click)="handleButton('OK')">OK</button>
        </div>
    </div>
</div>

<div *ngIf="data.event == 'NA'" class="dialog-content" [ngClass]="{'success-bg': isEventCreated, 'error-bg': !isEventCreated}">
    <div class="inner-container zoom-container">
        <div class="content">
            <div class="animation-container">
                <div *ngIf="isEventCreated" class="success-animation">
                    <div class="icon-wrapper success-icon">
                        <svg class="custom-icon" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                            <circle cx="12" cy="12" r="12" fill="none" stroke="currentColor" stroke-width="0.5" class="starburst"/>
                        </svg>
                    </div>
                    <span class="success-message">Successfully created new event for ACN - {{acnValue}}</span>
                </div>
                <div *ngIf="!isEventCreated" class="error-animation">
                    <div class="icon-wrapper error-icon">
                        <svg class="custom-icon" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" fill="currentColor"/>
                            <path d="M12 3l1.5 4.5 4.5 0-3.5 2.5 1.5 4.5-3.5-2.5-3.5 2.5 1.5-4.5-3.5-2.5 4.5 0z" fill="none" stroke="currentColor" stroke-width="0.5" class="starburst"/>
                        </svg>
                    </div>
                    <span class="error-message">{{creationResponse}}</span>
                </div>
            </div>
            <div class="button-container or-container">
                <button class="button-style option-button" mat-raised-button (click)="handleButton('LIST')" [ngClass]="{'success-button-width': isEventCreated}">List View</button>
                <div class="or-divider">
                    <hr class="custom-divider vertical"/>
                    <span class="or-text">or</span>
                    <hr class="custom-divider vertical"/>
                </div>
                <button class="button-style option-button" mat-raised-button (click)="handleButton('DETAILS')" [ngClass]="{'success-button-width': isEventCreated}">Details ACN - {{acnValue}}</button>
            </div>
            <hr class="custom-divider"/>
            <div class="button-container">
                <button class="button-style option-button" mat-raised-button (click)="handleButton('ADD_ANOTHER')" [ngClass]="{'success-button-width': isEventCreated}">Add Another Event</button>
            </div>
        </div>
    </div>
</div>