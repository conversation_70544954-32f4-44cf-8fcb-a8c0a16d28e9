import { ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit, PLATFORM_ID, Renderer2 } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from '../../services/spinner.service';
import { isPlatformBrowser } from '@angular/common';

@Component({
    selector: 'app-spinner',
    templateUrl: './spinner.component.html',
    styleUrl: './spinner.component.scss',
    standalone: false
})
export class SpinnerComponent implements OnInit, OnDestroy {

  isLoading$ = this.spinnerService.spinnerSubjectObservable$;
  private isLoadingSubscription!: Subscription;

  constructor(
    private spinnerService: SpinnerService,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef
  ) {}
  
  ngOnInit(): void {
    this.isLoadingSubscription = this.isLoading$.subscribe((isLoading: any) => {
      setTimeout(() => {
        if (isLoading) {
          this.renderer.addClass(document.body, 'spinner-active');
        } else {
          this.renderer.removeClass(document.body, 'spinner-active');
        }
        this.cdr.markForCheck();
        this.cdr.detectChanges();
      }, 0);
    });
  }

  ngOnDestroy(): void {
    if (this.isLoadingSubscription) {
      this.isLoadingSubscription.unsubscribe();
    }
  }
}