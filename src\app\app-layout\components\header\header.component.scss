.header-container {
  background-color: white;
  padding: 5% 3%;
  color: black;
  display: flex;
  gap: 10px;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.5s ease-in-out;
}

.closed-header-container {
  gap: 2px !important;
  transition: all 0.5s ease-in-out;
  justify-content: space-evenly;
}

.header {
  display: flex;
  width: 75% !important;
  padding: 20px 0;
  justify-content: center;
  align-items: center;
  text-align: center;
  place-self: center;
  border-radius: 10px;
}

.boxshadowEnabled {
  transition: all 0.5s ease-in-out;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}

.diableBoxshadow {
  padding: 10px 0 !important;
  transition: all 0.5s ease-in-out;
  box-shadow: none;
}

.menuIcon {
  padding-right: 1.35%;
}

.divider-class {
  border: 1px solid whitesmoke;
  height: 5vh;
}

.sideNavOpen {
  transition: all 0.6s ease-in-out;
height: 4vh; /* Adjust logo height */
}
.sideNavClosed {
  transition: all 0.6s ease-in-out;
  height: 2vh;
  margin: 10px 0;
}

.mat-list-item {
  color: black;
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: center;
  height: 50px !important;
  border-radius: 10px !important;
  transition: all 0.5s ease, background-color 0.8s ease, opacity 0.5s ease-in-out;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  opacity: 1 !important;
  
  mat-icon {
    font-size: 24px;
  }
}

.username-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 50px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  border-radius: 10px;
  padding: 20px 10px;
  transition: all 0.6s ease-in-out;
}

.user-sidenav-inner {
  display: flex;
  height: fit-content;
  transition: all 0.5s ease-in-out;
}

// ::ng-deep .popover {
//   max-width: unset !important;
//   width: max-content !important;
//   min-width: 220px;
//   padding: 12px 16px;
//   z-index: 9999 !important;
//   border-radius: 10px;
//   white-space: nowrap;
//   box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
//   background-color: #ffffff;
//   border: 1px solid #e4e4e4;
// }

// .popover-user-card {
//   display: flex;
//   align-items: center;
//   gap: 10px;
// }

// .user-icon-section {
//   border-radius: 50%;
//   padding: 6px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// .user-avatar-icon {
//   font-size: 20px;
//   color: #0d6efd;
// }

// .user-details-section {
//   display: flex;
//   flex-direction: column;
// }

// .user-label {
//   font-size: 11px;
//   color: #6c757d;
//   letter-spacing: 0.5px;
//   margin-bottom: 2px;
// }

// .user-name {
//   font-size: 15px;
//   font-weight: 600;
//   color: #212529;
// }


// ::ng-deep .popover {
//   max-width: unset !important;
//   width: max-content !important;
//   min-width: 240px;
//   z-index: 9999 !important;
//   padding: 16px 20px;
//   border-radius: 12px;
//   background: #ffffff;
//   box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.15);
//   white-space: nowrap;
//   font-family: 'Inter', 'Segoe UI', sans-serif;
// }

::ng-deep .user-info-popover.popover {
  max-width: unset !important;
  width: max-content !important;
  min-width: 240px;
  z-index: 9999 !important;
  padding: 16px 20px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.popover-profile-card {
  display: flex;
  align-items: center;
  gap: 14px;
}

.profile-avatar-section {
  background: #f0f4ff;
  width: fit-content;
  border-radius: 50%;
  padding: 8px;
}

.profile-avatar-icon {
  width: fit-content;
  font-size: 36px;
  color: #6c49b9;
}

.profile-details {
  display: flex;
  flex-direction: column;
}

.profile-name {
  font-size: 16px;
  font-weight: bold;
  color: #6c49b9;
}

.profile-role {
  font-size: 12px;
  color: #ff6600;
  font-weight: bold;
  margin-top: 2px;
}





.user-section {
  width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #7f7b7d;
}

.user-icon-section {
  flex-shrink: 0;
  width: fit-content;
  height: fit-content;
  align-content: center;
  text-align: center;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}

.user-icon-closed-section {
  width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: #7f7b7d;
}

.menuIcon {
  width: fit-content;
  font-size: 28px;
  color: black;
}

.user-name-section {
  display: flex;
  flex-direction: column;
  min-width: 0;
  text-align: center;
  align-items: center;
}

.userName {
  font-size: 14px;
  font-weight: bold;
  color: black;
  word-break: break-word;
  white-space: normal;
  line-height: 1.2;

  display: -webkit-box;
  -webkit-line-clamp: 2;  /* Limit to 2 lines */
  line-clamp: 2;          /* Standard property for compatibility */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}