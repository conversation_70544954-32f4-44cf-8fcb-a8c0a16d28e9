import { Component, Input, OnInit, inject, ChangeDetectorRef, SimpleChanges, Output, EventEmitter, AfterViewInit, ViewChild, ViewChildren, ElementRef, QueryList } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatStepper } from '@angular/material/stepper';
import { MatDialog } from '@angular/material/dialog';
import { map, startWith, distinctUntilChanged, filter } from 'rxjs/operators';
import { interval, Subscription } from 'rxjs';
import { MainService } from '../../../app-layout/services/main.service';
import { AddEventData } from '../../dao/addEvent-Data';
import { AddEventService } from '../../services/add-event.service';
import { AddEventConfirmationPopupComponent } from './add-event-confirmation-popup/add-event-confirmation-popup.component';
import { AppLayoutService } from '../../../app-layout/services/app-layout.service';
import { MatSelectChange } from '@angular/material/select';
import { Router } from '@angular/router';
import { DateAdapter } from '@angular/material/core';
import { NiwTimerResponse, TimerData } from '../../dao/niwTimerDao';
import { MaintenanceEventDetailsService } from '../../services/maintenance-event-details.service';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { AddEventRequestDao } from '../../dao/addEventDao';
import { UserDto } from '../../dto/UserInfo';
import { DatePipe } from '@angular/common';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { NIWTimerAbbreviations } from '../../constants/niwTimerAbbreviations';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
  selector: 'app-add-maintenance-event',
  standalone: false,
  templateUrl: './add-maintenance-event.component.html',
  styleUrl: './add-maintenance-event.component.scss',
  providers: [provideNativeDateAdapter()]
})
export class AddMaintenanceEventComponent implements OnInit, AfterViewInit{

  @Output() dataFilled = new EventEmitter<any>();
  @Input() acn: string = "";
  @Input() eventtype: string = "";

  @ViewChild('stepper') stepper!: MatStepper;
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  @ViewChildren('sectionRef') sections!: QueryList<ElementRef>;

  private observer!: IntersectionObserver;
  private timeUpdateSubscription?: Subscription;
  private commentValueSubscription?: Subscription;
  private tfNoteValueSubscription?: Subscription;
  private respMgrSubscription?: Subscription;
  activeStep = 0;
  isScrollingProgrammatically = false;
  sideNavClosed: boolean | null = false;
  specifyDetailForm!: FormGroup;
  niwTimerForm: FormGroup;
  selectedMsnData: any;
  selectedDiscrepancyData: any[] = [];
  selectedDiscText: string[] = [];
  isIntakeFormFullFilled: boolean = false;
  intakeFormRespnse: any;
  reportingCategoriesMap: any = {};
  reportingCategoriesActual: any;
  isDiscrepancySelected: boolean = false;
  isMsnStepVisited: boolean = false;
  now = new Date();
  aircraftType: string = "Loading...";
  superCommentExt: string[] = ['ATB', 'AHM', 'BTB', 'DMG', 'DOA', 'EAP', 'EMER', 'MX-DIV', 'RTO', 'WX-DIV'];
  selectedCommentTag: string = '';
  newTubFileNote: string = '';
  niwTableColumns: string[] = ['timerName'];
  niwTimerResponse!: NiwTimerResponse;
  niwTimerOptions: string[] = [];
  selectedNiwTimer: string = '';
  minEticDate: Date = new Date();
  startHours: string[] = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  startMinutes: string[] = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  filteredEticHours: string[] = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  filteredEticMinutes: string[] = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  managerList: string[] = [];
  filteredManagers: string[] = [];
  private addedDiscrepancyTexts: Set<string> = new Set();

  readonly dialog = inject(MatDialog);

  utcNow = new Date(
    Date.UTC(
      this.now.getUTCFullYear(),
      this.now.getUTCMonth(),
      this.now.getUTCDate(),
      this.now.getUTCHours(),
      this.now.getUTCMinutes(),
      this.now.getUTCSeconds(),
      0
    )
  );

  stationsList: string[] = [];
  statusList: string[] = AddEventData.eventStatus;
  excludedList = ['TRK', 'INF'];
  aofList: string[] = [];
  aofGmADMap: any = {};
  ownerList: string[] = AddEventData.owners;
  eticTypeList: string[] = ['FIRM', 'WA', 'Not In Work (NIW)'];
  filteredEticTypeList: string[] = ['FIRM', 'WA', 'Not In Work (NIW)'];
  timeUnitList: string[] = ['Hours', 'Days'];
  niwTimersList: string[] = [];
  filteredNiwTimersList: string[] = [];

  aofRequestPayload: any = {
    "mode": "FLIGHT_LEG_DETAILS",
    "userId": "472810",
    "acn": ""
  };

  mgrRequestPayload: any = {
    "mode": "MGR_DETAILS",
    "userId": "5945348",
    "tokenId": "1234567",
    "station": ""
  };

  stepCompletion: boolean[] = [true, true, false, false, false, false, false, false];

  constructor(
    private fb: FormBuilder,
    private addEventService: AddEventService,
    private appLayoutService: AppLayoutService,
    private router: Router,
    private dateAdapter: DateAdapter<Date>,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private cdr: ChangeDetectorRef
  ) {
    this.dateAdapter.setLocale('en-US');
    const sideNavClosedValue = this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage();
    this.appLayoutService.setSideNavClosedFromPreferences(sideNavClosedValue != null ? sideNavClosedValue?.sideNavClosed : false);
    this.niwTimerForm = this.fb.group({
      niwTimer: ['', this.eventtype === 'OOS' ? Validators.required : null]
    });
  }

  ngOnInit() {
    this.initializeComponent();
    this.appLayoutService.sideNavClosedObservable$.subscribe((isClosed) => {
      this.sideNavClosed = isClosed;
    });
    this.startTimeValidation();

    const currentStatus = this.specifyDetailForm.get('status')?.value;
    this.filterEticTypeList(currentStatus || '');

    this.niwTimersList = NIWTimerAbbreviations.getAllTimerNames();

    this.filterNiwTimersList(currentStatus || '');
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['acn'] || changes['eventtype']) {
      this.initializeComponent();
    }
  }

  ngAfterViewInit(): void {
    const options = {
      root: this.scrollContainer.nativeElement,
      threshold: 0.1,
      rootMargin: '0px'
    };

    this.observer = new IntersectionObserver((entries) => {
      if (this.isScrollingProgrammatically) return;

      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const index = this.sections.toArray().findIndex(sec => sec.nativeElement === entry.target);
          if (index !== -1 && this.activeStep !== index) {
            this.activeStep = index;
            if (index === 4 && this.eventtype !== 'NOTE') this.isMsnStepVisited = true;
            this.stepper.selectedIndex = index;
            this.cdr.detectChanges();
          }
        }
      });
    }, options);

    this.sections.changes.subscribe(() => {
      this.observeSections();
    });

    this.observeSections();
  }

  private observeSections() {
    this.observer.disconnect();
    this.sections.forEach(section => this.observer.observe(section.nativeElement));
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.timeUpdateSubscription) {
      this.timeUpdateSubscription.unsubscribe();
    }
    if (this.commentValueSubscription) {
      this.commentValueSubscription.unsubscribe();
    }
    if (this.tfNoteValueSubscription) {
      this.tfNoteValueSubscription.unsubscribe();
    }
    if (this.respMgrSubscription) {
      this.respMgrSubscription.unsubscribe();
    }
  }

  initializeComponent() {
    this.specifyDetailForm = this.fb.group({
      station: [null, this.eventtype !== 'NOTE' ? Validators.required : null],
      startDate: [{value: this.utcNow, disabled: true}, Validators.required],
      startHour: [{value: this.formatTime(this.utcNow).split(':')[0], disabled: true}, Validators.required],
      startMinute: [{value: this.formatTime(this.utcNow).split(':')[1], disabled: true}, Validators.required],
      status: [null, this.eventtype !== 'DOA' ? Validators.required : null],
      eticStartDate: [this.getDefaultEticDate(), this.eventtype === 'OOS' ? Validators.required : null],
      eticStartTime: [this.getDefaultEticTime(), this.eventtype === 'OOS' ? Validators.required : null],
      eticStartHour: [this.getDefaultEticTime().split(':')[0], this.eventtype === 'OOS' ? Validators.required : null],
      eticStartMinute: [this.getDefaultEticTime().split(':')[1], this.eventtype === 'OOS' ? Validators.required : null],
      info: [''],
      ost: [false],
      comment: ['', this.eventtype !== 'DOA' ? [Validators.required, Validators.maxLength(100)] : null],
      aof: [null, this.eventtype !== 'NOTE' && this.eventtype !== 'DOA' ? Validators.required : null],
      currentFlightArrivalTime: [''],
      owner: [null],
      contact: [''],
      respMgr: [''],
      memDesk: [''],
      tfNote: ['', Validators.required],
      selectedCommentTag: [''],
      eticttype: [null, this.eventtype === 'OOS' ? Validators.required : null],
      eticNiwReason: [null, (this.eventtype === 'OOS' && (this.specifyDetailForm.get('eticttype')?.value == 'FIRM' || this.specifyDetailForm.get('eticttype')?.value == 'WA')) ? Validators.required : null],
      timeNeeded: [0, (this.eventtype === 'OOS' && (this.specifyDetailForm.get('eticttype')?.value == 'FIRM' || this.specifyDetailForm.get('eticttype')?.value == 'WA')) ? Validators.required : null],
      timeUnits: [null, (this.eventtype === 'OOS' && (this.specifyDetailForm.get('eticttype')?.value == 'FIRM' || this.specifyDetailForm.get('eticttype')?.value == 'WA')) ? Validators.required : null],
    });

    this.filteredManagers = [...this.managerList];

    this.commentValueSubscription = this.specifyDetailForm.get('comment')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      if (value && typeof value === 'string' && value !== value.toUpperCase()) {
        this.specifyDetailForm.get('comment')?.setValue(value.toUpperCase(), { emitEvent: false });
      }
    });

    this.tfNoteValueSubscription = this.specifyDetailForm.get('tfNote')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      if (value && typeof value === 'string' && value !== value.toLowerCase()) {
        this.specifyDetailForm.get('tfNote')?.setValue(value.toLowerCase(), { emitEvent: false });
      }
    });

    this.respMgrSubscription = this.specifyDetailForm.get('respMgr')?.valueChanges.pipe(
      startWith(''),
      map(value => this._filterManagers(value || ''))
    ).subscribe(filtered => {
      this.filteredManagers = filtered;
      this.cdr.detectChanges();
    });

    if (this.eventtype === 'TRK' || this.eventtype === 'NOTE') {
      const val = this.eventtype === 'TRK' ? 'TRK' : 'INF';
      this.specifyDetailForm.patchValue({ status: val });
      this.specifyDetailForm.get('status')?.disable();
    }

    this.statusList = this.statusList.filter(status => !this.excludedList.includes(status));
    this.updateEticTimeOptions();
    this.updateValidators();
    this.validateFormForSave();
    this.getAffectedOutboundFlightList();
    this.getAircraftType();
    this.getAogNiwTimers();

    this.specifyDetailForm.get('station')?.valueChanges.subscribe(value => this.onStationChange(value));
    this.specifyDetailForm.get('selectedCommentTag')?.valueChanges.subscribe(value => this.updateCommentTag(value));
    this.specifyDetailForm.get('status')?.valueChanges.subscribe(value => {
      if (value === 'AOG') {
        this.niwTimerForm.get('niwTimer')?.setValidators(Validators.required);
      } else {
        this.niwTimerForm.get('niwTimer')?.clearValidators();
        this.selectedNiwTimer = '';
        this.niwTimerOptions = [];
      }
      this.niwTimerForm.get('niwTimer')?.updateValueAndValidity();

      this.filterEticTypeList(value);

      this.filterNiwTimersList(value);

      this.validateFormForSave();
    });
    this.specifyDetailForm.get('eticStartDate')?.valueChanges.subscribe(() => this.onEticDateChange());
    this.specifyDetailForm.get('eticStartHour')?.valueChanges.subscribe(() => this.onEticTimeChange());
    this.specifyDetailForm.get('eticStartMinute')?.valueChanges.subscribe(() => this.onEticTimeChange());

    this.specifyDetailForm.valueChanges.subscribe(() => {
      this.validateFormForSave();
    });

    this.specifyDetailForm.get('eticttype')?.valueChanges.subscribe(value => this.onEticTypeChange(value));
    this.onEticTypeChange(this.specifyDetailForm.get('eticttype')?.value);

    // Initial sync of time field
    this.syncEticTimeField();
  }

  textCaseValidator(form: FormGroup) {
    return null;
  }

  startTimeValidation(): void {
    const now = new Date();
    const msUntilNextUtcMinute = 60000 - (now.getUTCSeconds() * 1000 + now.getUTCMilliseconds());

    setTimeout(() => {
      this.updateCurrentTime();
      this.updateEticTimeOptions();
      this.timeUpdateSubscription = interval(60000).subscribe(() => {
        this.updateCurrentTime();
        this.updateEticTimeOptions();
      });
    }, msUntilNextUtcMinute);
  }

  updateCurrentTime() {
    if (!this.specifyDetailForm.get('startDate')?.disabled) return;

    this.utcNow = this.currentUTCTimeNow();
    this.specifyDetailForm.patchValue({
      startDate: this.utcNow,
      startHour: this.formatTime(this.utcNow).split(':')[0],
      startMinute: this.formatTime(this.utcNow).split(':')[1]
    }, { emitEvent: false });
    this.validateFormForSave();
    this.cdr.detectChanges();
  }

  getDefaultEticDate(): Date {
    const now = this.currentUTCTimeNow();
    now.setUTCMinutes(now.getUTCMinutes() + 5);
    return new Date(
      Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate()
      )
    );
  }

  getDefaultEticTime(): string {
    const now = this.currentUTCTimeNow();
    now.setUTCMinutes(now.getUTCMinutes() + 5);
    return this.formatTime(now);
  }

  validateAndUpdateEticTime() {
    if (this.eventtype !== 'OOS') return;

    // Skip validation if updating from time input to prevent conflicts
    if (this.isUpdatingFromTimeInput) return;

    const eticDateControl = this.specifyDetailForm.get('eticStartDate');
    const eticHourControl = this.specifyDetailForm.get('eticStartHour');
    const eticMinuteControl = this.specifyDetailForm.get('eticStartMinute');

    if (!eticDateControl || !eticHourControl || !eticMinuteControl) return;

    const selectedDate = new Date(eticDateControl.value || this.getDefaultEticDate());
    const selectedHour = parseInt(eticHourControl.value || '0', 10);
    const selectedMinute = parseInt(eticMinuteControl.value || '0', 10);

    const selectedDateTime = new Date(Date.UTC(
      selectedDate.getUTCFullYear(),
      selectedDate.getUTCMonth(),
      selectedDate.getUTCDate(),
      selectedHour,
      selectedMinute,
      0 // Reset seconds to avoid precision issues
    ));

    const now = this.currentUTCTimeNow();
    const minValidTime = new Date(now);
    minValidTime.setUTCSeconds(0); // Reset seconds to avoid precision issues
    // Allow times that are at least current time (no 5-minute buffer for user input)

    if (selectedDateTime < minValidTime) {
      const defaultTime = this.getDefaultEticTime();
      this.specifyDetailForm.patchValue({
        eticStartDate: this.getDefaultEticDate(),
        eticStartTime: defaultTime,
        eticStartHour: defaultTime.split(':')[0],
        eticStartMinute: defaultTime.split(':')[1]
      }, { emitEvent: false });
      this.syncEticTimeField();
    }

    this.updateEticTimeOptions();
    this.validateFormForSave();
  }

  updateEticTimeOptions(): void {
    if (this.eventtype !== 'OOS') return;

    const now = this.currentUTCTimeNow();
    this.minEticDate = new Date(
      Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate()
      )
    );

    const eticDateControl = this.specifyDetailForm.get('eticStartDate');
    const selectedDate = eticDateControl && eticDateControl.value ? new Date(eticDateControl.value) : this.minEticDate;
    const isToday = this.isSameDayUTC(selectedDate, now);

    this.filteredEticHours = isToday
      ? this.startHours.filter(h => parseInt(h, 10) >= now.getUTCHours())
      : [...this.startHours];

    const selectedHour = this.specifyDetailForm.get('eticStartHour')?.value;
    this.filteredEticMinutes = isToday && selectedHour && parseInt(selectedHour, 10) === now.getUTCHours()
      ? this.startMinutes.filter(m => parseInt(m, 10) >= now.getUTCMinutes())
      : [...this.startMinutes];

    const currentHour = parseInt(this.specifyDetailForm.get('eticStartHour')?.value || '0', 10);
    const currentMinute = parseInt(this.specifyDetailForm.get('eticStartMinute')?.value || '0', 10);
    if (isToday && (currentHour < now.getUTCHours() ||
        (currentHour === now.getUTCHours() && currentMinute < now.getUTCMinutes()))) {
      const defaultTime = this.getDefaultEticTime();
      this.specifyDetailForm.patchValue({
        eticStartTime: defaultTime,
        eticStartHour: defaultTime.split(':')[0],
        eticStartMinute: defaultTime.split(':')[1]
      }, { emitEvent: false });
      this.syncEticTimeField();
    }
  }

  onEticDateChange() {
    this.validateAndUpdateEticTime();
  }

  private isUpdatingFromTimeInput = false;

  onEticTimeChange() {
    this.syncEticTimeField();
    // Only validate if not updating from time input to avoid conflicts
    if (!this.isUpdatingFromTimeInput) {
      this.validateAndUpdateEticTime();
    }
  }

  onEticTimeInputChange() {
    const timeValue = this.specifyDetailForm.get('eticStartTime')?.value;
    if (timeValue) {
      const [hour, minute] = timeValue.split(':');

      // Set flag to prevent validation conflicts
      this.isUpdatingFromTimeInput = true;

      // Update the form controls without triggering events to avoid conflicts
      this.specifyDetailForm.get('eticStartHour')?.setValue(hour, { emitEvent: false });
      this.specifyDetailForm.get('eticStartMinute')?.setValue(minute, { emitEvent: false });

      // Reset flag immediately
      this.isUpdatingFromTimeInput = false;
    }
  }

  syncEticTimeField() {
    const hour = this.specifyDetailForm.get('eticStartHour')?.value;
    const minute = this.specifyDetailForm.get('eticStartMinute')?.value;
    if (hour && minute) {
      const timeString = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
      this.specifyDetailForm.get('eticStartTime')?.setValue(timeString, { emitEvent: false });
    }
  }

  preventNegativeInput(event: KeyboardEvent) {
    // Prevent minus sign, plus sign, and 'e' (scientific notation)
    if (event.key === '-' || event.key === '+' || event.key === 'e' || event.key === 'E') {
      event.preventDefault();
    }
  }

  onTimeNeededInput(event: any) {
    const value = event.target.value;
    if (value < 0) {
      // If negative value is entered, set it to 0
      this.specifyDetailForm.get('timeNeeded')?.setValue(0);
    }
  }

  validateFormForSave() {
    const step1Valid = this.specifyDetailForm.get('station')?.valid &&
                      this.specifyDetailForm.get('startDate')?.valid &&
                      this.specifyDetailForm.get('startHour')?.valid &&
                      this.specifyDetailForm.get('startMinute')?.valid &&
                      this.specifyDetailForm.get('status')?.valid &&
                      this.specifyDetailForm.get('aof')?.valid;
    const step4Valid = this.specifyDetailForm.get('comment')?.valid &&
                       this.specifyDetailForm.get('tfNote')?.valid;
    const step5Valid = this.eventtype !== 'NOTE' ? this.isMsnStepVisited && this.selectedMsnData?.length > 0 : true;

    const isValid = this.specifyDetailForm.valid &&
                    (this.eventtype !== 'NOTE' ? this.isIntakeFormFullFilled : true) &&
                    (this.eventtype !== 'NOTE' ? (!this.isDiscrepancySelected && this.selectedDiscrepancyData.length === 0 || this.isDiscrepancySelected && this.selectedDiscrepancyData?.length > 0) : true) &&
                    (this.specifyDetailForm.get('status')?.value === 'AOG' && this.eventtype !== 'NOTE' ? !!this.selectedNiwTimer : true);
    this.dataFilled.emit(isValid);
  }

  onManagerSelect(event: MatAutocompleteSelectedEvent): void {
    const selectedManager = event.option.value;
    this.specifyDetailForm.get('respMgr')?.setValue(selectedManager, { emitEvent: false });
    this.filteredManagers = [...this.managerList];
    this.cdr.detectChanges();
  }

  getAogNiwTimers(): void {
    this.maintenanceEventDetailsService.getAogNiwTimers(parseInt(this.acn)).subscribe({
      next: (response: NiwTimerResponse) => {
        this.niwTimerResponse = response;
        this.niwTimerOptions = response.timerDataList
          .filter((timer: TimerData) => timer.timerName.startsWith('Parts'))
          .map((timer: TimerData) => timer.timerName);
        this.validateFormForSave();
      },
      error: (err) => {
        console.error('Error fetching NIW timers:', err);
        this.niwTimerOptions = [];
        this.validateFormForSave();
      }
    });
  }

  selectNiwTimer(timer: string): void {
    this.selectedNiwTimer = timer;
    this.niwTimerForm.get('niwTimer')?.setValue(timer);
    this.validateFormForSave();
  }

  updateCommentTag(value: string) {
    if (!value) {
      this.selectedCommentTag = '';
      let currentComment = this.specifyDetailForm.get('comment')?.value?.toUpperCase() || '';
      // Remove any existing ETA or (CONF CALL) suffix
      const eta = this.specifyDetailForm.get('currentFlightArrivalTime')?.value || '';
      currentComment = currentComment.replace(/\(CONF CALL\)$/, '').replace(new RegExp(`${this.escapeRegExp(eta)}$`), '').trim();
      this.specifyDetailForm.get('comment')?.setValue(currentComment, { emitEvent: false });
      this.validateFormForSave();
      return;
    }

    let currentComment = this.specifyDetailForm.get('comment')?.value?.toUpperCase() || '';
    let newComment = '';
    const tagPrefix = `${value} - `;

    // Remove previous tag and any suffixes if they exist
    if (this.selectedCommentTag && currentComment.startsWith(this.selectedCommentTag)) {
      currentComment = currentComment.substring(this.selectedCommentTag.length).trim();
    }
    // Remove any existing ETA or (CONF CALL) suffix
    const eta = this.specifyDetailForm.get('currentFlightArrivalTime')?.value || '';
    currentComment = currentComment.replace(/\(CONF CALL\)$/, '').replace(new RegExp(`${this.escapeRegExp(eta)}$`), '').trim();

    if (['ATB', 'DMG', 'EMER', 'MX-DIV'].includes(value)) {
      newComment = `${tagPrefix}${currentComment} (CONF CALL)`;
    } else if (['AHM', 'BTB', 'RTO', 'WX-DIV'].includes(value)) {
      newComment = `${tagPrefix}${currentComment}`;
    } else if (['DOA', 'EAP'].includes(value)) {
      const eta = this.specifyDetailForm.get('currentFlightArrivalTime')?.value || '';
      newComment = `${tagPrefix}${currentComment} ${eta}`;
    } else {
      newComment = `${tagPrefix}${currentComment}`;
    }

    this.selectedCommentTag = tagPrefix;
    this.specifyDetailForm.get('comment')?.setValue(newComment.trim().toUpperCase(), { emitEvent: false });
    this.validateFormForSave();
  }

  getAircraftType() {
    this.addEventService.getAircraftType().subscribe({
      next: (result: any) => {
        if (result && result.ACN_CACHE_DETAIL && result.ACN_CACHE_DETAIL.length > 0) {
          const acnEntry = result.ACN_CACHE_DETAIL.find((entry: any) => entry.acn == this.acn);
          this.aircraftType = acnEntry?.fleetCode || "NA";
        } else {
          this.aircraftType = "NA";
        }
        this.validateFormForSave();
      },
      error: (err: any) => {
        console.error('Error fetching aircraft type:', err);
        this.aircraftType = "NA";
        this.validateFormForSave();
      }
    });
  }

  setupManagerAutocomplete() {
    this.filteredManagers = [...this.managerList];
  }

  private _filterManagers(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.managerList.filter(manager => manager.toLowerCase().includes(filterValue));
  }

  onStationChange(value: string) {
    this.managerList = [];
    let mgrPayload = { ...this.mgrRequestPayload };
    mgrPayload['station'] = value;
    if (mgrPayload['station']) {
      this.addEventService.getManagerDetails(value).subscribe({
        next: (result2: any) => {
          this.managerList = result2.map((j: any) => `${j.empFirstName} ${j.empLastName} (${j.respEmpnum})`);
          this.managerList = [...new Set(this.managerList)];
          this.setupManagerAutocomplete();
          this.validateFormForSave();
        },
        error: (err) => {
          console.error('Error fetching manager details:', err);
          this.managerList = [];
          this.setupManagerAutocomplete();
          this.validateFormForSave();
        }
      });
    }
  }

  onStatusChange(event: MatSelectChange) {
    this.validateFormForSave();
  }

  filterEticTypeList(status: string): void {
    const allowAllEticTypes = ['AOG', 'DWN', 'HMX', 'HMD', 'HMO'];

    if (allowAllEticTypes.includes(status)) {
      this.filteredEticTypeList = [...this.eticTypeList];
    } else {
      this.filteredEticTypeList = ['FIRM', 'WA'];

      const currentEticType = this.specifyDetailForm.get('eticttype')?.value;
      if (currentEticType && !this.filteredEticTypeList.includes(currentEticType)) {
        this.specifyDetailForm.get('eticttype')?.setValue(null);
        this.onEticTypeChange('');
      }
    }
  }

  filterNiwTimersList(status: string): void {
    const statusToTimersMap: { [key: string]: string[] } = NIWTimerAbbreviations.statusToTimersMap;

    if (statusToTimersMap[status]) {
      this.filteredNiwTimersList = statusToTimersMap[status];
    } else {
      this.filteredNiwTimersList = [...this.niwTimersList];
    }

    const currentReason = this.specifyDetailForm.get('eticNiwReason')?.value;
    if (currentReason && !this.filteredNiwTimersList.includes(currentReason)) {
      this.specifyDetailForm.get('eticNiwReason')?.setValue(null);
    }
  }



  get filteredStatusList(): string[] {
    return this.statusList.filter(status => !this.excludedList.includes(status));
  }

  updateValidators() {
    if (this.eventtype !== 'OOS') {
      this.specifyDetailForm.get('eticStartDate')?.clearValidators();
      this.specifyDetailForm.get('eticStartTime')?.clearValidators();
      this.specifyDetailForm.get('eticStartHour')?.clearValidators();
      this.specifyDetailForm.get('eticStartMinute')?.clearValidators();
    }
    if (this.eventtype === 'NOTE') {
      this.specifyDetailForm.get('station')?.clearValidators();
      this.specifyDetailForm.get('aof')?.clearValidators();
    }
    if (this.eventtype === 'DOA') {
      this.specifyDetailForm.get('aof')?.clearValidators();
      this.specifyDetailForm.get('station')?.clearValidators();
      this.specifyDetailForm.get('status')?.clearValidators();
      this.specifyDetailForm.get('comment')?.clearValidators();
    }
    this.specifyDetailForm.get('eticStartDate')?.updateValueAndValidity();
    this.specifyDetailForm.get('eticStartTime')?.updateValueAndValidity();
    this.specifyDetailForm.get('eticStartHour')?.updateValueAndValidity();
    this.specifyDetailForm.get('eticStartMinute')?.updateValueAndValidity();
    this.specifyDetailForm.get('station')?.updateValueAndValidity();
    this.specifyDetailForm.get('aof')?.updateValueAndValidity();
    this.specifyDetailForm.get('status')?.updateValueAndValidity();
    this.specifyDetailForm.get('comment')?.updateValueAndValidity();
    this.validateFormForSave();
  }

  onStepSelectionChange(event: any): void {
    this.isScrollingProgrammatically = true;
    const index = event.selectedIndex;
    this.activeStep = index;
    if (index === 4 && this.eventtype !== 'NOTE') this.isMsnStepVisited = true;
    const section = this.sections.toArray()[index];
    if (section) {
      this.scrollWithOffset(section.nativeElement, 50);
      setTimeout(() => {
        this.stepper.selectedIndex = index;
        this.isScrollingProgrammatically = false;
        this.cdr.detectChanges();
      }, 600);
    }
    this.validateFormForSave();
  }

  onScroll(): void {
    if (this.isScrollingProgrammatically) return;

    const scrollTop = this.scrollContainer.nativeElement.scrollTop;
    let closestSectionIndex = 0;
    let minDistance = Infinity;

    this.sections.toArray().forEach((section, index) => {
      const sectionTop = section.nativeElement.offsetTop;
      const distance = Math.abs(scrollTop - sectionTop);

      if (distance < minDistance) {
        minDistance = distance;
        closestSectionIndex = index;
      }
    });

    if (this.activeStep !== closestSectionIndex) {
      this.activeStep = closestSectionIndex;
      this.stepper.selectedIndex = closestSectionIndex;
      if (closestSectionIndex === 4 && this.eventtype !== 'NOTE') this.isMsnStepVisited = true;
      this.cdr.detectChanges();
    }
  }

  scrollWithOffset(element: HTMLElement, offset: number = 50) {
    const container = this.scrollContainer.nativeElement;
    const containerRect = container.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    const scrollPosition = container.scrollTop + (elementRect.top - containerRect.top) - offset;
    container.scrollTo({ top: scrollPosition, behavior: 'smooth' });
  }

  onFormModelUpdate(updatedModel: any) {
    this.reportingCategoriesActual = updatedModel;
    this.validateFormForSave();
  }

  onRecdRepCats(repCatMap: any) {
    if (repCatMap && repCatMap['reportingCategories']) {
      for (let i of repCatMap['reportingCategories']) {
        if (!this.reportingCategoriesMap.hasOwnProperty(i['level2Name'])) {
          this.reportingCategoriesMap[i['level2Name']] = [i['level1Id'], i['level1Name'], i['level2Id']];
        }
      }
      this.validateFormForSave();
    }
  }

  getAffectedOutboundFlightList() {
    this.stationsList = [];
    this.managerList = [];
    this.aofList = [];
    this.aofGmADMap = {};
    let payload = { ...this.aofRequestPayload };
    payload['acn'] = this.acn;
    this.addEventService.getFlightLegDetails(parseInt(this.acn)).subscribe({
      next: (result) => {
        const seenFlights = new Set<string>();
        if (result['upcomingFlights']) {
          result['upcomingFlights'].slice(0, 3).reverse().forEach((i: any) => {
            this.stationsList.push(i['legDestination']);
            const aofVal = (
              i['flightNumber'] + " | " + this.toDateFormat(i['flightDate'], false) + " - " +
              this.formatLegNbr(i['legNumber']) + " | " +
              i['legOrigin'] + " - " + i['legDestination'] + " | " +
              this.formatDateTimeToDDMMMYYYY_HHMM(i['legDepartureTime']) + " - " +
              this.formatDateTimeToDDMMMYYYY_HHMM(i['legArrivalTime']).split(" ")[1] + "#" +
              "STA: " + i['legDestination'] + " | GTime: " + this.convertMinutesToDHMin(i['groundTimeMinutes']) + "#" +
              this.toDateFormat(i['flightDate'], true)
            );
            this.aofList.push(aofVal);
          });
        }
        if (result['currentFlight']) {
          this.stationsList.push(result['currentFlight']['station']);
          let aofVal = `${result['currentFlight']['flightNumber']} | ${this.toDateFormat(result['currentFlight']['flightDate'], false)} - ${this.formatLegNbr(result['currentFlight']['legNumber'])} | ${result['currentFlight']['legOrigin']} - ${result['currentFlight']['legDestination']} | ${this.formatDateTimeToDDMMMYYYY_HHMM(result['currentFlight']['legDepartureTime'])} - ${this.formatDateTimeToDDMMMYYYY_HHMM(result['currentFlight']['legArrivalTime']).split(" ")[1]}#STA: ${result['currentFlight']['legDestination']} | GTime: ${this.convertMinutesToDHMin(result['currentFlight']['groundTimeMinutes'])}#${this.toDateFormat(result['currentFlight']['fleetCode'], true)}`;
          this.aofList.push(aofVal);
          this.specifyDetailForm.patchValue({ 
            currentFlightArrivalTime: this.formatDateTimeToETAHHMMZDD(result['currentFlight']['legArrivalTime']),
            station: result['currentFlight']['station'] || null,
            aof: aofVal
          });
          if (result['currentFlight']['station']) {
            this.onStationChange(result['currentFlight']['station']);
          }
        }
        if (result['pastFlights']) {
          result['pastFlights'].reverse();
          result['pastFlights'].forEach((i: any, index: number) => {
            if(index < 3) {
              this.stationsList.push(i['legDestination']);
              let aofVal = i['flightNumber'] + " | " + this.toDateFormat(i['flightDate'], false) + " - " + this.formatLegNbr(i['legNumber'])  + " | " + i['legOrigin'] + " - " + i['legDestination'] + " | " + this.formatDateTimeToDDMMMYYYY_HHMM(i['legDepartureTime']) + " / " + this.formatDateTimeToDDMMMYYYY_HHMM(i['legArrivalTime']).split(" ")[1] + "#" + "STA: " + i['legDestination'] + " | GTime: " + this.convertMinutesToDHMin(i['groundTimeMinutes']) + "#" + this.toDateFormat(i['flightDate'], true);
              this.aofList.push(aofVal);
            }
          });
        }
        this.stationsList = [...new Set(this.stationsList)];
        this.aofList = [...new Set(this.aofList)];
        this.validateFormForSave();
      },
      error: (err) => {
        console.error('Error fetching flight leg details:', err);
        this.aofList = [];
        this.stationsList = [];
        this.validateFormForSave();
      }
    });
  }

  setIntakeFormData(data: any) {
    this.isIntakeFormFullFilled = data.intakeFormFulfilled || false;
    this.intakeFormRespnse = data.intakeFormResponse || null;
    this.validateFormForSave();
  }

  setSelectedDiscrepancyData(data: any) {
    this.isDiscrepancySelected = data.isDiscrepancySelected;
    this.selectedDiscrepancyData = [...(data.addedDiscrepancies || [])];

    this.selectedDiscText = this.selectedDiscrepancyData.map((i: any) => i.discrepancyText);

    const commentControl = this.specifyDetailForm.get('comment');
    let currentComment: string = commentControl?.value?.toUpperCase() || '';

    const tagPrefix = this.selectedCommentTag && currentComment.startsWith(this.selectedCommentTag)
      ? this.selectedCommentTag
      : '';

    let commentContent: string = tagPrefix ? currentComment.substring(tagPrefix.length).trim() : currentComment;

    this.selectedDiscrepancyData.forEach((discrepancy: any) => {
      if (Array.isArray(discrepancy.text)) {
        const discrepancyText: string = discrepancy.text
          .filter((t: string) => t.trim() !== '')
          .join(' ')
          .toUpperCase()
          .replace(/\s+/g, ' ')
          .trim();

        if (!discrepancyText) return;

        if (discrepancy.copyToComment) {
          if (!this.addedDiscrepancyTexts.has(discrepancyText) && !commentContent.includes(discrepancyText)) {
            this.addedDiscrepancyTexts.add(discrepancyText);
            commentContent = commentContent ? `${commentContent} ${discrepancyText}` : discrepancyText;
          }
        } else {
          if (this.addedDiscrepancyTexts.has(discrepancyText)) {
            const regex = new RegExp(`\\b${this.escapeRegExp(discrepancyText)}\\b`, 'gi');
            commentContent = commentContent.replace(regex, '').replace(/\s+/g, ' ').trim();
            this.addedDiscrepancyTexts.delete(discrepancyText);
          }
        }
      }
    });

    let newComment: string = tagPrefix ? `${tagPrefix} ${commentContent}` : commentContent;
    newComment = newComment.replace(/\s+/g, ' ').trim();

    commentControl?.setValue(newComment.toUpperCase(), { emitEvent: false });

    this.validateFormForSave();
  }

  private escapeRegExp(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  setSelectedMsnData(data: any) {
    this.selectedMsnData = data || [];
    this.validateFormForSave();
  }

  onTubFileNoteInputChange() {
    this.validateFormForSave();
  }

  getEticInfoText() {
    if(this.specifyDetailForm.get('eticttype')?.value == 'FIRM' || this.specifyDetailForm.get('eticttype')?.value == 'WA') {
      return this.specifyDetailForm.get('eticttype')?.value;
    } else {
      const tu = (this.specifyDetailForm.get('timeUnits')?.value == 'Hours') ? 'H' : 'D';
      return NIWTimerAbbreviations.getAbbreviation(this.specifyDetailForm.get('eticNiwReason')?.value) + '+' + this.specifyDetailForm.get('timeNeeded')?.value + tu;
    }
    return '';
  }

  onSubmit() {
    this.maintenanceEventDetailsService.getUserInfo().subscribe((userInfo: UserDto) => {
      const affectedFlightDateSplit = this.specifyDetailForm.get('aof')?.value ? this.specifyDetailForm.get('aof')?.value.split('|')[1].split('-')[0].trim().split('/') : [];
      let addEventRequestDao: AddEventRequestDao = {
        eventId: "",
        changeType: "",
        groupId: "1",
        accessLevel: "90",
        acn: this.acn,
        aircraftType: this.aircraftType,
        eventType: this.eventtype,
        station: this.specifyDetailForm.get('station')?.value || '',
        startDateTime: this.getCurrentUTCDateTime(),
        status: this.specifyDetailForm.get('status')?.value || '',
        eticDateTime: this.specifyDetailForm.get('eticttype')?.value != 'Not In Work (NIW)' ? this.convertDateTimeToISO(
          this.specifyDetailForm.get('eticStartDate')?.value, this.specifyDetailForm.get('eticStartHour')?.value, this.specifyDetailForm.get('eticStartMinute')?.value
        ) : null,
        eticInfo: this.getEticInfoText(),
        eticComment: this.specifyDetailForm.get('comment')?.value.toUpperCase() || '',
        affectedFlightNumber: this.specifyDetailForm.get('aof')?.value ? this.specifyDetailForm.get('aof')?.value.split('|')[0].trim() : null,
        affectedFlightDate: affectedFlightDateSplit.length > 0 ? `${affectedFlightDateSplit[2]}${affectedFlightDateSplit[1]}${affectedFlightDateSplit[0]}` : '',
        affectedFlightLegNumber: this.specifyDetailForm.get('aof')?.value ? this.specifyDetailForm.get('aof')?.value.split('|')[1].trim().split('-')[1].trim() : null,
        inboundFlightNumber: null,
        inboundFlightDate: null,
        inboundFlightLegNumber: null,
        contactInfoOwner: this.specifyDetailForm.get('owner')?.value || '',
        contactInfoContact: this.specifyDetailForm.get('contact')?.value || '',
        userId: userInfo.uid,
        employeeName: userInfo.name,
        tokenId: null,
        estimatedArrivalDateTime: null,
        additionalDescription: null,
        newStatus: "",
        requestStatus: "",
        timerId: this.getNiWTimerDetails("timerId"),
        timerName: this.getNiWTimerDetails("timerName"),
        timerStartDateTime: this.getCurrentUTCDateTime(),
        timerStopDateTime: null,
        startNIWTimer: false,
        discrepancyList: this.removeUnwantedDataFromResponse(this.selectedDiscrepancyData),
        reportingCategoriesKeys: this.formatReportCategories(this.reportingCategoriesActual),
        tfNotesList: this.specifyDetailForm.get('tfNote')?.value ? [this.specifyDetailForm.get('tfNote')!.value.toLowerCase()] : [],
        msnData: this.selectedMsnData,
        convertedDateTime: this.getCurrentUTCDateTime(),
        createdDateTime: this.getCurrentUTCDateTime(),
        superUpdateRequired: false,
        serverError: null,
        overrideRequest: false,
        overrideEventId: 0,
        continueAddingEvent: false,
        empDepartment: userInfo.departmentname,
        addNewEvent: true,
        resMgrId: this.specifyDetailForm.get('respMgr')?.value.match(/\((\d+)\)/)?.[1] || '',
        memDeskContact: this.specifyDetailForm.get('memDesk')?.value || '',
        OST: this.specifyDetailForm.get('ost')?.value ? 'Y' : 'N',
        eticRsnCd: null,
        eticRsnComment: null,
      }
      console.log('Add Event Request:', addEventRequestDao);
      this.addEventService.addEvent(addEventRequestDao).subscribe((result) => {
        const addEventresult = result['data'];
          const dialogRef = this.dialog.open(AddEventConfirmationPopupComponent, {
            data: {
              disableClose: true,
              autoFocus: false,
              width: '50vw',
              maxWidth: '90vw',
              message: result['data'],
              event: 'NA'
            },
            panelClass: 'custom-addEvent-confirmation-dialog-container'
          });

          dialogRef.afterClosed().subscribe(result => {
            result === "ADD_NEW_EVENT" ? this.addEventService.triggerAddNewEvent() : null;
          });
        });
    });
  }

  resetForm() {
    this.specifyDetailForm.reset();
    this.niwTimerForm.reset();
    this.selectedMsnData = [];
    this.selectedDiscrepancyData = [];
    this.selectedDiscText = [];
    this.addedDiscrepancyTexts.clear();
    this.isIntakeFormFullFilled = false;
    this.intakeFormRespnse = null;
    this.reportingCategoriesMap = {};
    this.reportingCategoriesActual = null;
    this.isDiscrepancySelected = false;
    this.isMsnStepVisited = false;
    this.selectedNiwTimer = '';
    this.niwTimerOptions = [];
    this.initializeComponent();
    this.stepper.reset();
    this.activeStep = 0;
  }

  currentUTCTimeNow(): Date {
    const now = new Date();
    return new Date(
      Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate(),
        now.getUTCHours(),
        now.getUTCMinutes(),
        now.getUTCSeconds(),
        0
      )
    );
  }

  isSameDayUTC(date1: Date, date2: Date): boolean {
    return date1.getUTCFullYear() === date2.getUTCFullYear() &&
           date1.getUTCMonth() === date2.getUTCMonth() &&
           date1.getUTCDate() === date2.getUTCDate();
  }

  formatTime(date: Date): string {
    return `${date.getUTCHours().toString().padStart(2, '0')}:${date.getUTCMinutes().toString().padStart(2, '0')}`;
  }

  toDateFormat(date: any, isFleetCode: boolean = false): string {
    if (!date) return '';
    if (isFleetCode) return date;
    const d = new Date(date);
    return `${d.getUTCDate().toString().padStart(2, '0')}/${(d.getUTCMonth() + 1).toString().padStart(2, '0')}/${d.getUTCFullYear()}`;
  }

  formatLegNbr(leg: any): string {
    return leg.toString().padStart(2, '0');
  }

  formatDateTimeToDDMMMYYYY_HHMM(dateTime: string): string {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    const day = date.getUTCDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' }).toUpperCase();
    const year = date.getUTCFullYear();
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `${day}${month}${year} ${hours}:${minutes}`;
  }

  formatDateTimeToETAHHMMZDD(dateString: string) {
    const date = new Date(dateString);
    const day = date.getUTCDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' }).toUpperCase();
    const year = date.getUTCFullYear();
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `(ETA@${hours}${minutes}Z/${day})`;
  }

  convertMinutesToDHMin(minutes: number): string {
    if (!minutes) return '0h 0m';
    const days = Math.floor(minutes / (24 * 60));
    const hours = Math.floor((minutes % (24 * 60)) / 60);
    const mins = minutes % 60;
    return `${days > 0 ? days + 'd ' : ''}${hours}h ${mins}m`;
  }

  combineDateTime(date: Date, hour: string, minute: string): string {
    if (!date || !hour || !minute) return '';
    return new Date( Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate(),
      parseInt(hour, 10),
      parseInt(minute, 10)
    )).toISOString();
  }

  getCurrentUTCDateTime(): string {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  convertDateTimeToISO(date: any, hour: any, minute: any): string | null {
    if (!date || !hour || !minute) {
      return null;
    }
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    const year = date.getUTCFullYear();
    return `${year}-${month}-${day}T${hour}:${minute}:00`;
  }

  getNiWTimerDetails(value: string): string | null {
    if (this.specifyDetailForm.get('status')?.value !== 'AOG') {
      return null;
    }

    if (value === "timerId") {
      return this.niwTimerResponse?.timerDataList.find((timer: TimerData) => timer.timerName === this.selectedNiwTimer)?.timerId || null;
    }

    if (value === "timerName") {
      return this.selectedNiwTimer || null;
    }
    
    return null
  }

  removeUnwantedDataFromResponse(response: any[]) {
    return response?.map(item => {
      const { discrepancyOid, displayOpenDate, copyToManagerNotes, copyToComment, isDowningItemPreviouslySelected, priority, timeRemaining, text, message, modified, linkModified, downingModified, downingItem, ...rest } = item;
      return rest;
    });
  }

  formatReportCategories(data: any) {
    let finalData = []
    for(let i in data) {
      if(i == 'DOA_Reasons' || i == 'Miscellaneous') {
        for(let j in data[i]) {
          if(data[i][j] == true) {
            let obj: any = {};
            obj['levelOneId'] = this.reportingCategoriesMap[j][0];
            obj['levelTwoId'] = this.reportingCategoriesMap[j][2];
            obj['levelTwoName'] = j;
            obj['updatedLevelOneId'] = this.reportingCategoriesMap[j][0];
            obj['updatedLevelTwoId'] = this.reportingCategoriesMap[j][2];
            obj['isModified'] = true;
            obj['lastUpdatedTime'] = this.getCurrentUTCDateTime();
            finalData.push(obj);
          }
        }
      } else {
        if(data[i] != "") {
          let obj: any = {};
          obj['levelOneId'] = this.reportingCategoriesMap[data[i]][0];
          obj['levelTwoId'] = this.reportingCategoriesMap[data[i]][2];
          obj['levelTwoName'] = data[i];
          obj['updatedLevelOneId'] = this.reportingCategoriesMap[data[i]][0];
          obj['updatedLevelTwoId'] = this.reportingCategoriesMap[data[i]][2];
          obj['isModified'] = true;
          obj['lastUpdatedTime'] = this.getCurrentUTCDateTime();
          finalData.push(obj);
        }
      }
    }
    return finalData;
  }

  onEticTypeChange(eticType: string) {
    const isFirmEticOrWillAdvise = eticType === 'FIRM' || eticType === 'WA';

    if (isFirmEticOrWillAdvise) {
      // Enable Date, Time, Hour, Minute fields
      this.specifyDetailForm.get('eticStartDate')?.enable();
      this.specifyDetailForm.get('eticStartTime')?.enable();
      this.specifyDetailForm.get('eticStartHour')?.enable();
      this.specifyDetailForm.get('eticStartMinute')?.enable();

      // Set current UTC date and time when fields are enabled
      const defaultTime = this.getDefaultEticTime();
      this.specifyDetailForm.get('eticStartDate')?.setValue(this.utcNow);
      this.specifyDetailForm.get('eticStartTime')?.setValue(defaultTime);
      this.specifyDetailForm.patchValue({
        eticStartHour: defaultTime.split(':')[0],
        eticStartMinute: defaultTime.split(':')[1]
      }, { emitEvent: false });
      this.syncEticTimeField();

      // Clear and disable Reason, Time Needed, Units fields
      this.specifyDetailForm.get('timeNeeded')?.setValue(null);
      this.specifyDetailForm.get('timeUnits')?.setValue('Hours');
      this.specifyDetailForm.get('timeNeeded')?.disable();
      this.specifyDetailForm.get('timeUnits')?.disable();
    } else if (eticType && eticType !== '') {
      // Disable Date, Time, Hour, Minute fields
      this.specifyDetailForm.get('eticStartDate')?.disable();
      this.specifyDetailForm.get('eticStartTime')?.disable();
      this.specifyDetailForm.get('eticStartHour')?.disable();
      this.specifyDetailForm.get('eticStartMinute')?.disable();

      // Enable Reason, Time Needed, and Units fields for non-FIRM/non-WA types
      this.specifyDetailForm.get('timeNeeded')?.enable();
      this.specifyDetailForm.get('timeUnits')?.setValue('Hours');
      this.specifyDetailForm.get('timeUnits')?.enable();
    } else {
      // If no ETIC Type is selected, disable all fields initially
      this.specifyDetailForm.get('eticStartDate')?.disable();
      this.specifyDetailForm.get('eticStartTime')?.disable();
      this.specifyDetailForm.get('eticStartHour')?.disable();
      this.specifyDetailForm.get('eticStartMinute')?.disable();
      this.specifyDetailForm.get('timeNeeded')?.disable();
      this.specifyDetailForm.get('timeUnits')?.setValue('Hours');
      this.specifyDetailForm.get('timeUnits')?.disable();
    }
  }

}