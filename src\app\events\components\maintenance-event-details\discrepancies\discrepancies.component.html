<div class="container">
  <!-- <div class="header-container">
      <p *ngIf="!isAddEventSelected" class="title" [ngClass]="{ 'fill-animation': animateTitle }">Discrepancies</p>
      <div [ngClass]="{'add-button-container': isAddEventSelected, 'button-container': !isAddEventSelected}" [class.show]="true">
        <button *ngIf="!isAddEventSelected" mat-raised-button [disabled]="!isUpdateEnabled" (click)="updateDiscrepancies()" class="button">Update</button>
        <button mat-raised-button [disabled]="!isRowSelected" (click)="openDiscrepanciesDetailDialog()" class="button">View</button>
      </div>
  </div> -->

  <div class="table-container">
    <ag-grid-angular
      #agGrid
      id="agGridTable"
      class="ag-theme-alpine ag-grid-table"
      [style.height.px]="gridHeight"
      [rowData]="filteredTableData"
      [columnDefs]="columnDefs"
      [gridOptions]="gridOptions"
      [domLayout]="'normal'"
      [rowSelection]="'single'"
      (selectionChanged)="onRowSelected($event)"
      (rowDoubleClicked)="onRowDoubleClick($event)"
      (cellValueChanged)="onCellValueChanged($event)"
      (gridReady)="onGridReady($event)"
      [modules]="modules">
    </ag-grid-angular>
  </div>
</div>