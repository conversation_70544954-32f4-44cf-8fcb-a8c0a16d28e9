import { Component, ChangeDetectionStrategy, inject, ChangeDetectorRef, Input, SimpleChanges, OnChanges, NgZone } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { EditTimersComponent } from './edit-timers/edit-timers.component';
import { MatTableDataSource } from '@angular/material/table';
import moment from 'moment';
import { StartTimerConfirmationPopupComponent } from './start-timer-confirmation-popup/start-timer-confirmation-popup.component';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { ToastrMessageService } from '../../../../app-layout/services/toastr-message.service';
import { DateAdapter } from '@angular/material/core';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
    selector: 'app-niw-timers',
    templateUrl: './niw-timers.component.html',
    styleUrl: './niw-timers.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class NiwTimersComponent implements OnChanges {

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;

  readonly dialog = inject(MatDialog);

  selectedRow: any = null;

  timerNameAndDetailMap: any = {};

  runningTimer: any = 'None';

  niwTimerTableHeight = 0;
  
  dataSource = new MatTableDataSource<any>([]);

  niwTimerRetrievalReqBody: any = {
    'mode': 'NIW_TIMERS',
    'requestType': '',
    'eventId': '',
    'timerId': '',
    'userId': '',
  }

  niwTimerUpdateReqBody: any = {
    'mode': 'NIW_TIMERS',
    'user_id': '',
    'event_id': ''
  }

  eventType: string = '';
  eventId: string = '';
  userId: string = '5945348';

  ELEMENT_DATA: any[] = [];

  displayedColumns: string[] = ['NIW_Timer', 'Current_NIW_Time', 'Total_NIW_Time', 'Action'];

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService,
              private toastrMessageService: ToastrMessageService,
              private cdRef: ChangeDetectorRef, private ngZone: NgZone,
              private dateAdapter: DateAdapter<Date>
  ) {
    this.dateAdapter.setLocale('en-US');
  }

  ngOnInit() {
    // this.starterCall();
    this.calculateTableHeight();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") {
        this.eventId = this.detailsViewObj['eventID'].toString();
        this.eventType = this.detailsViewObj['eventType'];
        this.starterCall();
      } else {
        this.dataSource.data = [];
        this.ELEMENT_DATA = [];
      }
    }
  }

  starterCall() {
    this.niwTimerRetrievalReqBody['eventId'] = this.eventId;
    this.niwTimerRetrievalReqBody['userId'] = this.userId;
    this.niwTimerUpdateReqBody['event_id'] = this.eventId;
    this.niwTimerUpdateReqBody['user_id'] = this.userId;
    let reqBody = { ...this.niwTimerRetrievalReqBody };
    reqBody['requestType'] = 'VIEW',
    this.retrieveNiwTimerData(reqBody);
  }

  retrieveNiwTimerData(data:any) {
    this.ELEMENT_DATA = [];
    this.maintenanceEventDetailsService.getNiwTimers(data).subscribe((result) => {
      for(let i of result['timerDataList']) {
        let timerDetails = [i['timerId'], i['timerDesc'], i['activeCatg'], i['systemCatg'], i['listOrder']];
        this.timerNameAndDetailMap[i['timerName']] =  timerDetails;
        let getTimersByIdPayload = { ...this.niwTimerRetrievalReqBody };
        getTimersByIdPayload['requestType'] = 'UPDATE';
        getTimersByIdPayload['timerId'] = i['timerId'];
        this.getNiwTimersById(getTimersByIdPayload).subscribe((timersData) => {
          this.generateTableData(i['timerName'], timersData);
        });
      }
    });
  }

  getNiwTimersById(data: any) {
    return this.maintenanceEventDetailsService.getNiwTimers(data);
  }

  generateTableData(timerName: string, result: any) {
    let rowDataTemplate: any = { 
      niw_timer: timerName,
      niw_current_time: "00H:00M:00S",
      niw_total_time: "00H:00M:00S",
      timerStatus: "Start"
    };
    let timerDetails = result['timerDataList'];
    if(timerDetails != null) {
      let totaldiffInMs = 0;
      let currdiffInMs = 0;
      for(let i of timerDetails) {
        let startDtTm = i['timerStartDtTm'];
        let stopDtTm = i['timerStopDtTm'];
        if(i['timerStopDtTm'] != null) {
          const d1 = new Date(startDtTm);
          const d2 = new Date(stopDtTm);
          totaldiffInMs += (d2.getTime() - d1.getTime()); 
        } else {
          const localOffset = new Date().getTimezoneOffset() * 60 * 1000;
          const d1 = new Date(startDtTm - localOffset);
          const d2 = new Date();
          currdiffInMs += (d2.getTime() - d1.getTime());
          rowDataTemplate['timerStatus'] = "Stop";
          this.runningTimer = timerName;
        }
      }
      rowDataTemplate['niw_current_time'] = this.calculateTimeDiff(currdiffInMs);
      rowDataTemplate['niw_total_time'] = this.calculateTimeDiff(totaldiffInMs);
    }
    this.ELEMENT_DATA.push(rowDataTemplate);
    this.dataSource.data = this.ELEMENT_DATA;
  }

  calculateTimeDiff(diffInMs: any) {
    const totalSeconds = Math.floor(diffInMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const formattedHours = hours < 10 ? `0${hours}` : hours;
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
    const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;
    return `${formattedHours}H:${formattedMinutes}M:${formattedSeconds}S`;
  }

  convertToMillis(time: string) {
    const regex = /(\d+)H:(\d{2})M:(\d{2})S/;
    const match = time.match(regex);
    if (match) {
      const hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const seconds = parseInt(match[3]);
      const totalMilliseconds = (hours * 3600000) + (minutes * 60000) + (seconds * 1000);
      return totalMilliseconds;
    } else {
      throw new Error("Invalid time format. Please use '00H:00M:00S'.");
    }
  }

  openEditDialog(row: any): void {
    this.selectedRow = row;
    const dialogRef = this.dialog.open(EditTimersComponent, {
      panelClass: 'editTimer-custom-dialog',
      width: '45%',       // Responsive width
      maxWidth: '100vw',   // Prevents it from exceeding the viewport
      data: {
        eventId: this.eventId,
        timerName: this.selectedRow.niw_timer,
        timerId: this.timerNameAndDetailMap[this.selectedRow.niw_timer][0],
        userId: this.userId
      }
    });

    dialogRef.componentInstance.updateEvent.subscribe((update: any) => {
      this.ELEMENT_DATA = this.ELEMENT_DATA.map(item => {
        if (item.niw_timer === update['timerName']) {
          let timeVal = null;
          if(update['type'] == 'ADD') {
            timeVal = (this.convertToMillis(item['niw_total_time']) + this.convertToMillis(update['duration']))
          } else {
            timeVal = (this.convertToMillis(item['niw_total_time']) - this.convertToMillis(update['duration']))
          }
          return { 
            ...item,  
            niw_total_time: this.calculateTimeDiff(timeVal)
          };
        }
        return item;
      });  
      this.dataSource.data = [ ...this.ELEMENT_DATA];
    });
  }

  selectRow(row: any): void {
    this.selectedRow = row;
  }

  editTimer(status: string, timerName: any) {
    if(status === "Start") {
      this.startTimer(timerName);
    } else {
      this.stopTimer(timerName);
    }
  }

  startTimer(timerName: string) {
    const dialogRef = this.dialog.open(StartTimerConfirmationPopupComponent, {
      data: {
        'oldTimerName': this.runningTimer,
        'newTimerName': timerName
      },
      panelClass: 'startConfirmation-custom-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result == 'NO' || result == null) {
        return;
      } else {
        let reqPayload = { ...this.niwTimerUpdateReqBody }; 
        reqPayload['flag'] = 'START'; 
        reqPayload['timer_id'] = this.timerNameAndDetailMap[timerName][0]; 
        reqPayload['event_active'] = true;
        reqPayload['niw_timer_data'] = {};
        reqPayload['niw_timer_data']['eventTimersPk'] = {};
        reqPayload['niw_timer_data']['eventTimersPk']['eventId'] = this.eventId;
        reqPayload['niw_timer_data']['timerId'] = this.timerNameAndDetailMap[timerName][0];
        this.maintenanceEventDetailsService.updateNiwTimers(reqPayload).subscribe((result) => {
          for(let i of this.ELEMENT_DATA) {
            if(i['niw_timer'] == timerName) {
              this.ELEMENT_DATA = this.ELEMENT_DATA.map(item => {
                if (item.niw_timer === timerName) {
                  return { 
                    ...item, 
                    timerStatus: "Stop", 
                    niw_current_time: "00H:00M:01S" 
                  };
                }
                return item;
              });        
            }
            if(i['niw_timer'] != timerName && i['timerStatus'] == 'Stop') {
              this.stopOtherTimers(i['niw_timer']);
            }
          }
          this.runningTimer = timerName;
          this.dataSource.data = [ ...this.ELEMENT_DATA];
          this.toastrMessageService.success(`${timerName} Timer Started Successfully for Acn ${this.detailsViewObj.eventACN}`);
        });
      }
    });
  }

  stopOtherTimers(timerName: string) {
    let getTimersByIdPayload = { ...this.niwTimerRetrievalReqBody };
    getTimersByIdPayload['requestType'] = 'UPDATE';
    getTimersByIdPayload['timerId'] = this.timerNameAndDetailMap[timerName][0];
    this.getNiwTimersById(getTimersByIdPayload).subscribe((timersData) => {
      let timerDetails = timersData['timerDataList'];
      let totaldiffInMs = 0;
      for(let i of timerDetails) {
        const d1 = new Date(i['timerStartDtTm']);
        const d2 = new Date(i['timerStopDtTm']);
        totaldiffInMs += (d2.getTime() - d1.getTime()); 
      }
      this.ELEMENT_DATA = this.ELEMENT_DATA.map(item => {
        if (item.niw_timer === timerName) {
          return { 
            ...item, 
            timerStatus: "Start", 
            niw_current_time: "00H:00M:00S",
            niw_total_time: this.calculateTimeDiff(totaldiffInMs) 
          };
        }
        return item;
      }); 
      this.dataSource.data = [ ...this.ELEMENT_DATA];
    });
  }

  stopTimer(timerName: string) { 
    let timerRec: any = {};
    const retrievalPayload = { ...this.niwTimerRetrievalReqBody };
    retrievalPayload['requestType'] = 'UPDATE';
    retrievalPayload['timerId'] = this.timerNameAndDetailMap[timerName][0];
    this.getNiwTimersById(retrievalPayload).subscribe((result) => {
      for(let i of result['timerDataList']) {
        if(i['timerStopDtTm'] == null) {
          timerRec = { ...i };
        }
      }
      let reqPayload = { ...this.niwTimerUpdateReqBody };
      reqPayload['flag'] = 'STOP'; 
      reqPayload['timer_id'] = this.timerNameAndDetailMap[timerName][0]; 
      reqPayload['event_active'] = true;
      reqPayload['niw_timer_data'] = {};
      reqPayload['niw_timer_data']['eventTimersPk'] = {};
      reqPayload['niw_timer_data']['eventTimersPk']['eventId'] = this.eventId;
      reqPayload['niw_timer_data']['timerId'] = this.timerNameAndDetailMap[timerName][0];
      reqPayload['niw_timer_data']['eventTimersPk']['creationDtTm'] = moment(timerRec['creationDtTm']).format("YYYY-MM-DDTHH:mm:ss.SSS");
      reqPayload['niw_timer_data']['lastUpdateDtTm'] = moment(timerRec['lastUpdateDtTm']).format("YYYY-MM-DDTHH:mm:ss.SSS");
  
      this.maintenanceEventDetailsService.updateNiwTimers(reqPayload).subscribe((result) => {
        for(let i of this.ELEMENT_DATA) {
          if(i['niw_timer'] == timerName){
            const localOffset = new Date().getTimezoneOffset() * 60 * 1000;
            const d1 = new Date(timerRec['timerStartDtTm'] - localOffset);
            const d2 = new Date();
            this.ELEMENT_DATA = this.ELEMENT_DATA.map(item => {
              if (item.niw_timer === timerName) {
                return { 
                  ...item, 
                  timerStatus: "Start", 
                  niw_current_time: "00H:00M:00S",
                  niw_total_time: this.calculateTimeDiff(this.convertToMillis(i['niw_total_time']) + (d2.getTime() - d1.getTime())) 
                };
              }
              return item;
            }); 
            break; 
          }
        }
        this.runningTimer = 'None';
        this.dataSource.data = [ ...this.ELEMENT_DATA];
        this.toastrMessageService.success(`${timerName} Timer Stopped Successfully for Acn ${this.detailsViewObj.eventACN}`);
      });
    }); 
  }

  calculateTableHeight(): void {
    const headerHeight = 60;
    const footerHeight = 60;
    const padding = 20;

    this.ngZone.run(() => {
      this.niwTimerTableHeight = window.innerHeight - headerHeight - footerHeight - padding - 151;
      this.cdRef.detectChanges();
    });
  }

}
