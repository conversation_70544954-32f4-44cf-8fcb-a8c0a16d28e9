<!-- <div class="custom-checkbox-container">
    <input type="checkbox" class="custom-checkbox" [checked]="params.data.isChecked" (change)="onCheckboxChange($event)" (change)="onCheckboxChange($event)" />
    <span>{{ value }}</span>
</div> -->

<div class="custom-checkbox-container">
    <input
      type="checkbox"
      class="custom-checkbox"
      [checked]="params.data.isChecked"
      [disabled]="isDisabled"
      (change)="onCheckboxChange($event)"
    />
    <span class="clickable-link" (click)="openMsnShippingDetails(params.data.msn)">
      {{ params.data.msnShippingDetails }}
      {{ value }}
    </span>
</div>  