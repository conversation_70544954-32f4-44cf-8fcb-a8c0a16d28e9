import { Component } from '@angular/core';
import { DiscrepanciesFilterDialogComponent } from '../../maintenance-event-details/discrepancies/discrepancies-filter-dialog/discrepancies-filter-dialog.component';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-add-acn',
  standalone: false,
  templateUrl: './add-acn.component.html',
  styleUrl: './add-acn.component.scss'
})
export class AddAcnComponent {

  selectedAcnOption: string = 'singleAcn';
  multipleAcnValues: string = ''; // New model for textarea
  selectedDiscrepancyTypeValues: string[] = [];
  enableApplyFilterButton: boolean = true;
  acnValue: string = '';
  error: boolean = false;
  errorMessage: string = "";

  isSingleAcnSelected: boolean = false;
  isMultipleAcnSelected: boolean = false;

  constructor(private dialogRef: MatDialogRef<DiscrepanciesFilterDialogComponent>,
              private maintenanceEventDetailsService: MaintenanceEventDetailsService
              ) {}

  ngOnInit() {
    this.optionChanged();
    this.checkApplyButtonState();
  }

  optionChanged() {
    this.isSingleAcnSelected = this.selectedAcnOption === 'singleAcn';
    this.isMultipleAcnSelected = this.selectedAcnOption === 'multipleAcn';
    this.checkApplyButtonState();
  }

  allowOnlyDigits(event: KeyboardEvent): boolean {
    const charCode = event.key.charCodeAt(0);
    const isDigit = charCode >= 48 && charCode <= 57;
    if (!isDigit) {
      event.preventDefault();
      return false;
    }
    return true;
  }

  sanitizeInput(field: 'single' | 'rangeFrom' | 'rangeTo') {
    let value = '';

    switch (field) {
      case 'single':
        value = this.acnValue.replace(/\D/g, '').slice(0, 5);
        this.acnValue = value;
        break;
    }

    this.checkApplyButtonState();
  }

  checkApplyButtonState() {
    if (this.isSingleAcnSelected) {
      this.multipleAcnValues = '';
      this.enableApplyFilterButton = this.acnValue.length > 0;
    } else if (this.isMultipleAcnSelected) {
      this.acnValue = '';
      this.enableApplyFilterButton = this.multipleAcnValues != "";
    } else {
      this.acnValue = '';
      this.multipleAcnValues = '';
      this.enableApplyFilterButton = false;
    }
  }

  apply() {
    const data = {
      selectedAcnOption: this.selectedAcnOption,
      acnValue: this.acnValue ? this.acnValue : undefined,
      multipleAcns: this.multipleAcnValues.split(',').map(acn => acn.trim()).filter(acn => acn)
    };
    this.dialogRef.close(data);
  }

  cancel() {
    this.dialogRef.close();  // Close dialog without returning data
  } 

}
