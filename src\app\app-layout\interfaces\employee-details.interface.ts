export interface EmployeeDetails {
  displayNm?: string;
  title?: string;
  telNbr?: string;
  uid?: string;
  email?: string;
  deptNbr?: string;
  deptName?: string;
  location?: string;
  managerid?: string;
  seniorityLevel?: string;
}

export interface LoggedUserDetails {  
  id?: string;  
  name?: string;
  firstName?: string;
  lastName?: string;
  dssPermissions?: DssPermission[]
}

export interface DssPermission {  
  uiElement?: string;  
  access?: string[];  
}

export interface IDesigneeEmpl {
  userId: number;
  empId: number;
  firstName: string;
  lastName: string;
  middleName: string;
  displayName: string;
  allowDesigneeFlg: 'Y' | 'N';
}
