export class AddEventRequestDao {
  eventId: string = '';
  changeType: string = '';
  groupId: string = '';
  accessLevel: string = '';
  acn: string = '';
  aircraftType: string = '';
  eventType: string = '';
  station: string = '';
  startDateTime: string = '';
  status: string = '';
  eticDateTime: string | null = '';
  eticInfo: string = '';
  eticComment: string = '';
  affectedFlightNumber: string = '';
  affectedFlightDate: string = '';
  affectedFlightLegNumber: string = '';
  inboundFlightNumber: string | null = '';
  inboundFlightDate: string | null = '';
  inboundFlightLegNumber: string | null = '';
  contactInfoOwner: string = '';
  contactInfoContact: string = '';
  userId: string = '';
  employeeName: string = '';
  tokenId: string | null = '';
  estimatedArrivalDateTime: string | null = '';
  additionalDescription: string | null = '';
  newStatus: string = '';
  requestStatus: string = '';
  timerId: string | null = '';
  timerName: string | null = '';
  timerStartDateTime: string | null = '';
  timerStopDateTime: string | null = '';
  startNIWTimer: boolean = false;

  discrepancyList: DiscrepancyItem[] = [];
  reportingCategoriesKeys: ReportingCategory[] = [];
  tfNotesList: string[] = [];
  msnData: any[] = [];

  convertedDateTime: string = '';
  createdDateTime: string = '';
  superUpdateRequired: boolean = false;
  serverError: any = null;
  overrideRequest: boolean = false;
  overrideEventId: number = 0;
  continueAddingEvent: boolean = false;
  empDepartment: string = '';
  addNewEvent: boolean = true;
  resMgrId: string = '';
  memDeskContact: string = '';
  OST: string = '';
  eticRsnCd: string | null = '';
  eticRsnComment: string | null = '';

  constructor(init?: Partial<AddEventRequestDao>) {
    Object.assign(this, init);
  }
}

export class DiscrepancyItem {
  eventId: number = 0;
  link: boolean = false;
  ata: string = '';
  number: string = '';
  discType: string = '';
  eventType: string = '';
  openDate: string = '';
  openStation: string = '';
  inWork: string = '';
  closed: string = '';
  text: string[] = [''];
  message: string[] = [''];
  status: string = '';
  isModified: boolean = false;
  isLinkModified: boolean = false;
  isDowningModified: boolean = false;
  isDowningItem: boolean = false;

  constructor(init?: Partial<DiscrepancyItem>) {
    Object.assign(this, init);
  }
}

export class ReportingCategory {
  eventId: string = '';
  levelOneId: string = '';
  levelTwoId: string = '';
  levelTwoName: string = '';
  updatedLevelOneId: string = '';
  updatedLevelTwoId: string = '';
  isModified: boolean = false;
  lastUpdatedTime: string = '';

  constructor(init?: Partial<ReportingCategory>) {
    Object.assign(this, init);
  }
}