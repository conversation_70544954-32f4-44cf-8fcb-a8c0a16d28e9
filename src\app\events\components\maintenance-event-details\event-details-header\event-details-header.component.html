
<div class="container">
    <div class="row first-row">
      <div class="label-value" [attr.data-fulltext]="detailsHeaderDao.eventStatus">
        <span class="label">Status:</span> 
        <span class="value">{{ detailsHeaderDao.eventStatus || "" }}</span>
      </div>
      <div class="label-value"
           [attr.data-fulltext]="detailsHeaderDao.eventEticDateTime">
        <span class="label">ETIC:</span> 
        <span class="value">{{ detailsHeaderDao.eventEticDateTime || "" }}</span>
      </div>
      <div class="label-value"
           [attr.data-fulltext]="detailsHeaderDao.eventStation">
        <span class="label">Station:</span> 
        <span class="value">{{ detailsHeaderDao.eventStation || "" }}</span>
      </div>
      <div class="label-value"
           [attr.data-fulltext]="detailsHeaderDao.duration">
        <span class="label">Duration:</span> 
        <span class="value">{{ detailsHeaderDao.duration || "" }}</span>
      </div>
      <div class="label-value"
           [attr.data-fulltext]="detailsHeaderDao.startDateTime">
        <span class="label">Start:</span> 
        <span class="value">{{ detailsHeaderDao.startDateTime || "" }}</span>
      </div>
      <div class="label-value"
           [attr.data-fulltext]="detailsHeaderDao.numberOfMSN">
        <span class="label">MSNs:</span> 
        <span class="value">{{ detailsHeaderDao.numberOfMSN || "" }}</span>
      </div>
    </div>
  
    <div class="row second-row">
      <div class="label-value"><span class="label">Owner:</span> <span class="value">{{ detailsHeaderDao.acOwnerGroupId || "" }}</span></div>
      <div class="label-value medium"><span class="label">Contact:</span> <span class="value">{{ detailsHeaderDao.contact || "" }}</span></div>
      <div class="label-value medium"><span class="label">Responsible Mgr:</span> <span class="value">{{ detailsHeaderDao.resMgrId || "" }}</span></div>
      <div class="label-value medium"><span class="label">Speed Dial:</span> <span class="value">{{ detailsHeaderDao.mxSpeedDial || "" }}</span></div>
      <div class="label-value medium"><span class="label">MEM Desk Contact:</span> <span class="value">{{ detailsHeaderDao.memDeskContact || "" }}</span></div>
      <div class="label-value small ost-field"><span class="label">OST:</span> <span class="value">{{ detailsHeaderDao.eventOST || "" }}</span></div>
    </div>
  
    <div class="row third-row">
      <div class="label-value expandable" style="flex-grow: 2;" [attr.data-fulltext]="detailsHeaderDao.eventCurrentComment">
        <span class="label">Comment:</span> 
        <span class="value">{{ detailsHeaderDao.eventCurrentComment || ""  }}</span>
      </div>
      <div class="label-value expandable" style="flex-grow: 2;" [attr.data-fulltext]="detailsHeaderDao.eventEticText">
        <span class="label">ETIC Comments:</span> 
        <span class="value">{{ detailsHeaderDao.eventEticText || "" }}</span>
      </div>
      <div class="label-value expandable" style="flex-grow: 2;" [attr.data-fulltext]="detailsHeaderDao.eventEticReasonCd">
        <span class="label">Reason:</span> 
        <span class="value">{{ detailsHeaderDao.eventEticReasonCd || "" }}</span>
      </div>
    </div>
  </div>