import { Injectable } from '@angular/core';
import { IndividualConfig, ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ToastrMessageService {

private toastrConfig: Partial<IndividualConfig> = {
    timeOut: 5000,
    closeButton: false,
    progressBar: true,
    tapToDismiss: true,  
    progressAnimation: 'decreasing',
    positionClass: 'toast-top-right',
  };

  constructor(private toastr: ToastrService) {}

  success(message: string, title: string = '') {
    this.toastr.success(message, title, this.toastrConfig);
  }

  error(message: string, title: string = '') {
    this.toastr.error(message, title, this.toastrConfig);
  }

  info(message: string, title: string = '') {
    this.toastr.info(message, title, this.toastrConfig);
  }

  warning(message: string, title: string = '') {
    this.toastr.warning(message, title, this.toastrConfig);
  }

  clearAll() {
    this.toastr.clear();
  }

}
