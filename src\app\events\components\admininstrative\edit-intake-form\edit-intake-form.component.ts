import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { AdministrativeService } from '../../../services/administrative.service';
import { AdmininstrativeSharedService } from '../admininstrative-shared.service';
import { UserIntakeForm } from '../../../dto/UserIntakeFormDto';
import { intakeForm } from '../../../dto/intakeFormDto';
import { modifiedIntakeForm } from '../../../dto/modifiedIntakeFormDto';
import { questions } from '../../../dto/questionsDto';
import { CdkDragDrop, CdkDropList, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { CreateQuestionDialogComponent } from '../create-question-dialog/create-question-dialog.component';
import Swal from 'sweetalert2';
import { ROLE_AND_EVENT_MAP } from '../../../constants/colormap-constants';

interface Panel {
  title: string;
  content: questions[] | intakeForm[];
  panelId: number;
}

@Component({
  selector: 'app-edit-intake-form',
  standalone: false,
  templateUrl: './edit-intake-form.component.html',
  styleUrl: './edit-intake-form.component.scss'
})
export class EditIntakeFormComponent {

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    public dialog: MatDialog,
    public administrativeService: AdministrativeService,
    public admininstrativeSharedService: AdmininstrativeSharedService
  ) { }

  @ViewChild('doneList') doneList!: CdkDropList;
  @ViewChild('todoList') todoList!: CdkDropList;

  userIntakeFormList: Array<UserIntakeForm> = [];
  intakeFormsList: Array<intakeForm> = [];
  modifiedIntakeForm: modifiedIntakeForm = new modifiedIntakeForm();
  selectedFormTitle = '';
  initialQuestionsOfSelectedIntakeForm: Array<questions> = [];
  selectedIntakeFormQuestions: Array<questions> = [];
  questions: Array<questions> = [];
  done: Array<questions> = [];
  filteredQuestions: Array<questions> = [];
  searchTerm: string = '';
  selectedUserIntakeForm: UserIntakeForm = new UserIntakeForm();
  selectedIntakeForm: intakeForm = new intakeForm();
  addedQuestions: Array<questions> = [];
  deletedQuestions: Array<questions> = [];
  newlyAddedQuestions: Array<questions> = [];
  openIndex = -1;
  panels: Panel[] = [
    { title: 'Previous Questions', content: [], panelId: 1 },
    { title: 'Former Intake Forms', content: [], panelId: 2 },
  ];
  roleAndEventMap = ROLE_AND_EVENT_MAP;
  role: string = '';
  event: string = '';
  fleetType: string = '';

  ngOnInit(): void {
    this.route.queryParamMap.subscribe(params => {
      const allUserIntakeForms = params.get('allUserIntakeForms');
      const allQuestions = params.get('allQuestions');
      if (allUserIntakeForms) {
        this.userIntakeFormList = JSON.parse(allUserIntakeForms);
        this.intakeFormsList = this.userIntakeFormList.map(userIntakeForm => userIntakeForm.intakeForm);
        this.selectedIntakeForm = this.userIntakeFormList[0].intakeForm;
        this.selectedUserIntakeForm = this.userIntakeFormList[0];
        this.role = this.roleAndEventMap.get(this.selectedUserIntakeForm.roleId) || '';
        this.event = this.roleAndEventMap.get(this.selectedUserIntakeForm.eventId) || '';
        this.fleetType = this.selectedUserIntakeForm.dssAuthCode || '';
        this.selectedIntakeFormQuestions = this.userIntakeFormList[0].intakeForm.questions;
        this.selectedFormTitle = this.userIntakeFormList[0].intakeFormNm;
        this.initialQuestionsOfSelectedIntakeForm = [...this.userIntakeFormList[0].intakeForm.questions];
      }
      if (allQuestions) {
        this.questions = JSON.parse(allQuestions);
        this.done = this.questions;
        this.filteredQuestions = this.done;
        this.panels = [
          { title: 'Previous Questions', content: this.filteredQuestions, panelId: 1 },
          { title: 'Former Intake Forms', content: this.intakeFormsList, panelId: 2 },
        ];
      } else {
        console.error('allQuestions is null');
      }
    });
  }

  filterQuestions() {
    this.filteredQuestions = this.done.filter(question =>
      question.questionTxt.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  modifyTheSelectedForm(formId: number) {
    const userIntakeForm = this.userIntakeFormList.find(userIntakeForm => userIntakeForm.intakeForm.intakeFormId === formId);
    if (userIntakeForm) {
      this.selectedUserIntakeForm = userIntakeForm;
      this.role = this.roleAndEventMap.get(this.selectedUserIntakeForm.roleId) || '';
      this.event = this.roleAndEventMap.get(this.selectedUserIntakeForm.eventId) || '';
      this.fleetType = this.selectedUserIntakeForm.dssAuthCode || '';
      this.selectedIntakeForm = userIntakeForm.intakeForm;
      this.selectedIntakeFormQuestions = userIntakeForm.intakeForm.questions;
      this.selectedFormTitle = userIntakeForm.intakeFormNm;
      this.initialQuestionsOfSelectedIntakeForm = [...userIntakeForm.intakeForm.questions];
    }
  }

  addNewQuestion() {
    const dialogRef = this.dialog.open(CreateQuestionDialogComponent, {
      width: '80%',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.selectedIntakeFormQuestions.push(result.question);
        this.newlyAddedQuestions.push(result.question);
      }
    });
  }

  drop(event: CdkDragDrop<questions[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      const isDraggingToIntakeForm = event.container === this.todoList;
      if (isDraggingToIntakeForm) {
        const draggedQuestion: questions = event.previousContainer.data[event.previousIndex];
        const alreadyExists = this.selectedIntakeFormQuestions.some(
          q => q.questionId === draggedQuestion.questionId
        );
        if (alreadyExists) {
          Swal.fire('Duplicate Question', 'This question is already present in the intake form.', 'warning');
        } else {
          transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
        }
      } else {
        transferArrayItem(
          event.previousContainer.data,
          event.container.data,
          event.previousIndex,
          event.currentIndex
        );
      }
    }
  }

  findDeletedQuestions() {
    this.deletedQuestions = this.initialQuestionsOfSelectedIntakeForm.filter(initialQuestion =>
      !this.selectedIntakeFormQuestions.some(selectedQuestion => selectedQuestion.questionId === initialQuestion.questionId)
    );
  }

  findAddedQuestions() {
    const filteredQuestions = this.selectedIntakeFormQuestions.filter(selectedQuestion => selectedQuestion.questionId !== 0);
    this.addedQuestions = filteredQuestions.filter(selectedQuestion =>
      !this.initialQuestionsOfSelectedIntakeForm.some(initialQuestion => initialQuestion.questionId === selectedQuestion.questionId)
    );
  }

  OnSubmit() {
    this.findDeletedQuestions();
    this.findAddedQuestions();
    this.modifiedIntakeForm.intakeFormId = this.selectedIntakeForm.intakeFormId;
    this.modifiedIntakeForm.addedQuestions = this.addedQuestions;
    this.modifiedIntakeForm.deletedQuestions = this.deletedQuestions;
    this.modifiedIntakeForm.newlyAddedQuestions = this.newlyAddedQuestions;
    this.administrativeService.updateIntakeForm(this.modifiedIntakeForm);
    this.admininstrativeSharedService.refreshUserIntakeForms();
    Swal.fire('Success', 'User Intake Form Edited successfully', 'success');
    this.router.navigate(['/mets-admininstrative']);
  }

  OnCancel() {
    this.admininstrativeSharedService.refreshUserIntakeForms();
    this.router.navigate(['/mets-admininstrative']);
  }

  OnDelete() {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        if (this.selectedUserIntakeForm) {
          this.administrativeService.deleteUserIntakeForm(
            this.selectedUserIntakeForm.userIntakeFormId
          ).subscribe({
            next: () => {
              this.admininstrativeSharedService.refreshUserIntakeForms();
              Swal.fire('Deleted!', 'User Intake Form deleted successfully.', 'success');
              this.router.navigate(['/mets-admininstrative']);
            },
            error: (error) => {
              console.error('Error deleting user intake form:', error);
              Swal.fire('Error!', 'Failed to delete User Intake Form.', 'error');
            }
          });
        }
      }
    });
  }

  editQuestion(question: questions) {
    const dialogRef = this.dialog.open(CreateQuestionDialogComponent, {
      width: '80%',
      data: { editedQuestion: question, isEdit: true }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const questionIndex = this.selectedIntakeFormQuestions.findIndex(q => q.questionId === question.questionId);
        if (questionIndex !== -1) {
          this.selectedIntakeFormQuestions[questionIndex] = result.question;
        }
      }
    });
  }

  deleteQuestion(question: questions) {
    this.selectedIntakeFormQuestions = this.selectedIntakeFormQuestions.filter(q => q !== question);
    if (!this.done.some(q => q.questionId === question.questionId)) {
      this.done.push(question);
      this.filteredQuestions = [...this.done];
      this.filterQuestions();
    }
  }

  setOpenPanel(index: number) {
    this.openIndex = index;
  }

  getAnswersText(item: questions): string {
    return item.answers.map(a => a.answerTxt).join(', ') || 'N/A';
  }

}
