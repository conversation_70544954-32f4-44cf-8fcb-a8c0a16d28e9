import { Component } from '@angular/core';
import { IHeaderAngularComp } from 'ag-grid-angular';

@Component({
    selector: 'app-custom-maintenance-event-list-status',
    templateUrl: './custom-maintenance-event-list-status.component.html',
    styleUrl: './custom-maintenance-event-list-status.component.scss',
    standalone: false
})
export class CustomMaintenanceEventListStatusComponent {

  params: any;
  backgroundColor: string = '';
  textColor: string = '';

  agInit(params: any): void {
    this.params = params;
    this.setStyle(params.value);
  }

  refresh(params: any): boolean {
    this.params = params;
    this.setStyle(params.value);
    return true;
  }

  private setStyle(value: string): void {
    if (value === 'UP') {
      this.backgroundColor = '#C0FFC0';
      this.textColor = 'black';
    } else if (value === 'TRK') {
      this.backgroundColor = '#FFFF66';
      this.textColor = 'black';
    } else if (value === 'DWN') {
      this.backgroundColor = '#FF9999';
      this.textColor = 'black';
    } else if (value === 'HMX' || value === 'HMO' || value === 'HMD') {
      this.backgroundColor = '#CCCCCC';
      this.textColor = 'black';
    } else if (value === 'DOA') {
      this.backgroundColor = '#C0C0FF';
      this.textColor = 'red';
    } else if (value === 'NOS') {
      this.backgroundColor = '#CCCCCC';
      this.textColor = 'black';
    } else if (value === 'AOG') {
      this.backgroundColor = '#FFC099';
      this.textColor = 'black';
    } else {
      this.backgroundColor = '';
      this.textColor = '';
    }
  }

}

