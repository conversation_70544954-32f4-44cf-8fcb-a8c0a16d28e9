import { Component, Inject, On<PERSON>nit, OnDestroy, ViewChild, AfterViewInit, ViewChildren, ElementRef, QueryList, ChangeDetectorRef } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatStepper } from '@angular/material/stepper';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient, HttpParams } from '@angular/common/http';
import { EnvironmentService } from '../../../../app-layout/services/environment.service';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';
import { AddEventRequestDao } from '../../../dao/addEventDao';
import { UserDto } from '../../../dto/UserInfo';
import { DiscrepanciesComponent } from '../../maintenance-event-details/discrepancies/discrepancies.component';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { MatGridTileHeaderCssMatStyler } from '@angular/material/grid-list';
import { MaintenanceEventListService } from '../../../services/maintenance-event-list.service';

@Component({
  selector: 'app-close-event',
  templateUrl: './close-event.component.html',
  styleUrls: ['./close-event.component.scss'],
  standalone: false
})
export class CloseEventComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('stepper') stepper!: MatStepper;
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  @ViewChildren('sectionRef') sections!: QueryList<ElementRef>;
  @ViewChild(DiscrepanciesComponent) discrepanciesComponent!: DiscrepanciesComponent;

  selectedEvent: any;
  activeStep = 0;
  isScrollingProgrammatically = false;
  stepCompletion: boolean[] = [false, false, false, false, false];

  closeEventForm!: FormGroup;

  reportingCategoriesMap: any = {};
  reportingCategoriesActual: any;

  hasActiveNiwTimers = false;
  activeNiwTimers: any[] = [];
  showStopNiwTimersSection = false;

  isDiscrepanciesViewEnabled: boolean = false;
  isDiscrepanciesUpdateEnabled: boolean = false;

  // Form validation properties
  isCloseEventButtonEnabled = false;

  detailViewData: DetailViewResponseDao = new DetailViewResponseDao();

  private observer!: IntersectionObserver;

  constructor(
    public dialogRef: MatDialogRef<CloseEventComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private http: HttpClient,
    private environmentService: EnvironmentService,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private maintenanceEventListService: MaintenanceEventListService
  ) {
    console.log('Constructor - Raw data received:', data);
    this.selectedEvent = data?.selectedEvent;
    console.log('Constructor - Selected event:', this.selectedEvent);
    this.initializeForms();
  }

  ngOnInit(): void {
    console.log('ngOnInit called');
    console.log('Selected event data:', this.selectedEvent);
    this.detailViewData = new DetailViewResponseDao();
    this.checkActiveNiwTimers();

    // Set up form validation monitoring
    this.setupFormValidation();
  }

  ngAfterViewInit(): void {
    this.setupIntersectionObserver();

    setTimeout(() => {
      this.initializeDetailViewData();
    }, 100);
  }

  initializeForms(): void {
    const utcNow = new Date();
    const utcDate = new Date(Date.UTC(utcNow.getUTCFullYear(), utcNow.getUTCMonth(), utcNow.getUTCDate()));
    const utcTime = this.formatUTCTime(utcNow);

    this.closeEventForm = this.fb.group({
      endDate: [utcDate, Validators.required],
      endTime: [utcTime, Validators.required],
      addTubFileNote: [true],
      linkDiscrepancy: [true],

      discrepancyType: [''],
      discrepancyDescription: [''],

      tubFileNotes: [''],

      niwEndDate: [utcDate, Validators.required],
      niwEndTime: [utcTime, Validators.required]
    });
  }

  onFormModelUpdate(updatedModel: any) {
    this.reportingCategoriesActual = updatedModel;
  }

  onRecdRepCats(repCatMap: any) {
    if (repCatMap && repCatMap['reportingCategories']) {
      for (let i of repCatMap['reportingCategories']) {
        if (!this.reportingCategoriesMap.hasOwnProperty(i['level2Name'])) {
          this.reportingCategoriesMap[i['level2Name']] = [i['level1Id'], i['level1Name'], i['level2Id']];
        }
      }
    }
  }

  formatReportCategories(data: any) {
    let finalData = []
    for(let i in data) {
      if(i == 'DOA_Reasons' || i == 'Miscellaneous') {
        for(let j in data[i]) {
          if(data[i][j] == true) {
            let obj: any = {};
            obj['levelOneId'] = this.reportingCategoriesMap[j][0];
            obj['levelTwoId'] = this.reportingCategoriesMap[j][2];
            obj['levelTwoName'] = j;
            obj['updatedLevelOneId'] = this.reportingCategoriesMap[j][0];
            obj['updatedLevelTwoId'] = this.reportingCategoriesMap[j][2];
            obj['isModified'] = true;
            obj['lastUpdatedTime'] = this.getCurrentUTCDateTime();
            finalData.push(obj);
          }
        }
      } else {
        if(data[i] != "") {
          let obj: any = {};
          obj['levelOneId'] = this.reportingCategoriesMap[data[i]][0];
          obj['levelTwoId'] = this.reportingCategoriesMap[data[i]][2];
          obj['levelTwoName'] = data[i];
          obj['updatedLevelOneId'] = this.reportingCategoriesMap[data[i]][0];
          obj['updatedLevelTwoId'] = this.reportingCategoriesMap[data[i]][2];
          obj['isModified'] = true;
          obj['lastUpdatedTime'] = this.getCurrentUTCDateTime();
          finalData.push(obj);
        }
      }
    }
    return finalData;
  }

  getCurrentUTCDateTime(): string {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  private formatUTCTime(date: Date): string {
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  private setupFormValidation(): void {
    // Monitor form changes to enable/disable Close Event button
    this.closeEventForm.valueChanges.subscribe(() => {
      this.updateCloseEventButtonState();
    });

    // Initial validation check
    this.updateCloseEventButtonState();
  }

  private updateCloseEventButtonState(): void {
    const endDateValid = this.closeEventForm.get('endDate')?.valid || false;
    const endTimeValid = this.closeEventForm.get('endTime')?.valid || false;

    let niwFieldsValid = true;

    // Check NIW timer fields only if there are active timers
    if (this.showStopNiwTimersSection && this.activeNiwTimers.length > 0) {
      const niwEndDateValid = this.closeEventForm.get('niwEndDate')?.valid || false;
      const niwEndTimeValid = this.closeEventForm.get('niwEndTime')?.valid || false;
      niwFieldsValid = niwEndDateValid && niwEndTimeValid;
    }

    // Enable button only if all required fields are valid
    this.isCloseEventButtonEnabled = endDateValid && endTimeValid && niwFieldsValid;
  }

  getFormattedStartDate(timerStartDtTm: number): string {
    if (!timerStartDtTm) {
      return 'N/A';
    }

    try {
      const date = new Date(timerStartDtTm);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${month}/${day}/${year}`;
    } catch (error) {
      console.error('Error formatting start date:', error);
      return 'Invalid Date';
    }
  }

  getFormattedStartTime(timerStartDtTm: number): string {
    if (!timerStartDtTm) {
      return 'N/A';
    }

    try {
      const date = new Date(timerStartDtTm);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch (error) {
      console.error('Error formatting start time:', error);
      return 'Invalid Time';
    }
  }

  private initializeDetailViewData(): void {
    console.log('selectedEvent exists:', !!this.selectedEvent);
    if (this.selectedEvent) {
      this.detailViewData = new DetailViewResponseDao();
      this.detailViewData.eventID = this.selectedEvent.eventID || 0;
      this.detailViewData.eventType = this.selectedEvent.eventType || '';
      this.detailViewData.eventACN = this.selectedEvent.ACN || this.selectedEvent.acn || this.selectedEvent.eventACN || '';
    } else {
      console.warn('No selected event data available for reporting categories');
    }
    this.cdr.detectChanges();
  }

  checkActiveNiwTimers(): void {
    if (!this.selectedEvent?.eventID) {
      console.warn('No eventID found in selected event');
      this.showStopNiwTimersSection = false;
      return;
    }

    const params = new HttpParams().set('eventId', this.selectedEvent.eventID.toString());
    const url = this.environmentService.getNiwTimers;

    this.http.get<any>(url, { params }).subscribe({
      next: (response) => {
        console.log('NIW Timers API response:', response);

        if (response && response.eventActiveTimerDataList && Array.isArray(response.eventActiveTimerDataList)) {
          this.activeNiwTimers = this.processActiveTimers(response.eventActiveTimerDataList, response.timerDataList || []);
          this.hasActiveNiwTimers = this.activeNiwTimers.length > 0;
          this.showStopNiwTimersSection = this.hasActiveNiwTimers;

          console.log(`Found ${this.activeNiwTimers.length} active NIW timers`);
          if (this.hasActiveNiwTimers) {
            console.log('Active NIW timers with names:', this.activeNiwTimers);
          }
        } else {
          this.activeNiwTimers = [];
          this.hasActiveNiwTimers = false;
          this.showStopNiwTimersSection = false;
          console.log('No active NIW timers found or invalid response structure');
        }

        this.cdr.detectChanges();
        this.updateCloseEventButtonState();
      },
      error: (error) => {
        console.error('Error fetching NIW timers:', error);
        this.activeNiwTimers = [];
        this.hasActiveNiwTimers = false;
        this.showStopNiwTimersSection = false;
        this.cdr.detectChanges();

        this.updateCloseEventButtonState();
      }
    });
  }

  private processActiveTimers(activeTimersList: any[], timerDataList: any[]): any[] {
    return activeTimersList.map(activeTimer => {
      const timerId = activeTimer.timerID || activeTimer.timerId;
      const matchingTimer = timerDataList.find(timer =>
        (timer.timerID || timer.timerId) === timerId
      );
      const createdDateTime = this.convertMillisecondsToISOFormat(activeTimer.eventTimersPk.creationDtTm);
      const lastUpdateDateTime = this.convertMillisecondsToISOFormat(activeTimer.lastUpdateDtTm);

      return {
        ...activeTimer,
        timerName: matchingTimer?.timerName || matchingTimer?.name || 'Unknown Timer',
        timerId: timerId,
        createdDateTime: createdDateTime,
        lastUpdateDateTime: lastUpdateDateTime
      };
    });
  }

  updateDiscrepanciesButtons($event: any) {
    this.isDiscrepanciesViewEnabled = $event.rowSelected;
    this.isDiscrepanciesUpdateEnabled = $event.updateEnabled;
  }

  updateDiscrepancies() {
    this.discrepanciesComponent.updateDiscrepancies();
  }

  viewDiscrepancies() {
    this.discrepanciesComponent.openDiscrepanciesDetailDialog();
  }

  setupIntersectionObserver(): void {
    const options = {
      root: this.scrollContainer?.nativeElement,
      rootMargin: '-50% 0px -50% 0px',
      threshold: 0
    };

    this.observer = new IntersectionObserver((entries) => {
      if (this.isScrollingProgrammatically) return;

      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const index = this.sections.toArray().findIndex(sec => sec.nativeElement === entry.target);
          if (index !== -1 && this.activeStep !== index) {
            this.activeStep = index;
            this.stepper.selectedIndex = index;
            this.cdr.detectChanges();
          }
        }
      });
    }, options);

    this.sections.forEach(section => {
      this.observer.observe(section.nativeElement);
    });
  }

  onStepSelectionChange(event: any): void {
    this.isScrollingProgrammatically = true;
    const index = event.selectedIndex;
    this.activeStep = index;
    const section = this.sections.toArray()[index];
    if (section) {
      this.scrollWithOffset(section.nativeElement, 50);
      setTimeout(() => {
        this.stepper.selectedIndex = index;
        this.isScrollingProgrammatically = false;
        this.cdr.detectChanges();
      }, 600);
    }
    this.validateSteps();
  }

  onScroll(): void {
    if (this.isScrollingProgrammatically) return;

    const scrollTop = this.scrollContainer.nativeElement.scrollTop;
    let closestSectionIndex = 0;
    let minDistance = Infinity;

    this.sections.toArray().forEach((section, index) => {
      const sectionTop = section.nativeElement.offsetTop;
      const distance = Math.abs(scrollTop - sectionTop);

      if (distance < minDistance) {
        minDistance = distance;
        closestSectionIndex = index;
      }
    });

    if (this.activeStep !== closestSectionIndex) {
      this.activeStep = closestSectionIndex;
      this.stepper.selectedIndex = closestSectionIndex;
      this.cdr.detectChanges();
    }
  }

  scrollWithOffset(element: HTMLElement, offset: number): void {
    const elementPosition = element.offsetTop;
    const offsetPosition = elementPosition - offset;

    if (this.scrollContainer) {
      this.scrollContainer.nativeElement.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }

  validateSteps(): void {
    this.stepCompletion = [true, true, true, true, true];
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    this.maintenanceEventDetailsService.getUserInfo().subscribe((userInfo: UserDto) => {
      let selectedEventData = {
        userId: userInfo.uid,
        employeeName: userInfo.name,
        empDepartment: userInfo.departmentname,
        tokenId: "Bearer ",
        eventId: this.selectedEvent.eventID,
        eventType: this.selectedEvent.type,
        startDateTime: this.selectedEvent.startDateTime,
        eventEndDateTime: this.formatDateTimeToISO(this.closeEventForm.get('endDate')?.value, this.closeEventForm.get('endTime')?.value),
        ACN: this.selectedEvent?.ACN,
        station: this.selectedEvent.station,
        status: this.selectedEvent?.status,
        aircraftType: null,
        changeRequestLastUpdated: this.selectedEvent.changeRequestLastUpdateDtTime,
        changeType: parseInt(this.selectedEvent.changeType),
        reviewChangeType: 0,
        groupId: 1,
        groupTitle: null,
        accessLevel: "90",
        eticDateTime: this.convertEticDateTimeFormat(this.selectedEvent.eticDateTime),
        eticInfo: this.selectedEvent.eticText,
        eticComment: null,
        requestStatus: this.selectedEvent.requestStatus,
        isDOAEvent: false,
        isDOAComplied: false,
        doaCompliedMaintenance: false,
        discrepancyList: null,
        reportingCategoriesKeys: this.formatReportCategories(this.reportingCategoriesActual),
        activeTimerId: (this.activeNiwTimers.length > 0 ? this.activeNiwTimers[0].timerId : null),
        timerStopDateTime: this.closeEventForm.get('niwEndDate') != null ? this.formatDateTimeToISO(this.closeEventForm.get('niwEndDate')?.value, this.closeEventForm.get('niwEndTime')?.value) : null,
        timerCreatedDateTime: (this.activeNiwTimers.length > 0 ? this.convertToSqlDateTimeFormat(this.activeNiwTimers[0].createdDateTime) : null),
        timerLastUpdated: (this.activeNiwTimers.length > 0 ? this.convertToSqlDateTimeFormat(this.activeNiwTimers[0].lastUpdateDateTime) : null),
        tfNotesList: this.closeEventForm.get('tubFileNotes')?.value ? [this.closeEventForm.get('tubFileNotes')!.value.toLowerCase()] : [],
        createdDateTime: this.convertStringToTimestamp(this.selectedEvent.createdDateTime),
        closedDateTime: null,
        managerNote: this.selectedEvent.managerNote,
        acnRegistrationNumber: null,
        resMgrId: this.selectedEvent.resMgrId,
        memDeskContact: this.selectedEvent.memDeskContact,
        OST: this.selectedEvent.ost,
        eticRsnCd: this.selectedEvent.eticReasonCd,
        eticRsnComment: this.selectedEvent.eticRsnComments,
      };
      this.maintenanceEventListService.closeEvent(selectedEventData).subscribe((result: any) => {
        result['eventID'] = this.selectedEvent.eventID;
        const result2 = {
          action: 'close',
          eventData: result
        };
        this.dialogRef.close(result2);
      });
      console.log("Selected Event Data with values:", selectedEventData);
    });
  }

  private convertMillisecondsToISOFormat(milliseconds: number): string {
    if (!milliseconds) {
      return '';
    }

    const date = new Date(milliseconds);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  private formatDateTimeToISO(dateValue: any, timeValue: string): string {
    if (!dateValue || !timeValue) {
      return '';
    }

    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    const timeParts = timeValue.split(':');
    const hours = timeParts[0] ? timeParts[0].padStart(2, '0') : '00';
    const minutes = timeParts[1] ? timeParts[1].padStart(2, '0') : '00';
    const seconds = '00'; 

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  private convertStringToTimestamp(dateTimeString: string): number {
    if (!dateTimeString) {
      return Date.now();
    }

    const date = new Date(dateTimeString);

    if (isNaN(date.getTime())) {
      console.warn('Invalid date string provided:', dateTimeString);
      return Date.now();
    }

    return date.getTime(); 
  }


  private convertEticDateTimeFormat(eticDateTime: string): string {
    if (!eticDateTime) {
      return '';
    }

    const parts = eticDateTime.split('/');
    if (parts.length !== 2) {
      console.warn('Invalid ETIC DateTime format:', eticDateTime);
      return eticDateTime;
    }

    const timePart = parts[0];
    const datePart = parts[1];

    if (timePart.length !== 4) {
      console.warn('Invalid time part in ETIC DateTime:', timePart);
      return eticDateTime;
    }
    const hours = timePart.substring(0, 2);
    const minutes = timePart.substring(2, 4);

    if (datePart.length !== 6) {
      console.warn('Invalid date part in ETIC DateTime:', datePart);
      return eticDateTime;
    }
    const month = datePart.substring(0, 2);
    const day = datePart.substring(2, 4);
    const year = '20' + datePart.substring(4, 6);

    return `${year}-${month}-${day}T${hours}:${minutes}:00`;
  }

  private convertToSqlDateTimeFormat(dateTimeString: string): string {
    if (!dateTimeString) {
      return '';
    }

    const date = new Date(dateTimeString);

    if (isNaN(date.getTime())) {
      console.warn('Invalid date string provided for SQL format conversion:', dateTimeString);
      return dateTimeString;
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

}
