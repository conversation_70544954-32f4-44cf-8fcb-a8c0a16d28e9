import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BehaviorSubject, catchError, Observable, of, throwError } from 'rxjs'; // Import necessary operators
import { environment } from '../environments/environment';
import { EnvironmentService } from './app-layout/services/environment.service';

@Injectable({
  providedIn: 'root',
})
export class AppComponentService {
  private baseUrl = environment.api.baseUrl;
  private preferencesSubject = new BehaviorSubject<any>(null);
  preferences$ = this.preferencesSubject.asObservable();

  constructor(private http: HttpClient, private environmentService: EnvironmentService) { }

  getUserPreferences(employeeId: number): Observable<any> {
    const params = new HttpParams().set('id', employeeId);
    return this.http.get<any>(this.environmentService.getUserPreferences, { params })
    .pipe(catchError((error) => {
      const defaultPreferences = {
        screenName: 'Home',
        preferenceInfo: [
          { preferenceName: 'Table', preferenceValues: [] },
          { preferenceName: 'Fleet', preferenceValues: [] },
          { preferenceName: 'Event', preferenceValues: [] },
          { preferenceName: 'sideNavClosed', preferenceValues: ["false"] },
          { preferenceName: 'isOptionsClosed', preferenceValues: ["false"] }
        ]
      };
      return of(defaultPreferences);
    }));
  }
  
  setUserPreferences(preferences: any): void {
    this.preferencesSubject.next(preferences);
  }
}