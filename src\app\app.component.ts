import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { AppComponentService } from './app-component.service';
import { UserLoginService } from './app-layout/services/user-login.service';
import { SessionStorageKeys } from './events/constants/sessionStorageKeys';
import { OktaAuth } from '@okta/okta-auth-js';
import { OktaAuthStateService } from '@okta/okta-angular';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  standalone: false,
})
export class AppComponent implements OnInit {

  isAuthenticated: boolean = false;

  title = 'mets-server-nextgen-ui';
  preferences: any;

  preferencesApiCallInitated: boolean = false;

  constructor(private appComponentService: AppComponentService, private userService: UserLoginService,
              private oktaAuthStateService: OktaAuthStateService,private oktaAuth: OktaAuth
             ) {}

  async ngOnInit() {
    // // Wait for the Okta SDK to fully initialize and check if user is authenticated
    // this.isAuthenticated = await this.oktaAuth.isAuthenticated();

    // Optionally subscribe to auth state changes (if user logs in/out)
    this.oktaAuthStateService.authState$.subscribe((authState) => {
      this.isAuthenticated = !!authState?.isAuthenticated;
      if (this.isAuthenticated) {
        this.subscribeToUserDetails();
      }
    });

    // // If already authenticated, proceed
    // if (this.isAuthenticated) {
    //   this.subscribeToUserDetails();
    // }
  }

  subscribeToUserDetails() {
    this.userService.retrievedUserDetails$.subscribe((retrievedUserDetails: any) => {
      if (retrievedUserDetails != null && retrievedUserDetails?.name != null && retrievedUserDetails?.name != "" && !this.preferencesApiCallInitated) {
        this.preferencesApiCallInitated = true;
        this.appComponentService
          .getUserPreferences(parseInt(retrievedUserDetails?.employeeNumber))
          .subscribe({
            next: (preferences) => {
              this.appComponentService.setUserPreferences(preferences);
              this.preferences = preferences;
              setTimeout(() => {this.preferencesApiCallInitated = false}, 3000);
            },
            error: (error) => {
              this.preferences = {};
              this.appComponentService.setUserPreferences(this.preferences);
            },
            complete: () => {
            }
          });
        }
      }
    );
  }

  loadUserPreferences() {
    const userDetails = sessionStorage.getItem(SessionStorageKeys.LOGGED_USER_DETAILS);
    if (userDetails != null && !this.preferencesApiCallInitated) {
      const parsedUserDetails = JSON.parse(userDetails);
      this.appComponentService.getUserPreferences(parseInt(parsedUserDetails?.id)).subscribe((preferences) => {
        this.appComponentService.setUserPreferences(preferences);
        this.preferences = preferences;
        setTimeout(() => {this.preferencesApiCallInitated = false}, 3000);
      });
    }
  }
}