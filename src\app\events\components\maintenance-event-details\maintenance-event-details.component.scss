body {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  color: black;
  overflow-x: hidden;
}

.event-detail {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidenav-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.event_details.card {
  width: 100%;
  margin: 0 !important;
  background-color: whitesmoke;
  margin-bottom: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 2;
  border-radius: 12px;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
}

.navbar-row-1 {
  display: grid;
  grid-template-columns: 10% 10% 8% 8% 10% 11% 10% 8% 10% 1fr;
  gap: 5px;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  padding: 3px;
  border-radius: 8px;
}

.navbar-row-2 {
  display: grid;
  grid-template-columns: 12% 12% 12% 8% 12% 1fr;
  gap: 5px;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  padding: 3px;
  border-radius: 8px;
}

.event-detail-form-field {
  margin: 0;
  padding: 2px;
  border-radius: 8px;
  font-size: 14px;
}

.event-detail-form-field mat-label {
  font-size: 16px;
  color: black;
  font-weight: bold;
}

.event-detail-form-field input,
.event-detail-form-field mat-select {
  font-size: 0.9rem;
  background-color: lavender;
  color: #6c49b9;
  place-items: center;
  border-radius: 6px;
  padding: 3px;
  line-height: 1.4;
  text-align: center;
}

.mat-mdc-form-field-subscript-wrapper,
.mat-mdc-form-field-bottom-align,
.mat-mdc-form-field-hint-wrapper,
.mat-mdc-form-field-hint-spacer {
  display: none !important;
}

.scroll-container {
  flex: 1;
  overflow-y: auto;
  width: calc(100% - 30px);
  height: 100%;
  position: relative;
  scroll-snap-type: y mandatory;
}

.event-detail-container {
  width: 100%;
  margin: 0 !important;
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 13px;
  justify-content: center;
  box-sizing: border-box;
  height: 96%;
  min-height: 96%;
  padding: 5px 10px;
  scroll-snap-align: start;
  position: relative;
  z-index: 1;
}

.second-screen-wrapper {
  width: 100%;
  margin: 0 0 4% 0;
  height: 96%;
  min-height: 96%;
  overflow: hidden;
  position: relative;
  scroll-snap-align: start;
  padding: 5px 10px 8px 10px;
}

.second-screen {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 50% 50%;
  grid-template-rows: 50% 50%;
  gap: 13px;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
  z-index: 2;
}

.quadrant {
  border: 1.5px solid #b6b6b6;
  border-radius: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
  transition: box-shadow 0.2s ease-in-out;
  overflow: hidden;
  position: relative;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
}

.quadrant:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quadrant-header {
  position: absolute;
  padding: 0 12px;
  font-weight: bold;
  font-size: 16px;
  color: #3f2876;
  top: 0;
  left: 5px;
  z-index: 1;
  pointer-events: none;
}

.quadrant-header-wrapper {
  display: flex;
  justify-content: start;
  padding-left: 20px;
}

.quadrant-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px 10px;
  z-index: 0;
}

.quadrant-footer {
  background-color: #b7bbe3;
  color: white;
  text-align: center;
  width: 100%;
  position: sticky;
  bottom: 0;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  z-index: 1;
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 5px 0;
}

.tiny-fab {
  background-color: #6d4db6 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  height: 20px;
  padding: 0 25px;
  border-radius: 15px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.tiny-fab:hover {
  background-color: #5a3a9b !important;
  transform: scale(1.05);
}

.tiny-fab mat-icon {
  font-size: 14px;
  align-content: center;
  margin-right: 4px;
  vertical-align: middle;
}

.tiny-fab.mat-button-disabled,
.tiny-fab:disabled {
  pointer-events: auto !important;
  background-color: rgba(109, 77, 182, 0.5) !important;
  color: #e0e0e0 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  transform: none !important;
}

.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 10;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
  pointer-events: auto;
}

.resize-handle.vertical {
  top: 2%;
  width: 10px;
  height: 96%;
  cursor: col-resize;
}

.resize-handle.horizontal {
  left: 1%;
  width: 98%;
  height: 10px;
  cursor: row-resize;
}

.resize-handle-first {
  z-index: 3;
}

.resize-handle-second {
  z-index: 4;
}

.resize-handle:hover {
  background-color: #e6e6fa;
  box-shadow: 0 0 8px rgba(230, 230, 250, 0.6);
}

.resize-handle:active {
  z-index: 11;
  background-color: rgba(230, 230, 250, 0.8);
}

.resize-handle::before {
  content: '';
  display: block;
  margin: auto;
  background-color: rgba(0, 0, 0, 0.2);
}

.resize-handle.vertical::before {
  width: 2px;
  height: 100%;
}

.resize-handle.horizontal::before {
  width: 100%;
  height: 2px;
}

.resize-handle.vertical::after {
  content: '↔';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: black;
  opacity: 0.5;
  transition: opacity 0.3s ease, color 0.3s ease;
  pointer-events: none;
}

.resize-handle.horizontal::after {
  content: '↕';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: black;
  opacity: 0.5;
  transition: opacity 0.3s ease, color 0.3s ease;
  pointer-events: none;
}

.resize-handle:hover::after {
  opacity: 1;
  color: #6d4db6;
}

.right-sidebar {
  position: fixed;
  top: 150px;
  right: 0;
  width: 30px;
  height: calc(100vh - 175px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background-color: #a56cc1;
  border-radius: 20px 0 0 20px;
  z-index: 1001;
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
}

.sidebar-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  writing-mode: vertical-rl;
  text-align: center;
  width: 30px;
  background-color: #a56cc1;
}

.sidebar-tab:last-child {
  border-bottom: none;
}

.sidebar-tab mat-icon {
  color: #FF7518;
  font-size: 18px;
  margin-bottom: 5px;
  transform: rotate(90deg);
  transition: all 0.3s ease;
}

.sidebar-tab:hover {
  background-color: #6d4db6;
  width: 33px;
  transform: translateX(-3px);
}

.sidebar-tab.active {
  background-color: #3f2876;
  width: 33px;
  transform: translateX(-3px);
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.2);
}

.sidebar-tab.active mat-icon {
  color: #FF7518;
}

.event-content {
  width: 100%;
  padding: 0;
  height: auto;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background: linear-gradient(135deg, #f9f9f9, #eaeaea);
  border: 2px dashed #ccc;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #555;
  text-align: center;
  margin: 20px 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  transition: all 0.3s ease;
  max-width: 100%;
  height: 60%;
}

.no-data-message:hover {
  background: linear-gradient(135deg, #ededed, #dcdcdc);
  border-color: #999;
  color: #333;
  transform: scale(1.01);
}

.no-data-message {
  animation: fadeIn 0.4s ease-in;
}

.custom-sidenav-panel {
  right: 30px;
  height: 55%;
  max-width: 40%;
  width: 100%;
  background-color: #fafafa;
  border-radius: 12px 0 0 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  padding: 10px 12px;
  position: fixed;
  top: calc(80px + 22.5vh);
}

.panel-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  position: sticky;
  top: 0;
  z-index: 20;
  background: linear-gradient(135deg, #3f2876, #6c49b9);
  color: white;
  height: 35px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 18px 12px;
  border-radius: 6px 6px 0 0;
}

.panel-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
  font-size: 18px;
  white-space: nowrap;
}

.panel-content.note-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.note-box {
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.note-box:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.panel-header {
  padding: 1.2rem;
  background: linear-gradient(135deg, #6c49b9, #8a5ed5);
  color: #fff;
  font-weight: 600;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.panel-title {
  font-size: 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.flt-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0.65rem 0.5rem;
  box-sizing: border-box;
}

.flight-table-container {
  flex: 1 1 auto;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.full-height-table {
  height: 100%;
  display: table;
  width: 100%;
}

.mat-table-custom {
  width: 100%;
  table-layout: fixed;
  border: 0.5px solid #d3d3d3;
  border-radius: 6px;
  background-color: #fff;
  font-size: 11px;
  border-collapse: collapse;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.mat-table-custom th,
.mat-table-custom td {
  padding: 8px 5px;
  border-bottom: 0.5px solid #d3d3d3;
  border-right: 0.5px solid #d3d3d3;
  text-align: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-size: 13px;
  height: auto;
  transition: background-color 0.3s ease, transform 0.1s ease, border-color 0.3s ease, border-width 0.3s ease;
}

.mat-table-custom th {
  position: sticky;
  top: 0;
  z-index: 2;
  background: linear-gradient(to right, lightgray, #eaeaea);
  font-weight: 700;
  color: #333;
  font-size: 14px;
  border-bottom: 0.5px solid #d3d3d3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hover effect for Inbound and Affected Outbound rows */
.mat-table-custom tr.inbound-row:hover td,
.mat-table-custom tr.outbound-row:hover td {
  background-color: #FAE4D6;
  transform: scale(1.01);
  cursor: default;
  border-bottom: 1px solid #b0b0b0;
  border-right: 1px solid #b0b0b0;
}

/* Hover effect for actual outbound individual rows */
.actual-outbound-row:hover td:not(.actual-outbound-merged) {
  background-color: #FAE4D6;
  transform: scale(1.01);
  cursor: default;
  border-bottom: 1px solid #b0b0b0;
  border-right: 1px solid #b0b0b0;
}

/* Highlight actual outbound group when hovering category cell */
.mat-table-custom tbody:has(.actual-outbound-merged:hover) {
  .actual-outbound-row td {
    background-color: #FAE4D6;
    border-bottom: 1px solid #b0b0b0;
    border-right: 1px solid #b0b0b0;
  }

  .actual-outbound-merged {
    background-color: #FAE4D6;
    border-right: 1px solid #b0b0b0 !important;
  }
}

/* Highlight category cell when hovering any actual outbound row */
.mat-table-custom tbody:has(.actual-outbound-row:hover) {
  .actual-outbound-merged {
    background-color: #FAE4D6;
    border-right: 1px solid #b0b0b0 !important;
  }
}

.mat-table-custom tr:active td {
  transform: scale(0.99);
}

/* Column widths */
.mat-table-custom th:nth-child(1),
.mat-table-custom td:nth-child(1) {
  width: 27.5%;
}

.mat-table-custom th:nth-child(2),
.mat-table-custom td:nth-child(2) {
  width: 32.5%;
}

.mat-table-custom th:nth-child(3),
.mat-table-custom td:nth-child(3) {
  width: 20%;
}

.mat-table-custom th:nth-child(4),
.mat-table-custom td:nth-child(4) {
  width: 20%;
}

/* Category cell */
.merged-category {
  font-weight: 700;
  font-size: 13px;
  color: #6c49b9;
  border-right: 0.5px solid #d3d3d3 !important;
  vertical-align: middle;
  position: relative;
  border-bottom: none !important; /* 👈 Remove table bottom border */
}

/* Keep the subtle purple underline for category cell */
.merged-category::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10px;
  right: 10px;
  height: 2px;
  background: #6c49b9;
  opacity: 0.2;
}

/* Special case: remove top border when rowspan is active */
.actual-outbound-merged {
  border-top: none !important;
  border-right: 0.5px solid #d3d3d3 !important;
}

/* Remove bottom border on first actual outbound cell to simulate rowspan */
tr:has(.actual-outbound-merged) td.merged-category {
  border-bottom: none !important;
}

/* Ensure last actual outbound row has border */
.mat-table-custom tr:last-child td.actual {
  border-bottom: 0.5px solid #d3d3d3 !important;
}

.mat-table-custom tr:last-child td.actual.hovered {
  border-bottom: 1px solid #b0b0b0 !important;
}

/* Cell coloring */
.inbound {
  color: green;
  font-weight: 700;
}

.outbound {
  color: red;
  font-weight: 700;
}

.actual {
  color: #007bff;
  font-weight: 700;
}

.table-scroll-wrapper {
  flex: 1;
  overflow-y: auto;
  margin: 10px 0;
  padding: 0 0.5rem;
}

.custom-parts-table {
  width: 100%;
  table-layout: fixed;
  font-size: 12px;
  border-collapse: collapse;
  padding: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-parts-table th,
.custom-parts-table td {
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #eee;
  border-left: 0.5px solid rgba(224, 224, 224, 0.5);
  border-right: 0.5px solid rgba(224, 224, 224, 0.5);
  word-wrap: break-word;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.custom-parts-table th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(to right, lightgray, #eaeaea);
  font-weight: 600;
  color: #333;
  border-left: 0.5px solid rgba(211, 211, 211, 0.5);
  border-right: 0.5px solid rgba(211, 211, 211, 0.5);
}

.custom-parts-table tr:hover td {
  background-color: #FAE4D6;
  font-weight: 600;
  border-left: 0.5px solid rgba(199, 199, 199, 0.5);
  border-right: 0.5px solid rgba(199, 199, 199, 0.5);
  cursor: pointer;
}

.custom-parts-table th:nth-child(1),
.custom-parts-table td:nth-child(1) {
  width: 25%;
}

.custom-parts-table th:nth-child(2),
.custom-parts-table td:nth-child(2) {
  width: 18%;
}

.custom-parts-table th:nth-child(3),
.custom-parts-table td:nth-child(3) {
  width: 57%;
}

.selected-row {
  background-color: #FAE4D6 !important;
  color: black !important;
  font-weight: 600 !important;
}

::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
  background-color: transparent !important;
  padding: 0 5px !important;
}

@media (max-width: 768px) {
  .event_details.card {
    margin-bottom: 5px;
  }

  .scroll-container {
    height: 100%;
  }

  .event-detail-container {
    grid-template-columns: 100%;
    grid-template-rows: repeat(2, auto);
    gap: 10px;
    height: auto;
    min-height: auto;
  }

  .second-screen-wrapper {
    height: auto;
    min-height: auto;
  }

  .second-screen {
    grid-template-columns: 100%;
    grid-template-rows: repeat(4, auto);
    gap: 10px;
    height: auto;
    min-height: auto;
  }

  .quadrant {
    margin-bottom: 10px;
    height: auto;
    max-height: none;
  }

  .navbar-row-1,
  .navbar-row-2 {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    height: auto;
    padding: 5px;
  }

  .right-sidebar {
    width: 25px;
    height: calc(100vh - 180px);
  }

  .sidebar-tab {
    width: 25px;
    font-size: 12px;
  }

  .sidebar-tab:hover {
    width: 30px;
    transform: translateX(-5px);
  }

  .sidebar-tab mat-icon {
    font-size: 16px;
  }

  .resize-handle.vertical::after,
  .resize-handle.horizontal::after {
    font-size: 14px;
  }

  .custom-sidenav-panel {
    height: 70%;
    top: calc(90px + 15vh);
  }
}

@media (max-width: 480px) {
  .scroll-container {
    height: 100%;
  }

  .event-detail-form-field {
    margin: 2px;
  }

  .tiny-fab {
    font-size: 8px;
    height: 18px;
    padding: 0 15px;
  }

  .tiny-fab mat-icon {
    font-size: 12px;
  }

  .right-sidebar {
    width: 20px;
    height: calc(100vh - 200px);
  }

  .sidebar-tab {
    width: 20px;
    font-size: 10px;
  }

  .sidebar-tab:hover {
    width: 25px;
    transform: translateX(-5px);
  }

  .sidebar-tab mat-icon {
    font-size: 14px;
  }

  .resize-handle.vertical::after,
  .resize-handle.horizontal::after {
    font-size: 12px;
  }

  .custom-sidenav-panel {
    height: 80%;
    top: calc(100px + 10vh);
  }
}