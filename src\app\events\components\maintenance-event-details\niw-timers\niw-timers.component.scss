.layout-style {
  display: flex;
  margin-top: 1%;
}

.selected {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.timer-button {
  background:#6c49b9;
  color: white;
  width: 30px;
  height: 30px;
}

.cell-style {
  text-align: center;
  font-weight: bold;
  color: #7f7b7d;
}

.timerStarted {
  color: #FF7518 !important;
}

.timerStopStyle {
  background-color: #FF7518 !important;
}

.table-styles {
  overflow-y: auto;
  border: 2px solid #6c49b9;
  border-radius: 7px;
}

.data-fields {
  display: flex;
}

.fieldset-1 {
  width: 50%;
  margin-left: 1%;
}

.fieldset-1 {
  width: 50%;
  margin-left: 2%;
}

.fieldset-1 p {
  margin-bottom: 1.5rem;
}

.fieldset-2 p {
  margin-bottom: 1.5rem;
}

.fieldset-1 p span {
  font-weight: bold;
  color: #7f7b7d;
}

.fieldset-2 p span {
  font-weight: bold;
  color: #7f7b7d;
}

.fieldset-2 {
  width: 50%;
  margin-left: 2%;
}

.comments p {
  margin-bottom: 1.5rem;
  margin-left: 2%;
}

.comments p span{
  font-weight: bold;
  color: #7f7b7d;
}

.stickyHeader {
  position: sticky;
  top: 0;
  z-index: 3;
  background: #6c49b9 !important;
  color: white;
}

.button-style {
  width: 15%;
  align-content: space-around;
  margin-top: 3%;
}

.niw-timer-heading {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  padding: 5px 20px;
  border-radius: 10px;
}

.niw_timer_button {
  background: rgb(175, 146, 146) !important;
  color: white !important;
}

.disabled {
  background: rgb(175, 146, 146, 0.5) !important;
}

.custom-dialog-container .mat-mdc-dialog-surface {
  height: auto !important;
}

.outer-button-div {
  width: 15%;
  margin-top: 1%;
  margin-bottom: 1%;
  text-align: center;
}

.heading-1 {
  margin-bottom: 0px;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white;
  font-size: large;
  font-weight: bold;
}

.details-0 {
  display: flex;
  margin-top: 1%;
  margin-left: 1%;
  margin-right: 1%;
}

.section-2 {
  display: flex;
}