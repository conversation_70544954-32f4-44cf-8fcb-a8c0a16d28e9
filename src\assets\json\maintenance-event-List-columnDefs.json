[{"field": "acn", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "ACN"}, "hide": false}, {"field": "fleetDesc", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Fleet"}, "hide": false}, {"field": "station", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Sta"}, "hide": false}, {"field": "status", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Status"}, "cellRenderer": "customMaintenanceEventListStatus", "hide": false}, {"field": "owner", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Owner"}, "hide": false}, {"field": "flightDetails", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Flt Out/Dt"}, "hide": false}, {"field": "curComment", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Comment"}, "hide": false}, {"field": "startDateTime", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Start"}, "hide": false}, {"field": "flightDepartureDetails", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Dept"}, "hide": false}, {"field": "eticDateTime", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "ETIC"}, "cellRenderer": "customMaintenanceEventListEtic", "hide": false}, {"field": "ost", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "OST"}, "hide": false}, {"field": "durationData", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "Duration"}, "hide": false}, {"field": "alerts", "sortable": true, "filter": true, "resizable": true, "headerComponent": "customMaintenanceEventListHeader", "headerComponentParams": {"displayName": "<PERSON><PERSON><PERSON>"}, "cellRenderer": "customMaintenanceEventListAlerts", "valueGetter": "(params) => params.data?.requestStatus", "comparator": "this.pinRowsComparator", "hide": false}, {"field": "settings", "suppressSorting": true, "suppressMenu": true, "width": 0, "suppressFilter": true, "headerComponent": "customiseSettingsIconComponent", "pinned": "right", "cellStyle": {"border-left": "none", "border-right": "none"}, "lockPosition": true}]