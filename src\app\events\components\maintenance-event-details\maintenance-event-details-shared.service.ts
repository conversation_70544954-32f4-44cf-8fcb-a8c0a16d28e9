import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DiscrepanciesList } from '../../dao/discrepancies-listDao';
import { linkedDiscrepancyUpdTxt } from '../../dao/discrepancyUpdTxt';
import { TubFileNotesResponseDto } from '../../dto/TubFileNotesResponseDto';

@Injectable({
  providedIn: 'root'
})
export class MaintenanceEventDetailsSharedService {
  constructor() { }
  discrepancyTuples: Array<[type: string, text: string]> = [['', '']];

  private linkedDiscrepanciesSubject = new BehaviorSubject<DiscrepanciesList[]>([]);
  linkedDiscrepancies$ = this.linkedDiscrepanciesSubject.asObservable();

  private affectedOutboundFlightLeg = new BehaviorSubject<string>('');
  affectedOutboundFlightLeg$ = this.affectedOutboundFlightLeg.asObservable();

  private affectedOutboundFlightDate = new BehaviorSubject<string>('');
  affectedOutboundFlightDate$ = this.affectedOutboundFlightDate.asObservable();

  private affectedOutboundFlightNum = new BehaviorSubject<string>('');
  affectedOutboundFlightNum$ = this.affectedOutboundFlightNum.asObservable();

  private affectedFlightDetails = new BehaviorSubject<string>('');
  affectedFlightDetails$ = this.affectedFlightDetails.asObservable();
  
  private linkedDiscrepancyUpdtTxts = new BehaviorSubject<linkedDiscrepancyUpdTxt[]>([]);
  linkedDiscrepancyUpdtTxts$ = this.linkedDiscrepancyUpdtTxts.asObservable();

  private tfNotesSubject = new BehaviorSubject<TubFileNotesResponseDto[]>([]);
  tfNotes$ = this.tfNotesSubject.asObservable();

  setAffectedFlightDetails(flightLeg: string, flightDate: string, flightNum: string) {
    this.affectedOutboundFlightLeg.next(flightLeg);
    this.affectedOutboundFlightDate.next(flightDate);
    this.affectedOutboundFlightNum.next(flightNum);
  }

  setLinkedDiscrepancies(linkedDiscrepancies: DiscrepanciesList[]) {
    this.linkedDiscrepanciesSubject.next(linkedDiscrepancies);
  }

  setLinkedDsicrpancyUpdtTxt(linkedDscrpUpdtTxt: linkedDiscrepancyUpdTxt[]){
    this.linkedDiscrepancyUpdtTxts.next(linkedDscrpUpdtTxt);
  }

  getAffectedFlightDetails(): string {
    const flightLeg = this.affectedOutboundFlightLeg.getValue();
    const flightDate = this.affectedOutboundFlightDate.getValue();
    const flightNum = this.affectedOutboundFlightNum.getValue();
    const details = [];
    if (flightNum) details.push(`${flightNum}`);
    if (flightDate) details.push(`${this.formatOpenDate(flightDate)}`);
    if (flightLeg) details.push(`${flightLeg}`);
    return details.join('/');

  }

  formatOpenDate(dateString: string): string {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const dateObj = new Date(dateString);
    const day = dateObj.getDate().toString().padStart(2, '0'); // Ensures two-digit format
    const month = months[dateObj.getMonth()]; // Gets month abbreviation
    const year = dateObj.getFullYear().toString().slice(-2); // Gets last two digits of the year

    return `${day}${month}${year}`;
  }

  setTubeFileNotes(tfNotes: TubFileNotesResponseDto[]) {
    this.tfNotesSubject.next(tfNotes);
  }
}
