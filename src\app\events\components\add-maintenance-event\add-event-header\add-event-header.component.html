<div class="header-container" [ngClass]="{'header-centered': !isValidData, 'header-normal': isValidData}">
  <mat-card class="header-card">
    <mat-card-title class="header-title">Add New Maintenance Event</mat-card-title>
    <mat-card-content>
      <p *ngIf="!isValidData" class="note-text">To initiate the creation of a new maintenance event, please select a valid ACN and Event Type.</p>
      <form class="header-form" [formGroup]="myForm" (ngSubmit)="onSubmit()">
        <div class="input-group">
          <mat-form-field appearance="outline" class="header-input">
            <mat-label class="mat-label">ACN</mat-label>
            <input matInput type="number" formControlName="acn" placeholder="Enter ACN" />
            <mat-error *ngIf="myForm.get('acn')?.hasError('required')">ACN is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="header-input">
            <mat-label class="mat-label">Event Type</mat-label>
            <mat-select formControlName="eventType" placeholder="Select Event Type">
              @for (event of eventType; track event) {
                <mat-option [value]="event">{{event}}</mat-option>
              }
            </mat-select>
            <mat-error *ngIf="myForm.get('eventType')?.hasError('required')">Event Type is required</mat-error>
          </mat-form-field>

          <div class="button-group" *ngIf="isValidData">
            <button class="header-button save-button" [ngClass]="{'disabled': isDisabled}" (click)="callChildSubmitAndReset('save')" mat-raised-button [disabled]="isDisabled">Add Event</button>
            <button class="header-button reset-button" (click)="callChildSubmitAndReset('reset')" mat-raised-button>Reset</button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>

<div *ngIf="!isValidData" class="note-container">
  <p class="note-text">To initiate the creation of a new maintenance event, please select a valid ACN and Event Type.</p>
</div>

<div *ngIf="isValidData" class="content-container">
  <app-add-maintenance-event [acn]="passOnAcn" [eventtype]="passOnEventType" (dataFilled)="onValidForm($event)"></app-add-maintenance-event>
</div>