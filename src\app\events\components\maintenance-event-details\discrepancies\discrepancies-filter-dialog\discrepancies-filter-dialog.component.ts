import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';

@Component({
  selector: 'app-discrepancies-filter-dialog',
  templateUrl: './discrepancies-filter-dialog.component.html',
  styleUrl: './discrepancies-filter-dialog.component.scss',
  standalone: false
})
export class DiscrepanciesFilterDialogComponent implements OnInit {

  selectedTimeFrame: string = 'all';
  selectedDiscrepancyTypeValues: string[] = [];
  enableApplyFilterButton: boolean = true;
  daysInput!: number|undefined;
  fromDate!: Date|undefined;
  toDate!: Date|undefined;
  error: boolean = false;
  errorMessage: string = "";

  isAllOptionSelected:boolean = true;
  isDaysOptionSelected: boolean = false;
  isTimePeriodOptionSelected: boolean = false;

  // Discrepancy Types
  pdis: boolean = true;
  smis: boolean = true;
  lmpi: boolean = true;
  ierr: boolean = false;
  mdis: boolean = true;
  mtsi: boolean = true;
  ndis: boolean = false;

  constructor(private dialogRef: MatDialogRef<DiscrepanciesFilterDialogComponent>,
              private maintenanceEventDetailsService: MaintenanceEventDetailsService
              ) {}

  ngOnInit() {
    this.fetchSessionStorageValues();
    this.selectedDiscrepancyTypes();
    this.checkApplyButtonState();
  }

  fetchSessionStorageValues() {
    const discrepanciesFilterSessionStorage = this.maintenanceEventDetailsService.getDiscrepanciesFilterFromSessionStorage();
    if (discrepanciesFilterSessionStorage && Object.keys(discrepanciesFilterSessionStorage).length > 0) {
      this.selectedTimeFrame = discrepanciesFilterSessionStorage.selectedTimeFrame;
      this.daysInput = discrepanciesFilterSessionStorage.daysInput;
      this.fromDate = new Date(discrepanciesFilterSessionStorage.fromDate);
      this.toDate = new Date(discrepanciesFilterSessionStorage.toDate);
      this.pdis = discrepanciesFilterSessionStorage.discrepancies.pdis;
      this.smis = discrepanciesFilterSessionStorage.discrepancies.smis;
      this.lmpi = discrepanciesFilterSessionStorage.discrepancies.lmpi;
      this.ierr = discrepanciesFilterSessionStorage.discrepancies.ierr;
      this.mdis = discrepanciesFilterSessionStorage.discrepancies.mdis;
      this.mtsi = discrepanciesFilterSessionStorage.discrepancies.mtsi;
      this.ndis = discrepanciesFilterSessionStorage.discrepancies.ndis;
      this.timeFrameChange();
    }
  }

  // Common function to restrict dates between 25th October 1999 and today
  dateRangeFilter = (d: Date | null): boolean => {
    if (!d) return false;
    const minDate = new Date('1999-10-24');
    const maxDate = new Date();
    return d >= minDate && d <= maxDate;
  };

  timeFrameChange() {
    this.isAllOptionSelected = this.selectedTimeFrame === 'all';
    this.isDaysOptionSelected = this.selectedTimeFrame === 'days';
    this.isTimePeriodOptionSelected = this.selectedTimeFrame === 'timePeriod';
    this.selectedDiscrepancyTypes();
    this.checkApplyButtonState();
  }

  selectedDiscrepancyTypes() {
    this.selectedDiscrepancyTypeValues = [];
    if (this.pdis) this.selectedDiscrepancyTypeValues.push('pdis');
    if (this.smis) this.selectedDiscrepancyTypeValues.push('smis');
    if (this.lmpi) this.selectedDiscrepancyTypeValues.push('lmpi');
    if (this.ierr) this.selectedDiscrepancyTypeValues.push('ierr');
    if (this.mdis) this.selectedDiscrepancyTypeValues.push('mdis');
    if (this.mtsi) this.selectedDiscrepancyTypeValues.push('mtsi');
    if (this.ndis) this.selectedDiscrepancyTypeValues.push('ndis');
    this.checkApplyButtonState();
  }

  checkApplyButtonState() {
    if (this.selectedDiscrepancyTypeValues.length > 0) {
      this.error = false;
      this.errorMessage = "";
      if (this.isAllOptionSelected) {
        this.daysInput = this.fromDate = this.toDate = undefined;
        this.enableApplyFilterButton = true;
      } else if (this.isDaysOptionSelected) {
        this.fromDate = this.toDate = undefined;
        const isDaysInputValid = this.daysInput && this.daysInput > 0;
        this.enableApplyFilterButton = isDaysInputValid ? true : false;
        if (!this.enableApplyFilterButton && this.daysInput) {
          this.errorMessage = "Enter valid days";
          this.error = true;
        }
      } else if (this.isTimePeriodOptionSelected) {
        this.daysInput =  undefined;
        if (this.toDate && this.fromDate && this.toDate >= this.fromDate) {
          const isBeginDateValid = this.fromDate instanceof Date && !isNaN(this.fromDate.getTime());
          const isEndDateValid = this.toDate instanceof Date && !isNaN(this.toDate.getTime());
          this.enableApplyFilterButton = isBeginDateValid && isEndDateValid ? true : false;
        } else {
          if (this.fromDate && this.toDate) {
            this.enableApplyFilterButton = false;
            this.errorMessage = "End date greater than start date";
            this.error = true;
          }
        }
      }
    } else {
      this.enableApplyFilterButton = false;
      this.errorMessage = "Select any one discrepancy type";
      this.error = true;
    }
  }  

  apply() {
    // Send selected data to the parent component
    const data = {
      selectedTimeFrame: this.selectedTimeFrame,
      daysInput: this.daysInput,
      fromDate: this.fromDate,
      toDate: this.toDate,
      discrepancies: {
        pdis: this.pdis,
        smis: this.smis,
        lmpi: this.lmpi,
        ierr: this.ierr,
        mdis: this.mdis,
        mtsi: this.mtsi,
        ndis: this.ndis
      }
    };
    this.maintenanceEventDetailsService.saveDiscrepanciesFilterInSessionStorage(data);
    this.dialogRef.close(data);  // Pass the data to the parent component
  }

  cancel() {
    this.dialogRef.close();  // Close dialog without returning data
  }

  validateDaysInput() {
    if (this.daysInput !== undefined && this.daysInput < 1) {
      this.daysInput = 0; // Reset invalid value
    }
  }  

}
