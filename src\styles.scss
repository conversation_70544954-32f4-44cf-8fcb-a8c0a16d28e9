
// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.elevation-classes();
@include mat.app-background();

// Define the theme object.
$mets-server-nextgen-ui-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$azure-palette,
    tertiary: mat.$blue-palette,
  ),
  density: (
    scale: 0,
  )
));

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
:root {
  @include mat.all-component-themes($mets-server-nextgen-ui-theme);
}

// Comment out the line below if you want to use the pre-defined typography utility classes.
// For more information: https://material.angular.io/guide/typography#using-typography-styles-in-your-application.
// @include mat.typography-hierarchy($mets-server-nextgen-ui-theme);

// Comment out the line below if you want to use the deprecated `color` inputs.
// @include mat.color-variants-backwards-compatibility($mets-server-nextgen-ui-theme);
/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

.editTimer-custom-dialog {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.addTimer-custom-dialog {
  width: 60%;
  margin-left: auto;
  margin-right: auto;
  height: 50vh;
}

.ag-ltr .ag-cell {
  display: flex;
  justify-content: center;
  border-right-width: 1px;
}

.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
  border-color: #3F2876 !important;
  background-color: #6c49b9 !important;
}

.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input {
  color: black;
  font-weight: bolder !important;
}

.mat-mdc-select-value {
  color:black !important;
  font-weight: bolder !important;
}

.ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.ag-pinned-right-header {
  // background-color: #D2C6E1 !important;
  background-color: #B7BBE3;
  font-size: 22px;
}

.toast-container .ngx-toastr {
  white-space: nowrap;
  width: auto !important;
  max-width: 100% !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 12px 24px 12px 16px; // left 16px, right 24px
}

::ng-deep .custom-msn-dialog .mat-dialog-container {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
}
.mat-mdc-form-field-subscript-wrapper,
  .mat-mdc-form-field-bottom-align,
  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-hint-spacer{
    display: none !important;
  }

.custom-tub-dialog .mat-mdc-dialog-container .mat-mdc-dialog-title{
  background-color:#3F2876 !important; 
  color: white;;
  font-style: bold;
  padding: 5px;
  border-radius: 8px;
  text-align: center;
}
.custom-note-dialog .mat-mdc-dialog-container .mat-mdc-dialog-title{
  background-color:#3F2876 !important; 
  color: white;;
  font-style: bold;
  padding: 5px;
  border-radius: 8px;
  text-align: center;
}
.custom-note-dialog  .mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content {
  margin-left: 25%;
}


.custom-tub-dialog .mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end, .mat-mdc-dialog-action {
  margin-top: 10px;
  background-color:#3F2876 !important;
}
.custom-tub-dialog .mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content {
  padding: 0;
}

.custom-tub-dialog .file-upload {
  margin-top: 20px;
  color: black;
}

.custom-tub-dialog input[type="file"] {
  margin-top: 5px;
}

.custom-tub-dialog .mat-dialog-actions button {
  margin-left: 8px;
}
.custom-tub-dialog .mat-mdc-raised-button .mat-mdc-button-touch-target{
  background-color: #ff6600  !important;
  color: white !important;
  border-radius: 25px !important; /* makes the button curved */
  padding: 6px 20px !important;
  font-weight: bold;
  text-transform: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s, color 0.3s;
}
.custom-tub-dialog .mdc-button__label{
  color: white;
}
.custom-tub-dialog .mat-mdc-raised-button:hover .mat-mdc-button-touch-target{
   background-color: #ffa366 !important;
}
.custom-tub-dialog .mat-mdc-raised-button:hover .mdc-button__label{
  color: black !important;
}
.ql-toolbar.ql-snow {
    border: 1px solid;
    background-color: white !important;
    padding: 0 !important;
}
.ql-editor {
  background-color: lightgray;
  text-align: left;
  text-transform: lowercase;
  caret-color: #6c49b9; 
}

.ql-editor:focus::selection {
  background-color: rgba(108, 73, 185, 0.2);
}



.custom-tub-dialog .ql-container{
  font-size: 20px !important;
  background-color: #ffa366 !important;
}
.custom-tub-email-dialog  .mat-mdc-dialog-actions{
  // display: inline !important;
  justify-content: center !important;
}
.custom-tub-dialog  .mat-mdc-dialog-actions{
  // display: inline-block !important;
  background-color: #3F2876;
  justify-content: center !important;
  padding: 0;
}
.custom-note-dialog  .mat-mdc-dialog-actions{
  // display: inline-block !important;
  background-color: #3F2876;
  justify-content: center !important;
  padding: 0;
}
.custom-tub-email-dialog .mat-mdc-dialog-surface{
  height: max-content !important;
}
.custom-tub-email-dialog .mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{
  overflow: hidden !important;
}
.custom-tub-email-dialog .mat-mdc-form-field{
  margin-bottom: 10px !important;
  font-size: small !important;
  margin-bottom: auto !important;
}
.mat-mdc-select-value-text {
  font-size: 0.9rem;
  color: #6c49b9;
}

.custom-addEvent-confirmation-dialog-container {
  width: 50vw !important;
  min-width: 50vw !important;
  max-width: 90vw !important;
  position: static;
  margin: 0 auto;
  box-sizing: border-box;
}