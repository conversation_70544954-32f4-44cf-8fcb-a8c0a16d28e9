.container {
    position: relative;
    padding: 0 5px;
    width: 100%;
    margin: 0;
    max-width: none;
}

.intakeFormTitle {
    text-align: center;
    padding: 8px;
    color: white;
    font-size: 18px;
    margin-bottom: 10px;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    background-repeat: no-repeat;
}

.form-container {
    background: #ffffff;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    min-width: 100%;
    text-align: center;
    transition: transform 0.2s ease-in-out, box-shadow 0.3s ease-in-out;
}

.radio-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f4f4f4;
    border-radius: 8px;
    margin: 10px 0 0 0;
}

.form-container:hover {
    box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.15);
}

.form-container span {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 10px;
    color: red;
}

.form-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    background: whitesmoke;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 5px;
    gap: 10px;
    position: relative;
}

.form-group:hover {
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

.question-label {
    flex: 1 1 40%;
    min-width: 200px;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    align-self: center;
    gap: 5px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    padding: 5px 10px;
    position: relative;
}

.question-label::after {
    content: '';
    position: absolute;
    right: 0;
    top: 5%;
    height: 90%;
    width: 2px;
    background: grey;
    display: block;
}

.answer-section {
    flex: 1 1 40%;
    min-width: 200px;
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    padding: 5px 10px;
    font-size: 14px;
}

.required-symbol {
    color: red !important;
    font-weight: bolder !important;
    margin-right: 5px !important;
    font-size: 18px !important;
}

.noColor {
    pointer-events: none !important;
    user-select: none !important;
    color: whitesmoke !important;
}

mat-form-field {
    width: 100%;
    font-size: 14px;
}

mat-checkbox,
mat-radio-button {
    font-size: 14px;
}

textarea {
    white-space: pre-wrap;
    resize: vertical;
    width: 100%;
    max-width: 100%;
    min-height: 60px;
    max-height: 200px;
    padding: 8px;
    font-size: 14px;
}

mat-checkbox-group,
mat-radio-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.date-range-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    justify-content: center;
    width: 100%;
}

.date-range-container .from-field {
    order: 1;
    flex: 0 1 auto;
    min-width: 120px;
    max-width: 180px;
}

.date-range-container .to-text {
    order: 2;
    font-size: 14px;
    font-weight: bold;
    color: black !important;
    margin: 0 5px;
}

.date-range-container .to-field {
    order: 3;
    flex: 0 1 auto;
    min-width: 120px;
    max-width: 180px;
}

.dateRangeFeild {
    width: 55% !important;
}

@media screen and (max-width: 400px) {
    .date-range-container {
        flex-direction: column;
        align-items: flex-start;
    }
    .date-range-container .from-field,
    .date-range-container .to-field {
        flex: 1 1 100%;
        max-width: 100%;
    }
  }

.submit-button-container {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper, 
:host ::ng-deep .mat-mdc-form-field-bottom-align::before {
    display: none !important;
}

.mdc-checkbox .mdc-checkbox__checkmark {
    display: flex;
    justify-content: center;
    place-self: center;
    width: 15px !important;
    height: 15px !important;
}