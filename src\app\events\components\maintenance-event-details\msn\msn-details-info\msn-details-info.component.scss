.dialog-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    // min-height: 700px;
    // min-width: 800px;
    background: white;
    overflow: hidden;
}

.dialog-header {
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    padding: 8px;
    color: white;
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    border-radius: 8px 8px 0 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.dialog-content {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    min-height: 0;
    padding: 10px 15px 0px 15px;
}

.table-accordion-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    min-height: 0;
}

.msn-table-wrapper {
    flex: 0 0 auto; /* Only take the space it needs */
    max-height: 300px; // or remove this if your table height is manageable
    overflow: auto;
}

.mat-table-custom {
  width: 100%;
  table-layout: fixed;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  font-size: 11px;
  border-collapse: separate;
  border-spacing: 0;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // ✨ Add this line

  th, td {
    padding: 6px 8px;
    border-bottom: 1px solid #eee;
    text-align: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 11px;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(to right, lightgray, #eaeaea);
    font-weight: 600;
    color: #333;
    font-size: 12px;
  }

  tr:hover {
    cursor: pointer !important;
    background-color: #fafafa;
  }
}

.msn-table-custom td {
  white-space: nowrap;      /* Prevent line wrap */
  overflow: hidden;         /* Hide overflow */
  text-overflow: ellipsis;  /* Add "..." at the end */
  max-width: 100px;         /* Optional: Control max width */
}

.mat-header-cell, .mat-cell {
    text-align: center;
    background-color: #e0d7f5;
    color: #3F2876;
    font-weight: 500;
}

.selected-row {
    background-color: #FAE4D6 !important;
    color: black !important;
    font-weight: 600 !important;
}

.accordion-container {
    flex: 1 1 auto;
    overflow-y: auto;
    min-height: 0;
    margin-top: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 4px;
    background-color: #f9f9f9;
}


.gradient-expansion-panel {
    margin: 10px 5px;
}

.gradient-expansion-header {
    background: linear-gradient(135deg, #3F2876, #6c49b9) !important;
    color: white !important; 
    min-height: 40px !important;
    border-radius: 10px 10px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .mat-panel-title {
        flex-grow: 1;
    }

    .expansion-icon {
        margin-left: auto;
        color: white;
        font-size: 30px;
        width: 30px;
        height: 30px;
        line-height: 30px;
    }

    .mat-dark-theme & {
        .expansion-icon {
            color: #bb86fc;
        }
    }
}

.mat-expansion-panel-header {
    padding-right: 16px;
}

.gradient-expansion-header:hover,
.gradient-expansion-header.mat-expanded,
.gradient-expansion-header.mat-focused,
.gradient-expansion-header.cdk-focused,
.gradient-expansion-header.cdk-program-focused {
    background: linear-gradient(135deg, #3F2876, #6c49b9) !important;
    color: white !important;
}

.gradient-expansion-header:focus-visible {
    box-shadow: none !important;
}

::ng-deep .mat-expansion-panel-header .mat-expansion-panel-header-title {
    color: white !important;
    display: flex;
    font-size: 14px;
    font-weight: 900;
    justify-content: center;
    width: fit-content;
    text-align: center;
    margin: 0 auto;
    transition: margin 0.3s ease-in-out, text-align 0.3s ease-in-out;
}

.custom-comments-table th:nth-child(1),
.custom-comments-table td:nth-child(1) {
    width: 15%;
}

.custom-comments-table th:nth-child(2),
.custom-comments-table td:nth-child(2) {
    width: 10%;
}

.custom-comments-table th:nth-child(3),
.custom-comments-table td:nth-child(3) {
    width: 75%;
}

:host ::ng-deep .mat-expansion-panel-body {
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    padding: 16px;
    color: white;
    // overflow-y: auto;
    // max-height: 300px; /* Adjust as needed to control scrollable area */
}

::ng-deep .mat-expansion-panel-body {
    padding: 0px 10px 10px !important;
}

.mat-accordion > .mat-expansion-panel-spacing:first-child {
    margin-top: 10px;
    border-radius: 12px;
    background-color: #f5f5f5;
}

.mat-accordion .mat-expansion-panel:not(.mat-expanded),
.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing) {
    border-radius: 10px;
}

.no-data-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
    background: linear-gradient(135deg, #f2f2f2, #e6e6e6);
    border: 1px dashed #bbb;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    text-align: center;
    margin-top: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.no-data-message:hover {
    background: linear-gradient(135deg, #e8e8e8, #dcdcdc);
    border-color: #999;
    color: #444;
}

.dialog-footer {
    flex-shrink: 0;
    background: white;
    padding: 8px 16px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #ddd;
    z-index: 10;
}

.closeButton {
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    background: linear-gradient(135deg, #cc5200, #ff6600) !important;
    color: white !important;
    border-radius: 10px;
    min-width: 90px;
    height: 36px;
    font-size: 14px;
    padding: 6px 12px;
}

.custom-tab-group {
    flex: 1 1 auto;
    min-height: 0;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .mat-tab-body-wrapper {
        flex: 1 1 auto;
        min-height: 0;
        overflow: auto;
    }
}

.tab-content {
  padding: 10px;
  font-size: 14px;
}

.shipping-info-tab, .comments-tab {
  background-color: #f5f5f5;
  border-radius: 8px;
}

::ng-deep .custom-tab-group .mat-tab-label.mat-tab-label-active {
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: white !important;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

::ng-deep .custom-tab-group .mat-tab-label {
  color: rgba(0, 0, 0, 0.6); // Optional: normal tab color
}

:host ::ng-deep .custom-tab-group .mdc-tab {
  background-color: #f5f5f5;
  color: #333;
  border-radius: 4px 4px 0 0;
  margin-right: 4px;
}

:host ::ng-deep .custom-tab-group .mdc-tab--active {
  background: linear-gradient(135deg, #3F2876, #6c49b9) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

:host ::ng-deep .custom-tab-group .mdc-tab--active .mdc-tab__text-label {
  color: white !important;
  font-weight: bold !important;
}

.mat-table-custom.msn-table-custom {
  position: relative; /* needed for absolute positioning */
  width: 100%;
  overflow-x: auto;
}

.settings-column {
  width: 40px;
  padding: 0;
}

.settings-header-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}