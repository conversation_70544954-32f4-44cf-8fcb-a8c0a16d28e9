import { Component, Inject, ChangeDetectionStrategy, inject, EventEmitter, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AddEditTimersComponent } from '../add-edit-timers/add-edit-timers.component';
import { MatTableDataSource } from '@angular/material/table';
import moment from 'moment';
import { ToastrMessageService } from '../../../../../app-layout/services/toastr-message.service';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { DateAdapter } from '@angular/material/core';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
    selector: 'app-edit-timers',
    templateUrl: './edit-timers.component.html',
    styleUrl: './edit-timers.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class EditTimersComponent {

  reqPayloadTemplate: any = {
    'mode': "NIW_TIMERS",
    'requestType': "UPDATE",
    'userId': '',
    'eventId': '',
    'timerId': ''
  };

  reqUpdatePayloadTemplate: any = {
    'mode':'NIW_TIMERS',
    'flag':'DELETE',
  }

  @Output() updateEvent = new EventEmitter<any>();

  readonly dialog = inject(MatDialog);

  localOffset = new Date().getTimezoneOffset() * 60 * 1000;

  selectedRow: any = null;

  userId:string = '';
  timerName:string = '';
  timerId:string = '';
  eventId:string = '';

  ELEMENT_DATA: any[] = [];

  displayedColumns: string[] = ['Start', 'Stop', 'Duration'];
  dataSource = new MatTableDataSource<any>([]);

  constructor(private dialogRef: MatDialogRef<EditTimersComponent>, 
              private toastrMessageService: ToastrMessageService,
              @Inject(MAT_DIALOG_DATA) public data: any,
              private maintenanceEventDetailsService: MaintenanceEventDetailsService,
              private dateAdapter: DateAdapter<Date>) {
                this.dateAdapter.setLocale('en-US');
              }

  ngOnInit() {
    this.timerName = this.data.timerName;
    this.timerId = this.data.timerId;
    this.eventId = this.data.eventId;
    this.userId = this.data.userId;
    this.loadTimerData();
  }

  loadTimerData() {
    let data = { ...this.reqPayloadTemplate, 'userId': this.userId, 'eventId': this.eventId, 'timerId': this.timerId };
    this.maintenanceEventDetailsService.getNiwTimers(data).subscribe((result) => {
      this.generateTableData(result);
    });
  }

  generateTableData(result: any) {
    this.ELEMENT_DATA = [];
    let timerDetails = null;
    if(result.hasOwnProperty('timerDataList')) {
      timerDetails = result['timerDataList'];
    } else {
      timerDetails = result['UPDATED_TIMER_DETAILS'];

    }
    if(timerDetails != null) {
      let diffInMs = 0;
      for(let i of timerDetails) {
        let rowData: any = {};
        let startDtTm = i['timerStartDtTm'];
        let stopDtTm = i['timerStopDtTm'];
        if(stopDtTm != null) {
          const d1 = new Date(startDtTm);
          const d2 = new Date(stopDtTm);
          diffInMs = (d2.getTime() - d1.getTime()); 
        } else {
          const d1 = new Date(startDtTm - this.localOffset);
          const d2 = new Date();
          diffInMs = (d2.getTime() - d1.getTime());
        } 
        rowData['start'] = moment(startDtTm + this.localOffset).format("DDMMMYY HH:mm:ss");
        rowData['stop'] = (stopDtTm == null) ? "-" : moment(stopDtTm + this.localOffset).format("DDMMMYY HH:mm:ss");
        rowData['lastUpdateDtTm'] = moment(i['lastUpdateDtTm']).format("YYYY-MM-DD HH:mm:ss").toString();
        rowData['creationDtTm'] = moment(i['eventTimersPk']['creationDtTm'] + this.localOffset).format("YYYY-MM-DD HH:mm:ss").toString();
        rowData['duration'] = this.calculateTimeDiff(diffInMs);
        this.ELEMENT_DATA.push(rowData);
      }
    }
    this.dataSource.data = this.ELEMENT_DATA;
  }

  calculateTimeDiff(diffInMs: any) {
    const totalSeconds = Math.floor(diffInMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const formattedHours = hours < 10 ? `0${hours}` : hours;
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
    const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;
    return `${formattedHours}H:${formattedMinutes}M:${formattedSeconds}S`;
  }

  onDataChange(eventId:any, timerName:any, timerType:any ,duration: any): void {
    let changedData = {
      'eventId' : eventId,
      'timerName' : timerName,
      'duration' : duration,
      'type' : timerType
    }
    this.updateEvent.emit(changedData);
  }

  convertToMillis(time: string) {
    const regex = /(\d+)H:(\d{2})M:(\d{2})S/;
    const match = time.match(regex);
    if (match) {
      const hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const seconds = parseInt(match[3]);
      const totalMilliseconds = (hours * 3600000) + (minutes * 60000) + (seconds * 1000);
      return totalMilliseconds;
    } else {
      throw new Error("Invalid time format. Please use '00H:00M:00S'.");
    }
  }

  openEditDialog(row: any): void {
    this.selectedRow = row;
    let saveCreationDate = this.selectedRow['creationDtTm'];
    let saveDuration =this.convertToMillis(this.selectedRow['duration']);
    if(this.selectedRow.stop == '-') {
      this.toastrMessageService.warning('Running timer cannot be edited');
      return;
    }

    let timerData = {
      type: 'Edit',
      timerName: this.timerName,
      startDateTime: this.selectedRow.start,
      endDateTime: this.selectedRow.stop,
      timerId: this.timerId,
      eventId: this.eventId,
      userId: this.userId,
      lastUpdate: this.selectedRow['lastUpdateDtTm'],
      creationTime: this.selectedRow['creationDtTm']
    }

    const dialogRef = this.dialog.open(AddEditTimersComponent, {
      width: '30%',
      maxWidth: '100vw',
      height: '50%',
      data: timerData,
      panelClass: 'addTimer-custom-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result != null) {
        this.generateTableData(result);
      }
      if(result['UPDATED_TIMER_DETAILS']?.length != 0) {
        let flag = "ADD";
        let durationMills: any = null;
        for(let i of result['UPDATED_TIMER_DETAILS']) {
          if(moment(i['eventTimersPk']['creationDtTm']).format("YYYY-MM-DD HH:mm:ss").toString() == saveCreationDate) {
            durationMills = (new Date(i['timerStopDtTm']).getTime() - new Date(i['timerStartDtTm']).getTime());
            flag = (durationMills - saveDuration) > 0 ? 'ADD' : 'DELETE';
          }
        }
        this.onDataChange(this.eventId, this.timerName, flag , this.calculateTimeDiff(Math.abs(durationMills - saveDuration)));
      }
    });
  }

  openAddDialog(): void {
    let timerData = {
      type: 'Add',
      timerName: this.timerName,
      timerId: this.timerId,
      eventId: this.eventId,
      userId: this.userId
    };

    const dialogRef = this.dialog.open(AddEditTimersComponent, {
      data: timerData,
      width: '30%',       // Responsive width
      maxWidth: '100vw',   // Prevents it from exceeding the viewport
      height: '50%',
      panelClass: 'addTimer-custom-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result != null) {
        this.generateTableData(result);
      }
      if(result['UPDATED_TIMER_DETAILS']?.length != 0) {
        this.selectedRow = this.ELEMENT_DATA[this.ELEMENT_DATA.length-1];
        this.onDataChange(this.eventId, this.timerName, 'ADD' ,this.selectedRow['duration']);
      }
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }

  selectRow(row: any): void {
    if (this.selectedRow === row) {
      this.selectedRow = null;
      return;
    }
    this.selectedRow = row;
  }

  deleteTimer() {
    let data = { ...this.reqUpdatePayloadTemplate, 
      'user_id': this.userId,
      'token_id': this.userId,
      'event_active':true,
      'niw_timer_data':{
            'eventTimersPk': {
              'eventId': this.eventId,
              'creationDtTm': moment(this.selectedRow['creationDtTm']).format("YYYY-MM-DDTHH:mm:ss.SSS") 
            },
            'timerId': this.timerId,
            'lastUpdateDtTm': moment(this.selectedRow['lastUpdateDtTm']).format("YYYY-MM-DDTHH:mm:ss.SSS") 
        }
    }
    this.maintenanceEventDetailsService.updateNiwTimers(data).subscribe((data) => {
      this.generateTableData(data);
      this.toastrMessageService.success('Timer deleted successfully!');
      this.onDataChange(this.eventId, this.timerName, 'DELETE', this.selectedRow['duration']);
      this.selectedRow = null;
    });
  }

}
