<div class="managerNotesCaptureContainer">
    <div class="mgrNotesCaptureHeader">
        <h5 style="margin: 0; padding: 3%;">Manager Notes Capture</h5>
    </div>
    <div class="button-container">
        <div class="submit-button">
            <button mat-raised-button class="mat-button1" (click)="onSubmit()">Submit</button>
        </div>
        <div class="action-button">
            <button mat-raised-button class="mat-button1" (click)="onCancel()">Cancel</button>
        </div>
    </div>
    <div class="mgrNotesCaptureContent">
        <div *ngFor="let item of accordianData; let i = index">
            <button class="accordion" (click)="toggleDisplay(i)" [class.active]="openStates[i]">
                {{ item.title }} ({{ item.countOfObjects }})
                <span class="accordion-icon"></span>
            </button>
            <div class="panel" [style.display]="openStates[i] ? 'flex' : 'none'">

                <ng-container class="container" *ngIf="item.title === 'MSN'">
                    {{ discrepanciesData?.countOfObjects }}
                </ng-container>

                <ng-container class="container" *ngIf="item.title === 'Discrepancy'">
                    <div class="discrepancy-container">
                        <div *ngFor="let discrepancy of linkedDiscrepancies; let j = index">
                            <button class="discrepancy-drop-drown" (click)="toggleDiscrepancyDisplay(j)"
                                [class.active]="discrepancyOpenState[j]">
                                {{ discrepancy.discType }} {{ discrepancy?.ata }} {{ discrepancy?.number }}
                                {{ discrepancy?.status }} {{ discrepancy?.openStation }}
                                <span class="discrepancy-icon"></span>
                            </button>

                            <div class="discrepancy-content"
                                [style.display]="discrepancyOpenState[j] ? 'flex' : 'none'">
                                <div class="discrepancy-checkbox">
                                    <ng-container
                                        *ngFor="let updText of getUpdateTextsForDiscrepancy(discrepancy.number)">
                                        <div class="checkbox-card">
                                            <mat-checkbox
                                                (change)="onTextCheckboxChange(discrepancy, updText.formattedTxt, $event.checked)">
                                                <span class="formatted-text" [innerText]="updText.actualTxt"></span>
                                            </mat-checkbox>
                                        </div>
                                    </ng-container>
                                </div>

                                <!-- <div class="discrepancy-text">
                                    <strong>Original Text:</strong><br>
                                    <ng-container *ngFor="let txt of discrepancy.text">
                                        {{ txt }}<br>
                                    </ng-container>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </ng-container>
                <ng-container class="container" *ngIf="item.title === 'TFNotes'">
                    <div class="tfNotes-container">
                        <div *ngFor="let tfNote of tfNotes; let k = index">
                            <div class="checkbox-card">
                                <mat-checkbox
                                (change)="onTfNoteCheckboxChange(tfNote,$event.checked)">
                                    <div class="tfNote-main">
                                        <span class="formatted-text" [innerHTML]="tfNote.safeTfNote"></span>
                                    </div>
                                </mat-checkbox>
                                <div class="tfNote-footer">
                                    <span class="tf-metaData">{{ tfNote.lastUpdateDtTm | date: 'short' }}</span>
                                    <span class="tf-metaData">{{ tfNote.empName }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>                
            </div>
        </div>
    </div>
</div>