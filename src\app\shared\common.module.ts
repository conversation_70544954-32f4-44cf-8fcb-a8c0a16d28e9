import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbModule, NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AgGridAngular } from 'ag-grid-angular';
import { RouterModule } from '@angular/router';

@NgModule({
  imports: [
    CommonModule,
    NgbModule,
    NgbPopoverModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridAngular,
    RouterModule
  ],
  exports: [
    CommonModule,
    NgbModule,
    NgbPopoverModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridAngular,
    RouterModule
  ]
})
export class CommonLibrariesModule { }
