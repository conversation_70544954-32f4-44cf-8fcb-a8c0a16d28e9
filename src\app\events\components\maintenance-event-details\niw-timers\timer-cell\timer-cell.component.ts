import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { interval, Subscription } from 'rxjs';
import { DateAdapter } from '@angular/material/core';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
  selector: 'app-timer-cell',
  template: `{{ elapsedTime }}`,
  standalone: false,
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class TimerCellComponent implements OnInit, OnDestroy {
  @Input() startTime!: string;
  elapsedTime: string = '00H:00M:00S';
  private timerSubscription!: Subscription;

  constructor(private cd: ChangeDetectorRef, private dateAdapter: DateAdapter<Date>) {
    this.dateAdapter.setLocale('en-US');
  }

  ngOnInit(): void {
    if(this.startTime != '00H:00M:00S') {
      this.timerSubscription = interval(1000).subscribe(() => {
        this.elapsedTime = this.calculateElapsedTime(
          this.convertToMillis(this.startTime) + 1000
        );
        this.startTime = this.elapsedTime;
        this.cd.detectChanges();
      });
    }
  }

  convertToMillis(time: string) {
    const regex = /(\d+)H:(\d{2})M:(\d{2})S/;
    const match = time.match(regex);
    if (match) {
      const hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const seconds = parseInt(match[3]);
      const totalMilliseconds = (hours * 3600000) + (minutes * 60000) + (seconds * 1000);
      return totalMilliseconds;
    } else {
      throw new Error("Invalid time format. Please use '00H:00M:00S'.");
    }
  }

  calculateElapsedTime(start:any): string {
    const totalSeconds = Math.floor(start / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const formattedHours = hours < 10 ? `0${hours}` : hours;
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
    const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;
    return `${formattedHours}H:${formattedMinutes}M:${formattedSeconds}S`;
  }

  ngOnDestroy(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }
}