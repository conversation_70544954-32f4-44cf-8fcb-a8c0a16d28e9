.dialog-title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: white;
  text-align: center;
  padding: 6px 12px; /* Reduced vertical padding */
  margin: 0;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  line-height: 1;
}

.highlight-text {
    color: #ff6600;
    font-weight: bold;
}

.dialog-content {
    overflow: hidden; /* Prevents overflow issues */
    padding: 10px;
    background: #f5f7fa;
    border-radius: 12px;
    border: 1px solid #ddd;
    display: flex;
    flex-direction: column; /* Stack elements properly */
    justify-content: flex-start; /* Start from top */
    align-items: center;
    width: 100%;
}

.detail-box {
    display: flex;
    flex-direction: column; /* Ensures text flows properly */
    justify-content: flex-start; /* Ensures text starts from the top */
    align-items: flex-start; /* Aligns text properly */
    padding: 15px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-family: monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-line; /* Ensures line breaks are preserved */
    width: 100%;
    max-width: 100%;
    max-height: 100%;
    overflow-y: auto; /* Enables scrolling */
    text-align: left;
}

::ng-deep .mat-mdc-dialog-container .mat-mdc-dialog-title + .mat-mdc-dialog-content {
    padding-top: 10px;
}

.dialog-actions {
    display: flex;
    justify-content: center;
    padding: 15px 10px;
    gap: 15px;
}

.preserve-whitespace {
  white-space: pre-wrap; /* preserves spaces, line breaks, and wrapping */
  font-family: monospace; /* optional: aligns fixed-width formatting like console output */
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}

/* Buttons */
.action-button {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.close {
    min-width: 85px;
    max-height: 32px;
    border-radius: 10px;
    padding: 2px 10px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 500;
    transition: 0.3s ease-in-out;
    background-color: #ff6600 !important;
    color: white !important;
}

.close:hover {
  color: white !important;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}