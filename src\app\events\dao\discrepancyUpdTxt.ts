import { DiscrepanciesList } from "./discrepancies-listDao";

export class discrepancyUpdTxt {
    actualTxt: string;
    formattedTxt: string;

    constructor(actualTxt: string = '', formattedTxt: string = '') {
        this.actualTxt = actualTxt;
        this.formattedTxt = formattedTxt;
    }
}

export class linkedDiscrepancyUpdTxt {
    ata: string;
    dscrNumber: string;
    dscrpTxts: discrepancyUpdTxt[];

    constructor(ata: string = '', dscrNumber: string = '', dscrpTxts: discrepancyUpdTxt[] = []) {
        this.ata = ata;
        this.dscrNumber = dscrNumber;
        this.dscrpTxts = dscrpTxts;
    }
}

export class DiscrepancyTxtReq {
    acn: string;
    ata: string;
    discNumber: string;

    constructor(acn: string = '', ata: string = '', discNumber: string = '') {
        this.acn = acn;
        this.ata = ata;
        this.discNumber = discNumber;
    }
}

export interface DiscrepancySelectedTexts{
    discrepancy: DiscrepanciesList;
    selectedFormattedTexts: string[];
}