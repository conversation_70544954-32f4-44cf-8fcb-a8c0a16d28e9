<div style="padding: 5px;">
    <!-- <div class="niw-timer-heading">
        <p class="heading-1">NIW Timers for {{eventType}} Event</p>
    </div> -->
    <div>
        <p style="font-weight: bold;font-size: medium;color: #6c49b9;">Running NIW Timer: <span style="color: #FF7518;">{{runningTimer}}</span></p>
    </div>
    <div class="layout-style">
        <div style="width: 100%;">
            <div class="table-styles" [style.height.px]="niwTimerTableHeight">
                <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

                    <ng-container matColumnDef="NIW_Timer">
                    <th mat-header-cell *matHeaderCellDef class="stickyHeader"> NIW Timer </th>
                    <td mat-cell [class.timerStarted] = "element.timerStatus === 'Stop'" *matCellDef="let element" style="font-weight: bold;color: #7f7b7d;"> {{element.niw_timer}} </td>
                    </ng-container>
                
                    <ng-container matColumnDef="Current_NIW_Time">
                    <th mat-header-cell *matHeaderCellDef class="stickyHeader" style="text-align: center;"> Current NIW Time </th>
                    <td mat-cell [class.timerStarted] = "element.timerStatus === 'Stop'" *matCellDef="let element" class="cell-style"> 
                        <app-timer-cell [startTime]="element.niw_current_time"></app-timer-cell>
                    </td>
                    </ng-container>
                
                    <ng-container matColumnDef="Total_NIW_Time">
                    <th mat-header-cell *matHeaderCellDef class="stickyHeader" style="text-align: center;"> Total NIW Time </th>
                    <td mat-cell [class.timerStarted] = "element.timerStatus === 'Stop'" *matCellDef="let element" class="cell-style"> 
                        <app-total-niw-timer-cell [totalTime]="element.niw_total_time"></app-total-niw-timer-cell>
                    </td>
                    </ng-container>

                    <ng-container matColumnDef="Action">
                        <th mat-header-cell *matHeaderCellDef class="stickyHeader" style="text-align: center;"> Action </th>
                        <td mat-cell *matCellDef="let element" style="text-align: center;"> 
                            <button style="margin-right: 10%;" (click) = "editTimer(element.timerStatus, element.niw_timer)" mat-fab class="timer-button" [class.timerStopStyle] = "element.timerStatus === 'Stop'">
                                <mat-icon *ngIf = "element.timerStatus === 'Start'">play_arrow</mat-icon>
                                <mat-icon *ngIf = "element.timerStatus === 'Stop'">stop</mat-icon>
                            </button>
                            <!-- <button mat-fab class="timer-button">
                                <mat-icon>edit</mat-icon>
                            </button> -->
                        </td>
                    </ng-container>
                
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                        (click)="selectRow(row)" 
                        (dblclick)="openEditDialog(row)"
                        [class.selected]="selectedRow === row">
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
