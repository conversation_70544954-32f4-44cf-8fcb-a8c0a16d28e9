.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  background: #f4f7fc;
  font-family: 'Roboto', sans-serif;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dialog-header {
  text-align: center;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 8px;
  color: #ffffff;
  background: linear-gradient(135deg, #2a2a72, #6a5acd);
  border-radius: 12px 12px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.dialog-header:hover {
  background: linear-gradient(135deg, #1c1c54, #5a4ab5);
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2a2a72;
  text-align: center;
  margin: 4px 0;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  position: relative;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title::after {
  content: '';
  display: block;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #6a5acd, #ff8c00);
  margin: 4px auto;
  border-radius: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scaleX(1); }
  50% { transform: scaleX(1.2); }
  100% { transform: scaleX(1); }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 8px;
  background: #ffffff;
  border-radius: 10px;
  margin: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  max-height: calc(100vh - 80px);
}

.main-body {
  margin-bottom: 8px;
}

.tub-file-body,
.niw-timers-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.tub-file-content,
.niw-timers-content {
  display: flex;
  flex: 1;
  min-height: 0;
  gap: 8px;
  justify-content: space-between;
  align-items: stretch;
}

.tub-file-left,
.tub-file-right,
.niw-timers-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tub-file-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.tub-file-left .form-field.comment-field.full-width {
  width: 100%;
}

.tub-file-left .form-field.comment-field.half-height {
  height: 50%;
}

.tub-file-left .form-field.comment-field textarea {
  height: 100%;
  resize: none;
}

.tub-file-right .existing-notes-container {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(106, 90, 205, 0.2);
  display: flex;
  flex-direction: column;
}

.tub-file-right .existing-notes-container::-webkit-scrollbar {
  width: 6px;
}

.tub-file-right .existing-notes-container::-webkit-scrollbar-track {
  background: transparent;
}

.tub-file-right .existing-notes-container::-webkit-scrollbar-thumb {
  background-color: rgba(106, 90, 205, 0.5);
  border-radius: 3px;
}

.tub-file-right .existing-notes-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(106, 90, 205, 0.8);
}

.tub-file-right .no-notes {
  color: #6a5acd;
  font-size: 0.8rem;
  text-align: center;
  margin-top: 12px;
}

.tub-file-right .notes-content {
  color: #2a2a72;
  font-size: 0.8rem;
  line-height: 1.3;
}

.discrepancy-helper-text {
  font-size: 13px;
  font-weight: 600;
  color: #b91c1c; // Tailwind red-700, or use `red` or `#cc0000`
  margin-bottom: 8px;
  line-height: 1.4;
  padding: 15px;
}

.data-grid {
  display: grid;
  grid-template-columns: 18% 18% 18% 1fr 10%;
  gap: 8px;
  justify-content: center;
  align-items: center;
  padding: 8px;
  background: #f9fafb;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.data-item {
  background: #ffffff;
  border-radius: 8px;
  padding: 6px;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(106, 90, 205, 0.2);
  transition: all 0.3s ease;
}

.data-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(106, 90, 205, 0.2);
}

.data-item .label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2a2a72;
  text-transform: uppercase;
}

.data-item .value {
  cursor: default;
  font-size: 0.9rem;
  color: #6a5acd;
  font-weight: bold;
  word-break: break-word;
  padding: 4px 20px;
  background: rgba(106, 90, 205, 0.1);
  border-radius: 8px;
}

.form-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 8px;
  height: 50%;
}

.section-box {
  background: #f9fafb;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-box.new-status-section {
  flex: 0 0 25%;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 4px 16px rgba(106, 90, 205, 0.2), 0 2px 6px rgba(0, 0, 0, 0.12);
  padding: 15px;
}

.section-box.new-status-section:hover {
  box-shadow: 0 6px 20px rgba(106, 90, 205, 0.3), 0 3px 8px rgba(0, 0, 0, 0.18);
  transform: translateY(-2px);
}

.section-box.new-etic-section {
  flex: 1;
  min-width: 350px;
  box-shadow: 0 4px 16px rgba(106, 90, 205, 0.2), 0 2px 6px rgba(0, 0, 0, 0.12);
  height: 100%;
}

.section-box.new-etic-section:hover {
  box-shadow: 0 6px 20px rgba(106, 90, 205, 0.3), 0 3px 8px rgba(0, 0, 0, 0.18);
  transform: translateY(-2px);
}

.error-container {
  display: flex;
  align-items: center;
  align-content: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 6px 12px;
  background-color: #fff3cd;
  border: 1px solid #f5c06f;
  border-radius: 6px;
  color: #b71c1c;
  font-size: 0.85rem;
  font-weight: 600;
  width: 100%;
  animation: fadeIn 0.3s ease-in-out;
  overflow: hidden;
  text-overflow: ellipsis;
}

.error-chunk {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.error-icon {
  align-content: center;
  font-size: 16px;
  color: #d32f2f;
  font-weight: 600;
}

.error-separator {
  margin: 0 4px;
  color: #999;
}

.mat-error-message {
  color: #d32f2f;
  font-size: 0.8rem;
  font-weight: 600;
  margin: 4px 0;
  display: flex;
  align-items: center;
  
  &::before {
    content: '⚠';
    margin-right: 6px;
    font-size: 1rem;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Remove individual error messages from other places */
.data-item .error-message,
.comment-field .error-message,
.checkbox-row .error-message {
  display: none !important;
}

.custom-divider {
  margin: 6px 0;
  width: 85%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #6a5acd 20%, #ff8c00 80%, transparent);
  border: none;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.custom-divider.vertical {
  width: 2px;
  height: 95%;
  align-self: center;
  background: linear-gradient(to bottom, #6a5acd 0%, #6a5acd 40%, #d18b2e 50%, #ff8c00 60%, #ff8c00 100%);
  margin: 0 8px;
  border-radius: 2px;
}

.custom-divider::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  0% { transform: translate(0, 0); }
  100% { transform: translate(200%, 200%); }
}

.status-spacer {
  height: 8px;
}

.data-rows {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.data-row {
  display: flex;
  gap: 6px;
  margin-bottom: 6px;
  width: 100%;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
}

.data-row.reason-comment {
  margin-top: 6px;
}

.data-item.date-time.date-field {
  flex: 0 0 160px;
  width: 160px;
  max-width: 160px;
  min-width: 160px;
}

.data-item.date-time.time-field {
  flex: 0 0 200px;
  width: 200px;
  max-width: 200px;
  min-width: 200px;
}

.data-item.info {
  flex: 0 0 120px;
  width: 120px;
  max-width: 120px;
  min-width: 120px;
}

.data-item.ost {
  flex: 0 0 80px;
  width: 80px;
  max-width: 80px;
  min-width: 80px;
}

.data-item.reason-field {
  flex: 0 0 30%;
  width: 30%;
  max-width: 30%;
}

.data-item.comment-field {
  flex: 0 0 70%;
  width: 70%;
  max-width: 69%;
}

.data-item.date-time.date-field .form-field,
.data-item.date-time.time-field .form-field,
.data-item.info .form-field,
.data-item.ost .form-field,
.data-item.reason-field .form-field,
.data-item.comment-field .form-field,
.data-item.etic-type-field .form-field,
.data-item.niw-reason-field .form-field,
.data-item.time-needed-field .form-field,
.data-item.units-field .form-field {
  width: 100%;
}

.time-picker {
  display: flex;
  gap: 6px;
  width: 100%;
}

.time-field {
  flex: 1;
  min-width: 70px;
}

// Removed time-units-row since all fields are now in single row

.data-item.time-needed-field {
  flex: 0 0 100px;
  max-width: 100px;
  min-width: 100px;
}

.data-item.units-field {
  flex: 0 0 90px;
  max-width: 90px;
  min-width: 90px;
}

.data-item.etic-type-field {
  flex: 0 0 160px;
  max-width: 160px;
  min-width: 160px;
}

.data-item.niw-reason-field {
  flex: 0 0 160px;
  max-width: 160px;
  min-width: 160px;
}

.plus-sign {
  flex: 0 0 20px;
  max-width: 20px;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2em;
  color: #6c49b9;
}

// Ensure New ETIC section doesn't wrap
.new-etic-section {
  .data-rows {
    overflow-x: auto;
  }

  .data-row {
    min-width: fit-content;
    white-space: nowrap;
  }
}

// Responsive handling for smaller screens
@media (max-width: 1200px) {
  .new-etic-section .data-row {
    overflow-x: auto;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #6c49b9;
      border-radius: 2px;
    }
  }
}

.status-form {
  display: flex;
  justify-content: space-evenly;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  height: 100%;
}

.tubfile-note-form {
  justify-content: start !important;
}

.form-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: space-evenly;
  margin-bottom: 8px;
  height: 52%;
}

.form-field {
  width: 100%;
  transition: all 0.3s ease;
}

.new-status-section .form-field {
  width: 60%;
  height: auto;
}

.form-field ::ng-deep .mat-form-field-wrapper {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.form-field:hover ::ng-deep .mat-form-field-wrapper {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
}

.form-field ::ng-deep .mat-form-field-outline {
  border: 1px solid rgba(106, 90, 205, 0.25);
  border-radius: 8px;
}

.form-field ::ng-deep .mat-form-field-infix {
  padding: 6px 0;
}

.form-field ::ng-deep .mat-select-value,
.form-field ::ng-deep input,
.form-field ::ng-deep textarea {
  background-color: lavender;
  color: #6a5acd !important;
  justify-items: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
}

.form-field ::ng-deep .mat-form-field-label {
  color: #475569;
  font-weight: 500;
  font-size: 0.8rem;
}

.form-field:hover ::ng-deep .mat-form-field-label {
  color: #6a5acd;
}

.form-field ::ng-deep .mat-select-arrow {
  color: #6a5acd;
}

.form-field ::ng-deep .mat-datepicker-toggle .mat-icon-button {
  color: #6a5acd;
}

.form-field ::ng-deep .mat-datepicker-toggle:hover .mat-icon-button {
  color: #ff8c00;
}

.form-field ::ng-deep .purple-text {
  justify-items: center;
  background-color: lavender;
  color: #6a5acd !important;
  height: 100%;
  border-radius: 8px;
}

.form-checkbox {
  margin: 4px 0;
}

.form-checkbox ::ng-deep .mat-checkbox-checked .mat-checkbox-background {
  background-color: #ff8c00;
}

.form-checkbox ::ng-deep .mat-checkbox-frame {
  border-color: rgba(106, 90, 205, 0.4);
}

.form-checkbox:hover ::ng-deep .mat-checkbox-frame {
  border-color: #6a5acd;
}

.comments-section {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: center;
  height: 25%;
}

.comment-wrapper {
  flex: 1;
  min-width: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.comment-wrapper .form-field:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(106, 90, 205, 0.25);
  transition: all 0.3s ease-in-out;
}

.comment-field.flex-grow {
  flex: 1;
  min-width: 240px;
  height: 100%;
}

.comment-field ::ng-deep .mat-form-field-wrapper {
  box-shadow: 0 4px 12px rgba(106, 90, 205, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  transition: all 0.3s ease;
  height: 100%;
}

.comment-field:hover ::ng-deep .mat-form-field-wrapper {
  box-shadow: 0 6px 16px rgba(106, 90, 205, 0.25), 0 3px 8px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
}

.comment-field ::ng-deep textarea {
  font-size: 0.8rem;
  line-height: 1.3;
  background: #f8fafc;
  border-radius: 8px;
  padding: 6px;
  height: 100%;
  overflow-y: auto;
  resize: none;
  transition: all 0.3s ease;
}

.comment-field ::ng-deep textarea:focus {
  background: #ffffff;
  box-shadow: 0 0 8px rgba(106, 90, 205, 0.4);
  border: 1px solid #6a5acd;
}

.checkbox-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 10%;
}

.checkbox-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  height: 100%;
  align-items: center;
}

.checkbox-row ::ng-deep .mat-checkbox-label {
  color: #2a2a72;
  font-size: 0.8rem;
  font-weight: 500;
}

.checkbox-row ::ng-deep .mat-checkbox:hover .mat-checkbox-label {
  color: #6a5acd;
}

.dialog-footer {
  flex-shrink: 0;
  background: #ffffff;
  padding: 8px 12px;
  display: flex;
  gap: 12px;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 12px 12px;
}

.action-button {
  width: 8%;
  padding: 8px 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-button.previous-button:not(:disabled) {
  background: linear-gradient(135deg, #6a5acd, #483d8b);
  color: #ffffff;
  border: 1px solid rgba(106, 90, 205, 0.6);
}

.action-button.previous-button:not(:disabled):hover {
  background: linear-gradient(135deg, #5a4ab5, #3c2f6b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(106, 90, 205, 0.3);
}

.action-button.cancel-button:not(:disabled) {
  background: linear-gradient(135deg, #ff8c00, #e67e22);
  color: #ffffff;
  border: 1px solid rgba(255, 140, 0, 0.6);
}

.action-button.cancel-button:not(:disabled):hover {
  background: linear-gradient(135deg, #e67e22, #d2691e);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
}

.action-button.submit-button:not(:disabled) {
  background: linear-gradient(135deg, #6a5acd, #483d8b);
  color: #ffffff;
  border: 1px solid rgba(106, 90, 205, 0.6);
}

.action-button.submit-button:not(:disabled):hover {
  background: linear-gradient(135deg, #5a4ab5, #3c2f6b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(106, 90, 205, 0.3);
}

.action-button:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.8;
}

::ng-deep .mat-mdc-select-value {
  text-align-last: center;
  justify-items: center;
  background: linear-gradient(135deg, #f0f0ff, #e6e6fa);
  padding: 2px;
  border-radius: 4px;
}

::ng-deep .mat-mdc-option.main-reason {
  font-weight: 600;
  color: #2a2a72;
  background: linear-gradient(90deg, #e6e6fa, #f0f0ff);
  padding: 6px 12px;
  border-bottom: 1px solid rgba(106, 90, 205, 0.25);
  transition: all 0.2s ease;
}

::ng-deep .mat-mdc-option.main-reason:hover {
  background: linear-gradient(90deg, #dadaf8, #e6e6fa);
  color: #6a5acd;
}

::ng-deep .mat-mdc-option.sub-reason {
  font-weight: 400;
  color: #475569;
  padding: 6px 12px 6px 24px;
  background: #ffffff;
  position: relative;
}

::ng-deep .mat-mdc-option.sub-reason::before {
  content: '—';
  position: absolute;
  left: 12px;
  color: #6a5acd;
}

::ng-deep .mat-mdc-option.sub-reason:hover {
  background: #f8f9fa;
  color: #6a5acd;
}

::ng-deep .mat-mdc-option.main-reason.mdc-list-item--selected,
::ng-deep .mat-mdc-option.sub-reason.mdc-list-item--selected {
  background: linear-gradient(90deg, #6a5acd, #483d8b) !important;
  color: #ffffff !important;
}

::ng-deep .mat-mdc-option.main-reason.mdc-list-item--selected .mdc-list-item__primary-text,
::ng-deep .mat-mdc-option.sub-reason.mdc-list-item--selected .mdc-list-item__primary-text {
  color: #ffffff !important;
}

.comment-field {
  position: relative;

  .styled-textarea {
    text-align: center;
    padding-top: 30px;
    resize: none;
  }

  .custom-placeholder {
    position: absolute;
    top: 32px; // Adjust based on rows/height
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    font-style: italic;
    color: #999;
    pointer-events: none;
    transition: all 0.3s ease;
  }

  .mat-form-field-infix {
    min-height: 160px; // or adjust based on your design
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.lowercase-textarea {
  text-transform: lowercase;
}

.notes-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f9fafb;
}

.sort-by-sticky {
  position: sticky;
  top: 10px;
  width: 98%;
  align-self: center;
  background: linear-gradient(180deg, #ffffff, #f1f5f9);
  padding: 5px 18px;
  margin: 5px 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  border-radius: 12px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.sort-by-sticky:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.sort-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #5b21b6;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.sort-radio-group {
  display: flex;
  gap: 16px;
}

.sort-radio {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.sort-radio ::ng-deep .mat-radio-checked .mat-radio-outer-circle {
  border-color: #7c3aed;
}

.sort-radio ::ng-deep .mat-radio-checked .mat-radio-inner-circle {
  background-color: #7c3aed;
}

.notes-scroll-container {
  flex: 1;
  overflow-y: auto;
  margin-top: 5px;
  padding-bottom: 20px;
}

.existing-notes-container {
  max-height: none;
  padding: 0 14px;
}

.existing-notes-container::-webkit-scrollbar {
  width: 6px;
}

.existing-notes-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 12px;
}

.existing-notes-container::-webkit-scrollbar-thumb {
  background: #a5b4fc;
  border-radius: 12px;
}

.existing-notes-container::-webkit-scrollbar-thumb:hover {
  background: #818cf8;
}

.compact-note-card {
  padding: 5px 10px;
  margin: 8px 0;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 16px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.compact-note-card:hover {
  background: linear-gradient(145deg, #f0f0ff, #ede9fe);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.15);
}

.compact-note-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  border-radius: 12px 0 0 12px;
  background: linear-gradient(180deg, #7c3aed, #FF7518);
  transition: width 0.3s ease;
}

.compact-note-card:hover::before {
  width: 8px;
}

.note-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.note-text-container {
  background: rgba(139, 92, 246, 0.04);
  padding: 10px 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.note-heading {
  font-size: 1rem;
  color: #FF7518;
  line-height: 1.5;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.note-text {
  font-size: 0.95rem;
  color: #1e3a8a;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.note-meta-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  background: rgba(139, 92, 246, 0.05);
  padding: 8px 10px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.note-meta-content:hover {
  background: rgba(139, 92, 246, 0.08);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.7rem;
  color: #374151;
}

.meta-icon {
  color: #7c3aed;
  font-size: 12px;
}

.emp-name {
  color: #1f2a44;
  font-weight: 500;
  font-size: 0.65rem;
}

.update-time {
  color: #6b7280;
  font-style: italic;
  font-weight: 400;
  font-size: 0.65rem;
}

.no-notes {
  color: #6b7280;
  font-size: 1rem;
  font-style: italic;
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  margin: 16px 0;
  border: 1px solid rgba(139, 92, 246, 0.08);
}

.selectable {
  user-select: none;
}

.selected-card {
  background: linear-gradient(145deg, #fae4d6, #fae4d6) !important;
  border: 2px solid #a5b4fc !important;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.2);
  transform: scale(1.01);
}

.selected-card::before {
  width: 8px;
  background: linear-gradient(180deg, #6a5acd, #d97706);
}

.selectable {
  user-select: none;
}

.selected-card {
  background: #fefce8 !important;
  border: 2px solid #a5b4fc !important;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.2);
  transform: scale(1.02);
}

.selected-card::before {
  width: 8px;
  background: linear-gradient(180deg, #6a5acd, #d97706);
}

.selectable {
  user-select: none;
}

.niw-timers-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 16px;
  background: linear-gradient(180deg, #f9fafb, #f1f5f9);
  overflow: hidden;
  justify-content: flex-start;
}

.niw-timers-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 80%;
  max-width: 960px;
  margin: 10px auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(15, 23, 42, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.niw-timers-content:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 28px rgba(15, 23, 42, 0.2), 0 3px 10px rgba(0, 0, 0, 0.15);
}

.niw-timers-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 16px;
}

.niw-timer-form-container {
  width: 100%;
  background: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(15, 23, 42, 0.12);
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.niw-timer-form-container:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(15, 23, 42, 0.18);
}

.niw-timer-form {
  width: 100%;
  display: flex;
  justify-content: normal;
  // flex-direction: column;
  align-items: center;
  gap: 20px;
}

.niw-timer-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  width: 100%;
  background: #ffffff;
  padding: 16px;
  border-radius: 10px;
  box-shadow: 0 3px 12px rgba(15, 23, 42, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.niw-timer-row:hover {
  border-color: #6a5acd;
  box-shadow: 0 5px 16px rgba(106, 90, 205, 0.25);
}

.niw-timer-row .data-item.date-time.date-field,
.niw-timer-row .data-item.date-time.time-field {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.niw-timer-row .time-picker {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;
}

.niw-timer-row .form-field {
  width: 100%;
}

.niw-timer-row .form-field ::ng-deep .mat-form-field-wrapper {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.niw-timer-row .form-field:hover ::ng-deep .mat-form-field-wrapper {
  border-color: #6a5acd;
  box-shadow: 0 3px 12px rgba(106, 90, 205, 0.3);
  transform: translateY(-1px);
}

.niw-timer-row .form-field ::ng-deep .mat-form-field-label {
  color: #374151;
  font-weight: 600;
  font-size: 0.9rem;
}

.niw-timer-row .form-field:hover ::ng-deep .mat-form-field-label {
  color: #6a5acd;
}

.niw-timer-row .form-field ::ng-deep .mat-select-arrow,
.niw-timer-row .form-field ::ng-deep .mat-datepicker-toggle .mat-icon-button {
  color: #6a5acd;
}

.niw-timer-row .form-field ::ng-deep .mat-select-arrow:hover,
.niw-timer-row .form-field ::ng-deep .mat-datepicker-toggle:hover .mat-icon-button {
  color: #ff8c00;
}

.niw-timers-table-container {
  width: 50%;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.niw-timers-table {
  width: 50%;
  table-layout: fixed;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  font-size: 0.9rem;
  border-collapse: separate;
  border-spacing: 0;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // ✨ Add this line

  th, td {
    padding: 6px 8px;
    border-bottom: 1px solid #eee;
    text-align: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 0.9rem;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(to right, lightgray, #eaeaea);
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
  }

  tr:hover {
    cursor: pointer !important;
    background-color: #fafafa;
  }
}

.niw-timers-table ::ng-deep .mat-header-cell {
  background: linear-gradient(180deg, #6a5acd, #483d8b);
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid rgba(106, 90, 205, 0.3);
}

.niw-timers-table ::ng-deep .mat-cell {
  padding: 12px;
  font-size: 0.85rem;
  color: #1e3a8a;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background 0.2s ease;
}

.niw-timers-table ::ng-deep .mat-row:hover .mat-cell {
  background: #f0f0ff;
}

.niw-timers-table ::ng-deep .selected-row {
  background-color: #FAE4D6 !important;
  color: black !important;
  font-weight: 600 !important;
}

.niw-timers-table-container::-webkit-scrollbar {
  width: 6px;
}

.niw-timers-table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.niw-timers-table-container::-webkit-scrollbar-thumb {
  background-color: rgba(106, 90, 205, 0.5);
  border-radius: 3px;
}

.niw-timers-table-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(106, 90, 205, 0.8);
}

@media (max-width: 768px) {
  .dialog-container {
    height: auto;
    max-height: none;
  }

  .form-container {
    flex-direction: column;
    height: auto;
  }

  .section-box.new-status-section,
  .section-box.new-etic-section {
    flex: 1;
    min-width: 100%;
  }

  .data-grid {
    grid-template-columns: 1fr;
  }

  .data-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .data-item.date-time.date-field,
  .data-item.date-time.time-field,
  .data-item.info,
  .data-item.ost,
  .data-item.reason-field,
  .data-item.comment-field {
    flex: 1;
    width: 100%;
    max-width: 100%;
  }

  .comments-section {
    flex-direction: column;
    height: auto;
  }

  .comment-wrapper {
    min-width: 100%;
  }

  .tub-file-content,
  .niw-timers-content {
    flex-direction: column;
  }

  .tub-file-left,
  .tub-file-right,
  .niw-timers-left {
    width: 100%;
  }

  .niw-timer-row .data-item.date-time.date-field,
  .niw-timer-row .data-item.date-time.time-field {
    min-width: 100%;
    max-width: 100%;
  }
}