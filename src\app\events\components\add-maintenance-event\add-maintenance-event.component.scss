.stepper-container {
  width: calc(100% - 10px);
  margin: 5px;
  place-self: center;
  background: #ffffff;
  box-shadow: 0 8px 20px rgba(63, 40, 118, 0.25);
  padding: 0;
  border-radius: 14px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;

  ::ng-deep .mat-horizontal-stepper-header-container {
    padding: 0.5rem 1.2rem;
    position: relative;
    background: linear-gradient(135deg, #f8f7ff, #ffffff);
  }

  ::ng-deep .mat-horizontal-content-container {
    padding: 0 !important;
  }

  ::ng-deep .mat-step-header {
    position: relative;
    padding: 5px;
    height: 30px;
    transition: all 0.3s ease;

    &.mat-step-header[aria-selected="true"] {
      background: #B7BBE3;
      border-radius: 10px;
    }

    &:hover {
      background: #B7BBE3;
      transform: translateY(-2px);
    }

    .mat-step-icon {
      background: #6c49b9;
      color: #ffffff;
      width: 26px;
      height: 26px;
      font-size: 0.95rem;
      font-weight: bold;
      line-height: 26px;
      transition: all 0.4s ease;
      box-shadow: 0 2px 6px rgba(63, 40, 118, 0.2);
    }

    .mat-step-icon-selected {
      background: #ff6600;
      color: #6c49b9;
      font-weight: bold;
      transform: scale(1.1);
    }

    .mat-step-label {
      color: #3F2876;
      font-weight: 700;
      font-size: 0.75rem;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;
      line-height: 1.3;
      max-width: 110px;
      transition: color 0.3s ease;
    }

    .step-badge {
      position: absolute;
      top: 8px;
      right: 12px;
      background: #28a745;
      border-radius: 50%;
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 12px;
      box-shadow: 0 4px 8px rgba(63, 40, 118, 0.3);
      z-index: 10;
      visibility: visible !important;
      opacity: 1 !important;
      animation: badgePop 0.4s ease-in-out;
      transition: transform 0.3s ease;
    }

    .step-badge:hover {
      transform: scale(1.15);
    }
  }

  ::ng-deep .mat-stepper-horizontal-line {
    border-top: 4px solid #B7BBE3;
    margin: 0 8px;
    transition: border-color 0.3s ease;
  }
}

.content-container {
  width: calc(100% - 10px);
  margin: 5px;
  padding: 0.5rem;
  max-height: calc(100vh - 240px);
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-color: #6c49b9 #f8f7ff;
  border-radius: 10px;
  border: 2px solid #B7BBE3;
  background: #f8f7ff;
  animation: fadeInUp 0.7s ease-in-out;
  display: flex;
  justify-content: center;
  position: relative;

  &::-webkit-scrollbar {
    width: 14px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f7ff;
    border-radius: 12px;
  }

  &::-webkit-scrollbar-thumb {
    background: #6c49b9;
    border-radius: 12px;
    border: 3px solid #f8f7ff;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #3F2876;
  }
}

.form-container {
  width: 100%;
  margin: 0.5rem;
}

.step-section {
  margin-bottom: 2rem;
  animation: slideInFromTop 0.6s ease-in-out;
}

.step-title {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: #ffffff;
  border-radius: 12px;
  font-weight: 700;
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 20px rgba(63, 40, 118, 0.35);
  position: relative;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shine 3s infinite;
  }
}

.form-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 10px 28px rgba(63, 40, 118, 0.25);
  padding: 1rem;
  margin-bottom: 2rem;
  border-left: 8px solid #6c49b9;
  transition: transform 0.4s ease, box-shadow 0.4s ease;

  &:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(63, 40, 118, 0.3);
  }
}

.form-group {
  position: relative;
  padding: 1.5rem;
  border-radius: 12px;
  background: #f8f7ff;
  margin-bottom: 1rem;
  border: 2px solid #B7BBE3;
  transition: all 0.4s ease;

  &.general-info {
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
    transition: transform 0.4s ease, box-shadow 0.4s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(63, 40, 118, 0.25);
    }

    .section-title {
      text-align: center;
      width: 100%;
      display: block;
    }

    .info-fields-container {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: nowrap;
      background: #ffffff;
      padding: 1.5rem;
      border-radius: 10px;
      border: 2px solid #B7BBE3;
      width: 100%;
      justify-content: space-between;
    }
  }

  &.status-etic-container {
    display: flex;
    gap: 1.5rem;
    padding: 1rem;
  }

  &.acn-status {
    width: 30%;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    background: #ffffff;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
    transition: transform 0.4s ease;

    &:hover {
      transform: translateY(-4px);
    }
  }

  &.etic-details {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0 !important;
    background: #ffffff;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
    transition: transform 0.4s ease;

    &:hover {
      transform: translateY(-4px);
    }

    .etic-details-row {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1.5rem;
      flex-wrap: nowrap;
    }

    .etic-field {
      flex: 1;
      min-width: 120px;

      &.date-field {
        max-width: 240px;
      }

      &.small-time-field {
        max-width: 110px;
      }

      &.time-needed-field {
        max-width: 140px;
        flex: 0 0 140px;

        ::ng-deep .mat-form-field-infix {
          width: 120px !important;
        }

        ::ng-deep input[type="number"] {
          text-align: left !important;
          padding-right: 8px !important;
          width: 100% !important;

          // Reset and properly position spinner controls
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: auto !important;
            opacity: 1 !important;
            position: absolute !important;
            right: 4px !important;
            height: 100% !important;
            margin: 0 !important;
          }

          // For Firefox
          -moz-appearance: auto !important;
        }

        // Ensure the field container doesn't expand
        ::ng-deep .mat-form-field-wrapper {
          width: 100% !important;
          max-width: 140px !important;
        }

        // Target the specific input class
        ::ng-deep .time-needed-input {
          text-align: left !important;
          padding-right: 20px !important;
        }
      }
    }

    .etic-checkbox {
      flex: 0 0 auto;
    }
  }

  &.contact-info, &.other-details {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    background: #f8f7ff;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
    transition: transform 0.4s ease;

    &:hover {
      transform: translateY(-4px);
    }

    .contact-info-fields {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      background: #ffffff;
      padding: 1rem;
      border-radius: 10px;
      border: 2px solid #B7BBE3;
      justify-content: space-between;

      .form-field {
        flex: 1;
        min-width: 0;
        max-width: none;
      }
    }

    .other-details-fields {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1.5rem;
      background: #ffffff;
      padding: 1rem;
      border-radius: 10px;
      border: 2px solid #B7BBE3;
    }
  }
}

.section-title {
  font-size: 0.9rem;
  font-weight: 700;
  color: #2a2a72;
  text-align: center;
  margin: 6px 0;
  text-transform: uppercase;
  letter-spacing: 1.4px;
  position: relative;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  animation: fadeInUp 0.5s ease-in-out;
}

.section-title::after {
  content: '';
  display: block;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #6a5acd, #ff8c00);
  margin: 6px auto;
  border-radius: 3px;
  animation: pulse 2.5s ease-in-out infinite;
}

.form-field {
  width: 100%;
  max-width: 260px;

  &.full-width {
    max-width: 100%;
  }

  &.comment-tag {
    max-width: 220px;
  }

  &.comment-text {
    max-width: calc(100% - 240px);
  }

  &.time-field {
    max-width: 130px;
  }

  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 12px;
      border: 2px solid #B7BBE3;
      background: #ffffff;
      transition: all 0.4s ease;
      padding: 0.75rem;
      box-shadow: 0 2px 6px rgba(63, 40, 118, 0.1);
    }

    &.mat-focused .mat-mdc-text-field-wrapper {
      border-color: #6c49b9;
      box-shadow: 0 0 0 6px rgba(108, 73, 185, 0.3);
      transform: scale(1.02);
    }

    .mat-mdc-select-value,
    .mdc-text-field__input,
    textarea {
      color: #6c49b9 !important;
      font-weight: 700;
      transition: color 0.3s ease;
    }

    .mat-mdc-form-field-error {
      color: #cc5200;
      font-size: 0.9rem;
      animation: shake 0.3s ease-in-out;
    }

    .mat-datepicker-toggle,
    .mat-timepicker-toggle {
      color: #6c49b9;
      transition: color 0.3s ease;
    }

    .mat-mdc-option {
      color: #000000 !important;
    }

    .mat-mdc-select-value-text {
      color: #6c49b9 !important;
    }
  }
}

.info-field.acn-type-field {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  gap: 0.75rem;
  background: #ffffff;
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(63, 40, 118, 0.2);
  font-weight: 700;
  color: #3F2876;
  transition: transform 0.4s ease;
  flex: 1;
  min-width: 160px;

  &:hover {
    transform: translateY(-3px);
  }

  .label {
    color: #6c49b9;
    font-weight: 700;
  }

  .highlight {
    color: #6c49b9;
    font-weight: 800;
  }
}

.etic-not-available {
  text-align: center;
  color: #6c49b9;
  font-style: italic;
  margin: 1rem 0;
  font-size: 0.95rem;
  animation: fadeInUp 0.5s ease-in-out;
}

.comment-section {
  display: flex;
  flex-wrap: nowrap;
  gap: 1.5rem;
  align-items: flex-start;
}

.aof-option {
  font-weight: 700;
  color: #6c49b9;
  margin: 0;
}

.aof-subtext {
  font-size: 0.9rem;
  color: #6c49b9;
  margin: 0;
}

.niw-timers-table-container {
  padding: 1.5rem;
  background: #f8f7ff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(63, 40, 118, 0.15);
  width: 50%;
  margin: 0 auto;
}

.niw-timers-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 1rem;
    text-align: center;
    border-bottom: 2px solid #B7BBE3;
    transition: background 0.3s ease;
  }

  th {
    background: linear-gradient(135deg, #3F2876, #6c49b9);
    color: #ffffff;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.95rem;
  }

  td {
    background: #ffffff;
    cursor: pointer;
    transition: background 0.4s ease;

    &.selected-row {
      background: #FAE4D6;
      font-weight: 700;
      transform: scale(1.01);
    }

    &:hover {
      background: #FAE4D6;
      transform: scale(1.01);
    }
  }
}

.error-message {
  color: #cc5200;
  font-size: 0.9rem;
  margin-top: 1rem;
  font-weight: 700;
  animation: shake 0.3s ease-in-out;
  text-align: center;
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes badgePop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  80% {
    transform: scale(1.3);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.15);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@media (max-width: 768px) {
  .form-group {
    &.status-etic-container {
      flex-direction: column;
    }

    &.acn-status {
      width: 100%;
    }

    &.etic-details {
      width: 100%;

      .etic-details-row {
        flex-direction: column;
        align-items: flex-start;
      }

      .etic-field {
        max-width: 100%;
      }
    }

    &.general-info {
      .info-fields-container {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &.contact-info, &.other-details {
      .contact-info-fields, .other-details-fields {
        flex-direction: column;
      }

      .form-field {
        max-width: 100%;
      }
    }
  }

  .info-field.acn-type-field {
    width: 100%;
  }

  .comment-section {
    flex-direction: column;
  }

  .content-container {
    max-height: calc(100vh - 140px);
  }

  .niw-timers-table-container {
    width: 100%;
  }
}

.text-align-last-left {
  text-align-last: left !important;
}

.form-field ::ng-deep .mat-select-value,
.form-field ::ng-deep input{
  background-color: lavender;
  color: #6a5acd !important;
  justify-items: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
}

::ng-deep .mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input {
  background-color: lavender;
  color: #6a5acd !important;
  text-align-last: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
}

::ng-deep .mat-mdc-select-value {
  background-color: lavender;
  color: #6a5acd !important;
  justify-items: center;
  font-size: 0.85rem;
  padding: 3px 0 !important;
  border-radius: 6px;
  font-weight: 500;
  text-align-last: center;
}

.custom-addEvent-confirmation-dialog-container {
  width: 50vw !important;
  min-width: 50vw !important;
  max-width: 90vw !important;
  position: static;
  margin: 0 auto;
  box-sizing: border-box;
}

// Specific overrides for Time Needed field
::ng-deep .time-needed-field {
  max-width: 140px !important;
  flex: 0 0 140px !important;

  .mat-form-field-infix {
    width: 120px !important;
  }

  input[type="number"] {
    text-align: left !important;
    padding-right: 20px !important;

    // Ensure spinner controls are visible and positioned correctly
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: auto !important;
      opacity: 1 !important;
      margin: 0 !important;
      position: relative !important;
      right: 0 !important;
    }
  }
}