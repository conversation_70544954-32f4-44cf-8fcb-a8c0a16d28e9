import { ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MaintenanceEventDetailsService } from '../../../services/maintenance-event-details.service';
import { RetrievalDto } from '../../../dto/retrievalDto';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';

@Component({
  selector: 'app-event-details-header',
  templateUrl: './event-details-header.component.html',
  styleUrl: './event-details-header.component.scss',
  standalone: false
})
export class EventDetailsHeaderComponent implements OnChanges {

  detailsHeaderDao: DetailViewResponseDao = new DetailViewResponseDao();

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;

  constructor(private maintenanceEventDetailsService: MaintenanceEventDetailsService,
              private cdRef: ChangeDetectorRef,
              ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      if (this.detailsViewObj.eventACN != "") {
        this.displayDiscrepanciesHeaderData(this.detailsViewObj);
      } else {
        this.detailsHeaderDao = {} as DetailViewResponseDao; 
      }
    }
  }

  displayDiscrepanciesHeaderData(detailsViewObj?: DetailViewResponseDao): void {
    this.detailsHeaderDao = detailsViewObj ?? {} as DetailViewResponseDao;
    (this.detailsHeaderDao.eventEticDateTime == null || this.detailsHeaderDao.eventEticDateTime == "null") ? this.detailsHeaderDao.eventEticDateTime = "" : null;
    this.cdRef.detectChanges();
  }
}
