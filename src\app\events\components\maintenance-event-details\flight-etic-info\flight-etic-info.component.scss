// .container {
//     width: 100%;
//     margin: 0;
//     padding: 0;
// }
.container {
    position: relative;
    left: 14px;
    width: 100%;
    height: 65vh; /* Full viewport height */
    margin: 0;
    max-width: none;
    padding: 0 20px 0px 0px;
    display: flex;
    gap: 8px;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden; /* Prevent overflow */
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
}

/* Title Styling - Extreme Left & Interactive */
.title {
    flex: 0 0 100%;
    margin: 0;
    text-align: left;
    padding: 6px 20px;
    color: white;
    font-size: 18px;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(135deg, rgb(145, 115, 115), rgb(175, 146, 146));
    background-size: 0% 100%;
    background-repeat: no-repeat;
    opacity: 1;
}

/* This class is added dynamically for animation */
.title.fill-animation {
    animation: fillBackground 2s ease-in-out forwards;
}

/* Keyframes to animate background fill */
@keyframes fillBackground {
    0% {
        background-size: 0% 100%; /* Start empty */
    }
    100% {
        background-size: 100% 100%; /* Fully filled */
    }
}


.etic-statistics-container, .actual-flight-info-container, .affected-Outbound-flight-container {
    width: 100%;
    background: #ffffff;
    margin: 0; /* Remove margins to prevent extra height */
    padding: 5px 15px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease-in-out;
    box-sizing: border-box;
    overflow: auto; /* Allow scrolling for overflow content */
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.etic-statistics-container {
    height: 25vh; /* Fixed height for statistics section */
}

.actual-flight-info-container, .affected-Outbound-flight-container {
    height: 37.5vh; /* Fixed height for actual flight info section */
}

.etic-statistics-container:hover, .affected-Outbound-flight-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.etic-title-container, .actual-flight-info-title-container, .affected-Outbound-flight-title-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 0;
}

.etic-title, .actual-flight-info-title, .affected-Outbound-flight-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    color: #ffffff;
    text-align: center;
    background: linear-gradient(135deg, rgb(175, 146, 146), rgb(145, 115, 115));
    padding: 5px 3%;
    border-radius: 10px;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.etic-statistics-container:hover .etic-title, .actual-flight-info-container:hover .actual-flight-info-title, .affected-Outbound-flight-container:hover .affected-Outbound-flight-title {
    font-weight: bold;
    transform: scale(1.05);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.etic-statistics-content, .actual-flight-info-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 5px 0;
    gap: 10px; /* Reduced gap to save space */
}

.affected-Outbound-flight-content {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    padding: 5px 0;
    gap: 10px; /* Reduced gap to save space */
}

.label-value {
    display: flex;
    align-items: center;
    justify-content: center;
    background: whitesmoke;
    padding: 6px; /* Reduced padding to save space */
    border-radius: 10px;
    position: relative;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.2);
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.label {
    font-size: 14px;
    font-weight: bold;
    margin-right: 8px; /* Reduced margin to save space */
    opacity: 0.7;
}

.value {
    color: rgb(175, 146, 146);
    font-size: 15px;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
}