export class AcnCacheDetail {
  status: string | null;
  fleetCode: string;
  acn: string;

  constructor(data?: Partial<AcnCacheDetail>) {
    this.status = data?.status ?? null;
    this.fleetCode = data?.fleetCode ?? '';
    this.acn = data?.acn ?? '';
  }
}

export class AcnCacheResponseDao {
  ACN_CACHE_DETAIL: AcnCacheDetail[];

  constructor(data?: Partial<AcnCacheResponseDao>) {
    this.ACN_CACHE_DETAIL = data?.ACN_CACHE_DETAIL?.map(item => new AcnCacheDetail(item)) || [];
  }
}