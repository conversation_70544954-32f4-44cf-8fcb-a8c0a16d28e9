# METS (MAINTENANCE EVENT TRACKING SYSTEM)
This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 18.0.7.

# Required Software Versions
Node - v20.15.0
Npm - 10.7.0
Angular@CLI - 18.1.3

# Node, Npm & Angular Installation Steps
1. Download the Node V20.15.0 from this official site (https://nodejs.org/en/blog/release/v20.15.0).
2. Install the downloaded file in local. This will also install the npm in local.
3. Open command prompt and check the installed versions for Node & Npm. Use command "node -v" for checking Node verion and for checking NPM version use "npm -v".
4. Check if the installed versions of Node & Npm are same as above.
5. In command prompt run "npm install -g @angular/cli" for installing the angular in local machine.
6. Check installed angular version usng command "ng version".
7. If the angular version differs from the above then use this commmand "npm install -g @angular/cli@18.1.3" or  "npm install @angular/cli@18.1.3 --save-dev".
8. For angular installation reference use the official link from angular (https://angular.dev/installation).

# Application Local Set-up Steps
1. Clone the application from the GitHub repo into your local folder.
2. Go inide the project (cd eai-1839-eai-1839-mets-server-nextgen).
3. Open command prompt from the folder address and execute command code . (OR) Instead you can directly open VS Code and use Open Folder option to select the cloned project. 
4. Open terminal (CTRL + SHIFT + `) and run command "npm install"
5. This will download all the dependencies in the project under the name "node_modules" name.
6. run the command "npm start" or "ng serve" to start the application in local

## Development server
Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Build
Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests
Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests
Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.