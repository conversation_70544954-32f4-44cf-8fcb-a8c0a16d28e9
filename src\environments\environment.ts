export const environment = {
    production: false,
    api: {
        // baseUrl: "https://mets-l4-las-batch.test.cloud.fedex.com",
        baseUrl: "http://localhost:8999",
        extension: "/api/mets",
        endpoints: {
            preferences: {
                preferencesEndpoint: '/preferences',
                getUserPreferences: '/getPreferences',
                saveUserPreferences: '/setPreferences'
            },
            retrieval: {
                retrievalEndpoint: '/retrieval',
                eventListView: '/list-view',
                regions: '/regions/all',
                station: '/stations/',
                reportingCategories: '/rpt-catgories',
                niwtimers: '/niw-timers',
                allniwtimers: '/all-niw-timers',
                tubFileNotes: '/tfNotes/',
                detailView: '/detail-view/',
                openDiscrepancies: '/open-discrepancies',
                msnDetails: '/shortagenotice/',
                msnShippingInfo: '/msnShipping/',
                msnShippingDetail: '/msnDetail/',
                discrepancyDetailView: '/discrepancy-detail/',
                managersList: '/managers/',
                flightEticInfo: '/fltEtic/',
                flightLegDetails: '/flightLeg/',
                acnCache: '/acn-cache',
                flightChecks: '/getFlightChecks',
                user: '/users/',
                sendEmail: '/retrieval/sendEmail',
                discrepancyUpdatedText: '/discrepancy-txt',
                permissions: {
                    getActions: '/getActions',
                }
            },
            update: {
                updateEndpoint: '/update',
                event_detail: '/event_detail',
                reportingCategories: '/rpt-catg',
                niwTimersUpdate: '/niw-timers',
                discrepancyUpdate: "/discrepancy",
                flightLegDetails: '/flightLeg/',
                acnCache: '/acn-cache',
                tfNote: '/update/tfnotes',
            },
            addEvent: '/addEvent',
            close: '/closeEvent',
            userIntakeForm:{
                create: '/userIntakeForm/create',
                getAllUserIntakeForms: '/userIntakeForm/getAllUserIntakeForms',
                updateIntakeForm: '/userIntakeForm/updateIntakeForm',
                getRoleAndEventType: '/userIntakeForm/getRoleAndEventType',
                deleteUserIntakeForm: '/userIntakeForm/deleteUserIntakeForm',
                deleteQuestion: '/userIntakeForm/deleteQuestion',
                getAllQuestions: '/userIntakeForm/getAllQuestions',
                updateQuestion: '/userIntakeForm/updateQuestion',
                addNewQuestion: '/userIntakeForm/addNewQuestions',
                getUserIntakeForm: '/userIntakeForm/getUserIntakeForm',
                getIntakeForms: '/userIntakeForm/getIntakeForms',
            },
            listActions: {
                changestatus: '/changeEvent'
            }
        },
        auth: {
            username: 'user',
            password: 'user123'
        }
    },
    okta: {
        clientId: '0oa202xgw4ckRxueh0h8',
        issuer: 'https://purpleid-stage.oktapreview.com/oauth2/aus1kmc1lccSd9aSX0h8',
        redirectUri: window.location.origin + "/authorization-code/callback",
        scopes: ['openid', 'profile'],
        pkce: true,
        tokenManager: {
          autoRenew: true,
          expireEarlySeconds: 300
        }
    },
    // okta: {
    //     clientId: "0oa1giq11gm3L7kMf0h8",
    //     issuer: "https://purpleid-test.oktapreview.com/oauth2/aus1gipvjr85gnnGM0h8",
    //     redirectUri: window.location.origin + "/authorization-code/callback",
    //     // redirectUri: "https://mets-l4-las-admin.test.cloud.fedex.com/mets-server-nextgen-ui/authorization-code/callback",
    //     // redirectUri: "http://localhost:8080/authorization-code/callback",
    //     scopes: ['openid', 'profile'],
    //     pkce: true,
    //     tokenManager: {
    //       autoRenew: true,
    //       expireEarlySeconds: 300
    //     }
    // }
};