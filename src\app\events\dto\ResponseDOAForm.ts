export class ResponseDOAForm {
    data: Data;
  
    constructor() {
      this.data = new Data();
    }
  }
  
  class Data {
    DETAIL_VIEW_OBJECT: DetailViewObject;
    CURRENT_TIME: string;
  
    constructor() {
      this.DETAIL_VIEW_OBJECT = new DetailViewObject();
      this.CURRENT_TIME = '';
    }
  }
  
  class DetailViewObject {
    eventID: number;
    eventType: string;
    startDateTime: string;
    startDateTimeUTC: string;
    endDateTime: string | null;
    eventACN: string;
    eventFleetDesc: string;
    eventStation: string;
    eventStatus: string;
    eventEticDateTime: string;
    eventEticText: string | null;
    eventCurrentComment: string;
    eventOST: string;
    eventLastUpdateDateTime: string;
    eventLastUpdatedBy: string;
    eventCreatedDateTime: string;
    eventCreatedBy: string;
    eventOnwerGroupId: string;
    acOwnerGroupId: string;
    errorText: string | null;
    gate: string | null;
    mxSpeedDial: string | null;
    crew: string | null;
    contact: string | null;
    eventEticReasonCd: string;
    eventEticReasonComment: string;
    inboundFlightNumber: string;
    inboundFlightDate: string;
    inboundLegNumber: string;
    inboundLegDate: string;
    inboundOrigination: string;
    inboundFlightDepartureTime: string;
    inboundDestination: string;
    inboundArrivalDate: string;
    inboundArrivalTime: string;
    outboundFlightNumber: string;
    outboundFlightDate: string;
    outboundLegNumber: string;
    outboundLegDate: string;
    outboundOrigination: string;
    outboundFlightDepartureTime: string;
    outboundDestination: string;
    outboundArrivalDate: string;
    equipmentType: string;
    doaChangeIndicator: string | null;
    testFlightList: string | null;
    roadTripList: any[];
    testFlightData: string | null;
    activeTimer: boolean;
    doaAlert: boolean;
    numberOfDiscrepancies: string;
    requestStatus: string;
    changeType: number;
    linkedDiscList: LinkedDisc[];
    numberOfMSN: string;
    groupId: string;
    contactInfoOwnerList: string[];
    doaData: string | null;
    isEventCancelled: boolean;
    eventOriginalComment: string;
    isEventActive: boolean;
    managerNote: string;
    eventNewStatus: string;
    doaFlightNumber: string | null;
    doaFlightDate: string | null;
    doaFlightLegNumber: string | null;
    resMgrId: string | null;
    memDeskContact: string | null;
    duration: string;
    eventActive: boolean;
    eventCancelled: boolean;
  
    constructor() {
      this.eventID = 0;
      this.eventType = '';
      this.startDateTime = '';
      this.startDateTimeUTC = '';
      this.endDateTime = null;
      this.eventACN = '';
      this.eventFleetDesc = '';
      this.eventStation = '';
      this.eventStatus = '';
      this.eventEticDateTime = '';
      this.eventEticText = null;
      this.eventCurrentComment = '';
      this.eventOST = '';
      this.eventLastUpdateDateTime = '';
      this.eventLastUpdatedBy = '';
      this.eventCreatedDateTime = '';
      this.eventCreatedBy = '';
      this.eventOnwerGroupId = '';
      this.acOwnerGroupId = '';
      this.errorText = null;
      this.gate = null;
      this.mxSpeedDial = null;
      this.crew = null;
      this.contact = null;
      this.eventEticReasonCd = '';
      this.eventEticReasonComment = '';
      this.inboundFlightNumber = '';
      this.inboundFlightDate = '';
      this.inboundLegNumber = '';
      this.inboundLegDate = '';
      this.inboundOrigination = '';
      this.inboundFlightDepartureTime = '';
      this.inboundDestination = '';
      this.inboundArrivalDate = '';
      this.inboundArrivalTime = '';
      this.outboundFlightNumber = '';
      this.outboundFlightDate = '';
      this.outboundLegNumber = '';
      this.outboundLegDate = '';
      this.outboundOrigination = '';
      this.outboundFlightDepartureTime = '';
      this.outboundDestination = '';
      this.outboundArrivalDate = '';
      this.equipmentType = '';
      this.doaChangeIndicator = null;
      this.testFlightList = null;
      this.roadTripList = [];
      this.testFlightData = null;
      this.activeTimer = false;
      this.doaAlert = false;
      this.numberOfDiscrepancies = '';
      this.requestStatus = '';
      this.changeType = 0;
      this.linkedDiscList = [];
      this.numberOfMSN = '';
      this.groupId = '';
      this.contactInfoOwnerList = [];
      this.doaData = null;
      this.isEventCancelled = false;
      this.eventOriginalComment = '';
      this.isEventActive = false;
      this.managerNote = '';
      this.eventNewStatus = '';
      this.doaFlightNumber = null;
      this.doaFlightDate = null;
      this.doaFlightLegNumber = null;
      this.resMgrId = null;
      this.memDeskContact = null;
      this.duration = '';
      this.eventActive = false;
      this.eventCancelled = false;
    }
  }
  
  class LinkedDisc {
    ata: string;
    discrepancy: string;
    discrepancyText: string | null;
    eventType: string | null;
  
    constructor() {
      this.ata = '';
      this.discrepancy = '';
      this.discrepancyText = null;
      this.eventType = null;
    }
  }