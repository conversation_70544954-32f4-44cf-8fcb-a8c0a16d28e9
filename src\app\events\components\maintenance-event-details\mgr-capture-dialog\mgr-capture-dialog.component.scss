.managerNotesCaptureContainer {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 2%;
    height: 80%;
    padding: 1%;
}

// .managerNotesCaptureHeader{
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     margin-bottom: 10px;
// }
.mgrNotesCaptureHeader {
    display: flex;
    width: 50%;
    align-items: center;
    background-color: #4D148C;
    color: white;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px;
}

.mgrNotesCaptureContent {
    display: flex;
    flex-direction: column;
    width: 95%;
    gap: 8px;
    border-radius: 8px;
}


.accordion {
    display: flex;
    background-color: #4D148C;
    color: white;
    cursor: pointer;
    padding: 18px;
    width: 100%;
    text-align: left;
    border: none;
    outline: none;
    transition: 0.4s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px;
    justify-content: space-between;
}

// .accordion:after {
//     content: '\25BC';
//     // color: #777;
//     color: white;
//     font-weight: bold;
//     float: right;
//     margin-left: 5px;
// }

// .accordion.active:after {
//     content: "\25B2";
//     color: #4D148C;
// }

.accordion.active {
    background-color: white;
    color: #4D148C;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px;
    border: 1px solid #4D148C;
}


.accordion-icon {
    width: 1em;
    height: 1em;
    border-left: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(-45deg);
    transition: transform 0.3s ease;
    margin-top: 0.4%;
}

.accordion.active .accordion-icon {
    transform: rotate(135deg);
    border-color: #5a20cb;
}

.panel {
    background-color: #EAE3F2;
    display: flex;
    flex-direction: column;
    margin: 1% 0 1%;
    align-items: center;
}

.discrepancy-container {
    display: flex;
    flex-direction: column;
    width: 81%;
    margin: 1% 0 1%;
}


.discrepancy-drop-drown {
    display: flex;
    width: 100%;
    background-color: #D8C1F1;
    border: 1px solid #D8C1F1;
    padding: 2%;
    text-align: left;
    cursor: pointer;
    position: relative;
    margin-bottom: 1%;
    box-sizing: border-box;
    border-radius: 8px;
    justify-content: space-between;
}

.discrepancy-drop-drown::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background-color: #4D148C;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.discrepancy-icon {
    width: 1em;
    height: 1em;
    border-left: 2px solid #333;
    border-bottom: 2px solid #333;
    transform: rotate(-45deg);
    transition: transform 0.3s ease;
}

.discrepancy-drop-drown.active .discrepancy-icon {
    transform: rotate(135deg);
    border-color: #5a20cb;
}

.discrepancy-content {
    display: flex;
    width: 100%;
    border: 1px solid #FAE4D6;
    background-color: #FAE4D6;
    margin: 1% 0 1%;
    padding: 2%;
    text-align: left;
    position: relative;
    box-sizing: border-box;
    border-radius: 8px;
}

.discrepancy-content::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background-color: #D65200;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

::ng-deep.mdc-checkbox__background {
    background-color: white;
}

::ng-deep.mat-mdc-dialog-container {
    max-width: none !important;
}

.button-container {
    padding-left: 84%;
    display: flex;
    justify-content: end;
    gap: 3%;
    margin: 0.5% 0;
}

.mat-button1 {
    color: #4D148C !important;
    background-color: white !important;
}

.mat-button1:hover {
    background-color: #4D148C !important;
    color: white !important;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.formatted-text {
    white-space: pre-wrap;
}

.discrepancy-checkbox {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    align-items: center;
}

.checkbox-card {
    background-color: white;
    border: 1px solid #D8C1F1;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: 0.3s ease;
    width: 60%;
}

.checkbox-card:hover {
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}


.tfNotes-container {
    display: flex;
    flex-direction: column;
    width: 81%;
    margin: 1% 0;
    // align-items: center;
}

.checkbox-card {
    background-color: white;
    border: 1px solid #D8C1F1;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: 0.3s ease;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}


.checkbox-card:hover {
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.tfNote-main {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tfNote-footer {
    display: flex;
    justify-content: space-between;
    font-size: 0.85em;
    color: #555;
    border-top: 1px solid #eee;
    padding-top: 4px;
}

.tf-metaData {
    font-style: italic;
}

