import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { MsnShippingDetailsComponent } from '../msn-shipping-details/msn-shipping-details.component';
import { SortModelItem } from 'ag-grid-community';

@Component({
  selector: 'app-custom-msn-checkbox',
  standalone: false,
  templateUrl: './custom-msn-checkbox.component.html',
  styleUrl: './custom-msn-checkbox.component.scss'
})
export class CustomMsnCheckboxComponent implements ICellRendererAngularComp {

  params: any;
  value!: string;
  checked: boolean = false;
  isDisabled: boolean = false;

  constructor(private dialog: MatDialog, private cdr: ChangeDetectorRef) {}

  agInit(params: any): void {
    this.params = params;
    this.value = params.value;
    // this.checked = params.data.isChecked || false;
    
    const matchedMsns = params.data?.matchedMsns || [];
    this.checked = matchedMsns.includes(this.value.toString()) || false;

     this.isDisabled = params.data?.isLinkedMsn;

    this.params.data.isChecked = this.checked;
    this.cdr.detectChanges();
  }

  refresh(params: any): boolean {
    this.value = params.value;
    const matchedMsns = params.data?.matchedMsns || [];
    this.checked = matchedMsns.includes(this.value.toString());

    return true;
  }

  onCheckboxChange(event: Event) {
    this.checked = (event.target as HTMLInputElement).checked;

    this.params.data.isChecked = this.checked;
    this.params.api.applyTransaction({ update: [this.params.data] });
     this.params.api.onSortChanged();


    this.params.api.dispatchEvent({
      type: "cellValueChanged",
      column: this.params.column,
      colDef: this.params.colDef,
      data: this.params.data,
      node: this.params.node,
      oldValue: !this.checked,
      newValue: this.checked,
    });
  }

  openMsnShippingDetails(msn: number): void {
    this.dialog.open(MsnShippingDetailsComponent, {
      data: { msn },
      disableClose: true,
      autoFocus: false,
      width: '95vw',
      maxWidth: '100vw',
      panelClass: 'custom-msn-dialog'
    });
  }
  
}