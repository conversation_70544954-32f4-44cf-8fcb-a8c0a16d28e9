<div class="create-intake-form-container">
  <div class="intake-form-drag-box">
      <div class="intake-form-drag-box-title">{{title}}</div>
      <div class="create-new-question-container">
          <div class="icon-text-container" (click)="addNewQuestion()">
              <mat-icon>add</mat-icon>
              <div class="create-new-question">Create New Question</div>
          </div>
      </div>
      <div class="intake-form-scrollable-content">
          <div cdkDropList #todoList="cdkDropList" [cdkDropListData]="todo" [cdkDropListConnectedTo]="[doneList]"
              class="intake-form-questions-list" (cdkDropListDropped)="drop($event)">
              @for (item of todo; track item; let i = $index) {
              <div class="intake-form-box" cdkDrag>
                  <div class="question-details">
                      <p class="question-text">Question: {{item.questionTxt}}</p>
                      <p class="question-type">Answer Type: {{item.questionGrp}}</p>
                      <p class="question-required" *ngIf="item.required">Required</p>
                      <p class="question-answers" *ngIf="item.questionGrp === 'RADIO' || item.questionGrp === 'MULTIPLE'">Answers: {{getAnswersText(item)}}</p>
                  </div>
                  <div class="intake-form-box-actions">
                      <mat-icon class="edit-icon" (click)="editQuestion(item)">edit</mat-icon>
                      <mat-icon class="delete-icon" (click)="deleteQuestion(item)">delete</mat-icon>
                  </div>
              </div>
              }
          </div>
      </div>
      <div class="button-container">
          <div class="submit-button">
              <button mat-raised-button class="mat-button1" [disabled]="!todo.length" (click)="OnSubmit()">Submit</button>
          </div>
          <div class="action-button">
              <button mat-raised-button class="mat-button1" (click)="OnCancel()">Cancel</button>
          </div>
      </div>
  </div>
  <div class="divider"></div>
  <div class="previous-questions-container">
      <div class="previous-questions-title-container">
          <div class="previous-questions-title">Questions list</div>
          <div class="search-container">
              <mat-icon class="search-icon">search</mat-icon>
              <input type="text" class="search-input" placeholder="Search questions..." 
                     [(ngModel)]="searchTerm" (input)="filterQuestions()">
              <div class="search-border-animation"></div>
          </div>
      </div>
      <div class="previous-questions-scrollable-content">
          <div cdkDropList #doneList="cdkDropList" [cdkDropListData]="filteredQuestions" [cdkDropListConnectedTo]="[todoList]"
              class="questions-list" (cdkDropListDropped)="drop($event)">
              @for (item of filteredQuestions; track item) {
              <div class="intake-form-box" cdkDrag>
                  <div class="question-details">
                      <p class="question-text">Question: {{item.questionTxt}}</p>
                      <p class="question-type">Question Type: {{item.questionGrp}}</p>
                      <p class="question-required" *ngIf="item.required">Required</p>
                      <p class="question-answers" *ngIf="item.questionGrp === 'RADIO' || item.questionGrp === 'MULTIPLE'">Answers: {{getAnswersText(item)}}</p>
                  </div>
              </div>
              }
          </div>
      </div>
  </div>
</div>