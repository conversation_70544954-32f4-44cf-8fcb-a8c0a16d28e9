import { DetailViewResponseDao } from "../dao/detailViewDao";

export class MetsEventUpdateEntity {
  mode: string;
  acn: string;
  group_id: string;
  event_id: string;
  access_level: string;
  timer_id: string;
  flag: string;
  request_type: string;
  user_id: string;
  token_id: string;
  discrepancy_filter: string;
  discrepancy_from_date: string;
  discrepancy_to_date: string;
  discrepancy_SPAN: string;
  comment_updated: boolean;
  event_active: boolean;
  niw_timer_data: any;
  tf_notes_data: any;
  flight_etic_data: any;
  event_doa_data: any;
  detail_view_data?: DetailViewResponseDao;
  start_date_time: string;
  email_data: any;
  event_discrepancy_data: any[];
  report_categories_data: any[];
  wlm_niw_timer_start_time: Date | null;
  wlm_niw_timer_stop_time: Date | null;

  constructor(data: Partial<MetsEventUpdateEntity> = {}) {
    this.mode = data.mode || 'EVENT_UPDATE';
    this.acn = data.acn || '';
    this.group_id = data.group_id || '';
    this.event_id = data.event_id || '';
    this.access_level = data.access_level || '';
    this.timer_id = data.timer_id || '';
    this.flag = data.flag || '';
    this.request_type = data.request_type || '';
    this.user_id = data.user_id || '';
    this.token_id = data.token_id || '';
    this.discrepancy_filter = data.discrepancy_filter || '';
    this.discrepancy_from_date = data.discrepancy_from_date || '';
    this.discrepancy_to_date = data.discrepancy_to_date || '';
    this.discrepancy_SPAN = data.discrepancy_SPAN || '';
    this.comment_updated = data.comment_updated ?? false;
    this.event_active = data.event_active ?? true;

    this.niw_timer_data = data.niw_timer_data ?? null;
    this.tf_notes_data = data.tf_notes_data ?? null;
    this.flight_etic_data = data.flight_etic_data ?? null;
    this.event_doa_data = data.event_doa_data ?? null;

    this.detail_view_data = data.detail_view_data ?? {
      eventID: 0,
      eventType: '',
      startDateTime: '',
      startDateTimeUTC: '',
      endDateTime: '',
      eventACN: '',
      eventFleetDesc: '',
      eventStation: '',
      eventStatus: '',
      eventEticDateTime: null,
      eventEticText: '',
      eventCurrentComment: '',
      eventOST: null,
      eventLastUpdateDateTime: '',
      eventLastUpdatedBy: '',
      eventCreatedDateTime: '',
      eventCreatedBy: '',
      eventOnwerGroupId: '',
      acOwnerGroupId: '',
      errorText: null,
      gate: null,
      mxSpeedDial: null,
      crew: null,
      contact: '',
      eventEticReasonCd: null,
      eventEticReasonComment: null,
      inboundFlightNumber: null,
      inboundFlightDate: null,
      inboundLegNumber: null,
      inboundLegDate: null,
      inboundOrigination: null,
      inboundFlightDepartureTime: null,
      inboundDestination: null,
      inboundArrivalDate: null,
      inboundArrivalTime: null,
      outboundFlightNumber: null,
      outboundFlightDate: null,
      outboundLegNumber: null,
      outboundLegDate: null,
      outboundOrigination: null,
      outboundFlightDepartureTime: null,
      outboundDestination: null,
      outboundArrivalDate: null,
      equipmentType: '',
      activeTimer: false,
      doaAlert: false,
      numberOfDiscrepancies: '',
      requestStatus: '',
      changeType: 0,
      linkedDiscList: [],
      linkedMsns: [],
      numberOfMSN: '',
      groupId: '',
      contactInfoOwnerList: [],
      doaData: null,
      isEventCancelled: false,
      eventOriginalComment: '',
      isEventActive: false,
      managerNote: '',
      eventNewStatus: '',
      doaFlightNumber: null,
      doaFlightDate: null,
      doaFlightLegNumber: null,
      resMgrId: '',
      memDeskContact: '',
      duration: '',
      eventCancelled: false,
      eventActive: false
    };

    this.start_date_time = data.start_date_time || '';
    this.email_data = data.email_data ?? null;
    this.event_discrepancy_data = data.event_discrepancy_data || [];
    this.report_categories_data = data.report_categories_data || [];
    this.wlm_niw_timer_start_time = data.wlm_niw_timer_start_time ?? null;
    this.wlm_niw_timer_stop_time = data.wlm_niw_timer_stop_time ?? null;
  }
}
