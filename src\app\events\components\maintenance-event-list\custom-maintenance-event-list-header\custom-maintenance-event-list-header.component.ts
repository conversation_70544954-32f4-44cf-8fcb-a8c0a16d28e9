import { Component, ElementRef, ViewChild } from '@angular/core';
import { IHeaderAngularComp } from 'ag-grid-angular';
import { IHeaderParams, Column } from 'ag-grid-community';

@Component({
    selector: 'app-custom-maintenance-event-list-header',
    templateUrl: './custom-maintenance-event-list-header.component.html',
    styleUrls: ['./custom-maintenance-event-list-header.component.scss'],
    standalone: false
})
export class CustomMaintenanceEventListHeaderComponent implements IHeaderAngularComp {
  public displayHeaderName: string = '';
  public params!: IHeaderParams;
  public isFilterActive: boolean = false;
  public sortDirection: 'asc' | 'desc' | null = null;

  @ViewChild('filterIcon', { static: false }) filterIconRef!: ElementRef;

  agInit(params: IHeaderParams): void {
    this.params = params;
    this.displayHeaderName = params.displayName || '';
  
    if (this.params.column.getColId() !== 'settings') {
      this.updateFilterState();
      this.params.api.addEventListener('filterChanged', this.updateFilterState.bind(this));
  
      this.updateSortState();
      this.params.api.addEventListener('sortChanged', this.updateSortState.bind(this));
    }
  }
  

  refresh(params: IHeaderParams): boolean {
    this.agInit(params);
    return true;
  }

  onFilterRequested(event: MouseEvent): void {
    event.stopPropagation();
    if (this.params.column.getColId() !== 'settings') {
      this.params.api.showColumnMenu(this.params.column);
    }
  }  

  onSortRequested(event: MouseEvent): void {
    event.stopPropagation();
    if (this.params.column.getColId() !== 'settings') {
      const newSort = this.sortDirection === 'asc' ? 'desc' : 'asc';
      this.params.setSort(newSort, event.shiftKey);
      this.sortDirection = newSort;
      this.params.api.refreshHeader();
    }
  }  

  private updateFilterState(): void {
    if (this.params && this.params.column) {
      this.isFilterActive = this.params.column.isFilterActive();
    }
  }

  private updateSortState(): void {
    const sortedColumns = this.params.api.getAllGridColumns().filter((col: Column) => {
      return col.getSort() !== null;
    });
    const isCurrentColumnSorted = sortedColumns.some((col: Column) => col.getColId() === this.params.column.getColId());
    this.sortDirection = isCurrentColumnSorted ? (this.params.column.getSort() as 'asc' | 'desc' | null) : null;
  }
}
