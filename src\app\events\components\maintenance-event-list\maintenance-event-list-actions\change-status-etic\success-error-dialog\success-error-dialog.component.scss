.success-bg {
  background: linear-gradient(145deg, rgba(39, 174, 96, 0.25), rgba(39, 174, 96, 0.15));
}

.error-bg {
  background: linear-gradient(145deg, rgba(192, 57, 43, 0.25), rgba(192, 57, 43, 0.15));
}

.dialog-container {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15), 0 0 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 50vw;
  max-width: 600px;
  min-width: 300px;
  box-sizing: border-box;
  background: transparent;
}

.zoom-container {
  animation: zoomIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  width: 100%;
  height: 100%;
}

.dialog-content {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1));
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
}

mat-dialog-content {
  overflow: hidden !important;
}

.message-container {
  padding: 15px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  position: relative;
  animation: glow 2s ease-in-out infinite;
  transition: transform 0.3s ease;
}

.icon-wrapper:hover {
  transform: rotate(5deg);
}

.success-icon {
  background: rgba(39, 174, 96, 0.3);
  color: #27ae60;
}

.error-icon {
  background: rgba(192, 57, 43, 0.3);
  color: #c0392b;
}

.custom-icon {
  width: 48px;
  height: 48px;
  animation: spinPulse 2.5s ease-in-out infinite;
  z-index: 2;
}

.starburst {
  animation: starburst 2s ease-in-out infinite;
  transform-origin: center;
}

.message-text {
  font-size: 18px;
  line-height: 1.6;
  margin: 0;
  opacity: 0;
  animation: fadeIn 0.7s ease-out 0.4s forwards;
  white-space: pre-line;
  font-family: 'Inter', sans-serif;
  max-width: 100%;
  word-wrap: break-word;
  font-weight: 700;
}

.success-text {
  color: #2ecc71;
}

.error-text {
  color: #c0392b;
}

.dialog-actions {
  padding: 0 40px 32px;
  display: flex;
  justify-content: center;
}

.action-button {
  background: linear-gradient(90deg, #6c5ce7, #a29bfe);
  color: white;
  padding: 12px 40px;
  border-radius: 24px;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 15px;
  letter-spacing: 1.2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
  outline: none;
}

.action-button:hover {
  transform: scale(1.05) translateY(-4px);
  box-shadow: 0 8px 25px rgba(108, 92, 231, 0.6);
  background: linear-gradient(90deg, #a29bfe, #6c5ce7);
  animation: pulse 1.5s infinite;
}

.action-button:active {
  transform: scale(0.95) translateY(0);
  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
}

.timer-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 80px;
  opacity: 0.5;
  z-index: 1;
  filter: drop-shadow(0 0 5px rgba(39, 174, 96, 0.3));
}

.timer-ring circle {
  transform: rotate(-90deg);
  transform-origin: center;
}

@keyframes zoomIn {
  from {
    transform: scale(0.7);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2), 0 0 30px rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

@keyframes spinPulse {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(10deg) scale(1.15);
  }
  100% {
    transform: rotate(0deg) scale(1);
  }
}

@keyframes starburst {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(108, 92, 231, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(108, 92, 231, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(108, 92, 231, 0);
  }
}