export class UpdateDiscrepancyDto {
    access_level: string = '';
    user_id: string = '';
    event_discrepancy_data: EventDiscrepancyDataDto[] = [];
}

export class EventDiscrepancyDataDto {
    eventId: number = 0;
    link: string = '';
    ata: string = '';
    number: string = '';
    discType: string = '';
    eventType: string = '';
    openDate: string = '';
    openStation: string = '';
    inWork: string = '';
    closed: string = '';
    status: string = '';
    isModified: boolean = false;
    isLinkModified: boolean = false;
    isDowningModified: boolean = false;
    isDowningItem: boolean = false;
}