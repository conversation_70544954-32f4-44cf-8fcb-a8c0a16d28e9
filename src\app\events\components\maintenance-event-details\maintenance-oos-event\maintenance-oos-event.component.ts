import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MetsEventUpdateEntity } from '../../../dto/maintenance-event-update-dto';
import { DetailViewResponseDao } from '../../../dao/detailViewDao';

@Component({
  selector: 'app-maintenance-oos-event',
  standalone: false,
  templateUrl: './maintenance-oos-event.component.html',
  styleUrl: './maintenance-oos-event.component.scss'
})
export class MaintenanceOosEventComponent implements OnInit, OnChanges {

  @Input() stationList: string[] = [];
   
  fetchedSationList: string[] = [];
  contactValue: string = '';
  initialContactValue: string = '';
  isUpdateButtonDisabled: boolean = true;
  ownerValue: any;
  initialOwnerValue: any;
  station: string = '';
  initialStationValue: string = '';
  mgrNotes: string = '';
  initialMgrNotes: string = '';

  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;

  @Output() updatedDetailsViewObj = new EventEmitter<DetailViewResponseDao>();
  constructor(
  ) { }
  ngOnInit(): void {
    // Initialize the contact value with the input details
    this.fetchedSationList = this.stationList;
    this.initialContactValue = this.detailsViewObj.contact || ''; // Use empty string if undefined
    this.contactValue = this.initialContactValue;
    this.ownerValue = this.detailsViewObj.acOwnerGroupId;
    this.initialOwnerValue=this.ownerValue;
    this.initialStationValue = this.detailsViewObj.eventStation;
    this.station = this.initialStationValue;
    this.initialMgrNotes = this.detailsViewObj.managerNote;
    this.mgrNotes = this.initialMgrNotes;
    var toggle = true;
    document.getElementById('start-card')?.addEventListener("mouseenter", () => {
      setTimeout(() => {
        this.rotateCard(toggle);
        setTimeout(() => {
          this.rotateCard(toggle);
          toggle = !toggle;
        }, 200);
        toggle = !toggle;
      }, 200);
    });
}

rotateCard(toggle:boolean){
  if(toggle){
    document.getElementById('start-card')?.classList.add("rotate30");
  }
  else{
    document.getElementById('start-card')?.classList.remove("rotate30");
  }
}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      this.fetchedSationList = this.stationList;
      this.initialContactValue = this.detailsViewObj.contact || '';
      this.contactValue = this.initialContactValue;
      this.initialStationValue = this.detailsViewObj.eventStation || '';
      this.station = this.initialStationValue;
      this.initialMgrNotes = this.detailsViewObj.managerNote;
      this.ownerValue = this.detailsViewObj.acOwnerGroupId;
      this.initialOwnerValue = this.ownerValue;
      this.mgrNotes = this.initialMgrNotes;
      this.isUpdateButtonDisabled = true; // Disable button if there is no change
    }
  }

  // Method that runs whenever the contact value changes
  onContactChange(): void {
    // Check if the current contact value is different from the initial one
    if (this.contactValue !== this.initialContactValue) {
      this.isUpdateButtonDisabled = false; // Enable button if values are different
    } else {
      this.isUpdateButtonDisabled = true; // Disable button if values are the same
    }
    this.contactValue = this.contactValue;
  }

  onStationChange(): void {
    // Check if the current contact value is different from the initial one
    if (this.station !== this.initialStationValue) {
      this.isUpdateButtonDisabled = false; // Enable button if values are different
    } else {
      this.isUpdateButtonDisabled = true; // Disable button if values are the same
    }
    this.station = this.station;
  }

  onOwnerChange(): void {
    if (this.ownerValue !== this.initialOwnerValue) {
      this.isUpdateButtonDisabled = false; // Enable button if values are different
    } else {
      this.isUpdateButtonDisabled = true; // Disable button if values are the same
    }
    this.ownerValue = this.ownerValue;
  }

  onMgrNotesChange(): void {
    // Check if the current contact value is different from the initial one
    if (this.mgrNotes !== this.initialMgrNotes) {
      this.isUpdateButtonDisabled = false; // Enable button if values are different
    } else {
      this.isUpdateButtonDisabled = true; // Disable button if values are the same
    }
    this.mgrNotes = this.mgrNotes;
  }

  action(){

  }

  // Update method to handle the update button click
  update(): void {
    const detail_view_data: DetailViewResponseDao = {
      ...this.detailsViewObj,
      managerNote: this.mgrNotes,
      contact: this.contactValue,
      eventStation: this.station
    };
    this.detailsViewObj.managerNote = this.mgrNotes
    this.detailsViewObj = this.detailsViewObj;
    // if (maintenanceEventListDto.detail_view_data) {
    //   maintenanceEventListDto.detail_view_data.managerNote = this.mgrNotes;
    //   maintenanceEventListDto.detail_view_data.contact = this.contactValue;
    //   maintenanceEventListDto.detail_view_data.eventStation = this.station;
    //   maintenanceEventListDto.detail_view_data=this.detailsViewObj
    // }
    this.isUpdateButtonDisabled = true; // Disable button after update
    this.updatedDetailsViewObj.emit(detail_view_data);
  }

}
