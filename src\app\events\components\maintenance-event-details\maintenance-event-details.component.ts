import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, inject, Input, NgZone, OnDestroy, OnInit, Output, Renderer2, SimpleChanges, ViewChild } from '@angular/core';
import { MaintenanceEventDetailsService } from '../../services/maintenance-event-details.service';
import { OosEventDetailsInterface } from './maintenance-event-details-interface';
import { ActivatedRoute, Router } from '@angular/router';
import { MainService } from '../../../app-layout/services/main.service';
import { MainComponent } from '../../../app-layout/components/main/main.component';
import { AppLayoutService } from '../../../app-layout/services/app-layout.service';
import { DetailViewResponseDao } from '../../dao/detailViewDao';
import { AddEventService } from '../../services/add-event.service';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { AircraftInfoChecks } from '../../dao/aircraftInfoChecksDao';
import { FlightStatusChckService } from '../../services/flight-status-chck.service';
import { STATUS_COLOR_MAP, TEXT_COLOR_MAP } from '../../constants/colormap-constants';
import { MatDialog } from '@angular/material/dialog';
import { MsnShippingDetailsComponent } from './msn/msn-shipping-details/msn-shipping-details.component';
import { MsnDetailsInfoComponent } from './msn/msn-details-info/msn-details-info.component';
import { UserDto } from '../../dto/UserInfo';
import { MaintenanceEventListService } from '../../services/maintenance-event-list.service';
import { DiscrepanciesComponent } from './discrepancies/discrepancies.component';
import { MgrCaptureDialogComponent } from './mgr-capture-dialog/mgr-capture-dialog.component';
import { DiscrepancySelectedTexts } from '../../dao/discrepancyUpdTxt';
import Swal from 'sweetalert2';
import { MaintenanceEventDetailsSharedService } from './maintenance-event-details-shared.service';
import { DiscrepanciesList } from '../../dao/discrepancies-listDao';
import { TubFileNotesResponseDto } from '../../dto/TubFileNotesResponseDto';
import { EventFlightEticResponseDao } from '../../dao/event-flight-etic-detailsDao';
import { TfEmailComponent } from './tub-file-notes/tf-email/tf-email.component';
import { TfEmailNoteComponent } from './tub-file-notes/tf-email-note/tf-email-note.component';
import { TfEditorComponent } from './tub-file-notes/tf-editor/tf-editor.component';
import { ToastrMessageService } from '../../../app-layout/services/toastr-message.service';
import { FlightLeg } from '../../dto/FlightLeg';
import { IntakeFormComponent } from '../add-maintenance-event/intake-form/intake-form.component';


@Component({
  selector: 'app-maintenance-event-details',
  templateUrl: './maintenance-event-details.component.html',
  styleUrl: './maintenance-event-details.component.scss',
  standalone: false
})
export class MaintenanceEventDetailsComponent implements AfterViewInit, OnInit, OnDestroy {

  @ViewChild('stickyHeader') stickyHeader!: ElementRef;
  @ViewChild('customCard', { static: false }) customCard!: ElementRef;
  @ViewChild('customPages', { static: false }) customPages!: ElementRef;
  @ViewChild('content', { static: false }) content!: ElementRef;
  @ViewChild('emrStatusContent') emrStatusContent!: ElementRef;

  @ViewChild('quadrant1') quadrant1!: ElementRef;
  @ViewChild('quadrant2') quadrant2!: ElementRef;
  @ViewChild('quadrant3') quadrant3!: ElementRef;
  @ViewChild('quadrant4') quadrant4!: ElementRef;

  @ViewChild('secondQuadrant1') secondQuadrant1!: ElementRef;
  @ViewChild('secondQuadrant2') secondQuadrant2!: ElementRef;
  @ViewChild(DiscrepanciesComponent) discrepanciesComponent!: DiscrepanciesComponent;
  @ViewChild(IntakeFormComponent) intakeFormsComponent!: IntakeFormComponent;

  @Input() stationList: string[] = [];
  @Input() detailsViewObj: DetailViewResponseDao = {} as DetailViewResponseDao;
  @Output() updatedDetailsViewObj = new EventEmitter<DetailViewResponseDao>();

  isNoteSelected = false;
  selectedNotes: string = '';
  formatedSelectedNote: string = '';
  allNotes: string = '';
  tfDateTime: any;
  eventId: string = '';

  currentQuadrant: HTMLElement | null = null;
  startWidth = 0;
  startHeight = 0;
  isSideNavClosed: boolean = false;

  fetchedSationList: string[] = [];

  private renderer: Renderer2 = inject(Renderer2);
  private destroy$ = new Subject<void>();
  private aircraftInfoChecksSubject = new BehaviorSubject<AircraftInfoChecks | null>(null);
  eventFlightEticDetails: EventFlightEticResponseDao = {} as EventFlightEticResponseDao;

  currentPage: number = 1;
  pages: number[] = [1, 2, 3, 4, 5, 6];
  acn: string = '';
  pageMap = new Map<number, string>();
  detailViewData: DetailViewResponseDao = {} as DetailViewResponseDao;
  pageContainerHeight: number = 0;
  customPageHeight: number = 0;
  customPageDown: number = 0;
  aircraftInfoChecks: AircraftInfoChecks = new AircraftInfoChecks();
  colorMap = STATUS_COLOR_MAP;
  textColorMap = TEXT_COLOR_MAP;
  linkedDiscrepancies: DiscrepanciesList[] = [];

  stationsList: string[] = [];
  station: string = '';
  initialStationValue: string = '';
  isUpdateButtonDisabled: boolean = true;
  contactValue: string = '';
  initialContactValue: string = '';
  ownerValue: any;
  initialOwnerValue: any;
  mgrNotes: string = ' ';
  initialMgrNotes: string = '';
  userInfo!: UserDto;
  userMail: string = '';

  partsList = [];
  selectedPart: any = null;

  isDiscrepanciesViewEnabled: boolean = false;
  isDiscrepanciesUpdateEnabled: boolean = false;
  isIntakeFormUpdateEnabled: boolean = false;
  selectedDiscrepancyTxts: DiscrepancySelectedTexts[] = [];
  isMgrNotesDisabled: boolean = true;
  selectedTfNotes: TubFileNotesResponseDto[] = [];
  OssDestails: OosEventDetailsInterface = { Type: 'B777-FS2', Flt_Dt: '9083/11', Dept: '02/11 07:30', Gate: 'A1', Dest: 'IND' };
  
  gridTemplateColumns = '50% 50%';
  gridTemplateRows = '50% 50%';
  secondScreenGridColumns = '50% 50%';
  activePanel: 'fs' | 'ers' | 'parts' | null = null;
  private isResizing = false;
  private resizeDirection: 'horizontal' | 'vertical' | 'vertical-second' | null = null;
  private startX = 0;
  private startY = 0;
  upcomingFltList: FlightLeg[] = [];
  inboundFltList: FlightLeg[] = [];
  inboundFlight: FlightLeg = {} as FlightLeg;
  displayedColumns: string[] = ['category', 'flight', 'oriDep', 'destArr'];
  flights: FlightLeg[] = [];

  private verticalPosition: number = 50;
  private horizontalPosition: number = 50;
  private secondVerticalPosition: number = 50;

  constructor(
    private maintenaceEventDetailsService: MaintenanceEventDetailsService,
    private maintenanceEventListService: MaintenanceEventListService,
    private appLayoutService: AppLayoutService,
    private mainService: MainService,
    private route: ActivatedRoute,
    private cdRef: ChangeDetectorRef,
    private addEventService: AddEventService,
    private flightStatusChckService: FlightStatusChckService,
    private dialog: MatDialog,
    private maintenanceEventDetailsSharedService: MaintenanceEventDetailsSharedService,
    private toastrMessageService: ToastrMessageService,
  ) {
    const sideNavClosedValue = this.appLayoutService.getSideNavAndListScreenMenuClosedOptionsFromSessionStorage();
    this.appLayoutService.setSideNavClosedFromPreferences(sideNavClosedValue != null ? sideNavClosedValue?.sideNavClosed : false);
  }

  ngOnInit(): void {
    this.fetchedSationList = this.stationList;
    this.route.queryParamMap.subscribe(params => {
      this.acn = params.get('acn') || '';
      this.detailViewData = new DetailViewResponseDao();
      this.acn != "" ? this.getDetails() : null;
    });
    this.setMap();
    this.aircraftInfoChecksSubject.pipe(takeUntil(this.destroy$)).subscribe(checks => {
      this.aircraftInfoChecks = checks || new AircraftInfoChecks();
      if (this.emrStatusContent) {
        this.applyBackgroundColor();
      }
    });
    this.maintenanceEventDetailsSharedService.linkedDiscrepancies$.subscribe((data) => {
      this.linkedDiscrepancies = data;
    });
    this.maintenaceEventDetailsService.getUserInfo().subscribe(response => {
      this.userInfo = response;
      if (this.userInfo != null) {
        this.userMail = this.userInfo.mail;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['detailsViewObj'] && !changes['detailsViewObj'].isFirstChange()) {
      this.fetchedSationList = this.stationList;
      this.initialContactValue = this.detailsViewObj.contact || '';
      this.contactValue = this.initialContactValue;
      this.initialStationValue = this.detailsViewObj.eventStation || '';
      this.station = this.initialStationValue;
      this.initialMgrNotes = this.detailViewData.managerNote ? decodeURIComponent(this.detailViewData.managerNote) : '';
      this.ownerValue = this.detailsViewObj.acOwnerGroupId;
      this.initialOwnerValue = this.ownerValue;
      this.mgrNotes = this.initialMgrNotes;
      this.isUpdateButtonDisabled = true;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngAfterViewInit() {
    this.adjustLayout();
  }

  @HostListener('window:resize')
  onResize() {
    this.adjustLayout();
  }

  private adjustLayout() {
    setTimeout(() => {
      const totalHeight = window.innerHeight;
      const totalWidth = window.innerWidth;

      const customCardHeight = this.customCard?.nativeElement?.offsetHeight || 0;
      const stickyHeaderEl = this.stickyHeader?.nativeElement;
      const stickyVisible = this.currentPage > 1;
      const stickyHeaderHeight = (stickyVisible && stickyHeaderEl) ? stickyHeaderEl.offsetHeight : 0;

      const availableHeight = totalHeight - customCardHeight - stickyHeaderHeight;

      const customPages = this.customPages?.nativeElement.querySelectorAll('.custompage');
      customPages?.forEach((page: HTMLElement) => {
        page.style.height = `${availableHeight}px`;
      });

      const customPageDowns = this.customPages?.nativeElement.querySelectorAll('.customPageDown');
      customPageDowns?.forEach((pageDown: HTMLElement) => {
        pageDown.style.height = `${availableHeight}px`;
        pageDown.style.width = `${totalWidth * 0.9}px`;
        pageDown.style.position = 'absolute';
        pageDown.style.right = '0';
      });

      if (this.content?.nativeElement) {
        this.content.nativeElement.style.height = `${availableHeight}px`;
      }
    }, 100);
  }

  setMap() {
    this.pageMap.set(1, 'OOS Event');
    this.pageMap.set(2, 'Reporting Categories');
    this.pageMap.set(3, 'NIW Timers');
    this.pageMap.set(4, 'Discrepancies');
    this.pageMap.set(5, 'Material Shortage Notice');
    this.pageMap.set(6, 'Flight / ETIC Info');
  }

  onScroll(event: any): void {
    const scrollPosition = event.target.scrollTop;
    const pageHeight = this.content.nativeElement.clientHeight;
    const newPage = Math.floor(scrollPosition / pageHeight) + 1;

    this.currentPage = newPage;
    (this.currentPage >= 4) ? this.maintenaceEventDetailsService.showTitleEffects(true) : this.maintenaceEventDetailsService.showTitleEffects(false);
  }

  getDetails() {
    this.addEventService.validateAcnExists(this.acn).subscribe((acnExists) => {
      if (!acnExists) {
        this.toastrMessageService.error(`ACN ${this.acn} does not exist. Please enter a valid ACN.`, 'Invalid ACN');
        return;
      }

      this.flightStatusChckService.getFlightChecks(this.acn, "49941").subscribe(
        data => {
          this.aircraftInfoChecks = data;
          this.aircraftInfoChecksSubject.next(this.aircraftInfoChecks);
          if (this.emrStatusContent) {
            this.applyBackgroundColor();
          }
        },
        error => {
          this.aircraftInfoChecksSubject.next(null);
        }
      );
      this.mainService.refreshDetailsAcnTabData(true);
      this.maintenaceEventDetailsService.getMainDetailScreenData(parseInt(this.acn)).subscribe({
        next: (response: any) => {
          this.getMaintanenceShortageNoticeData(this.acn);
          const detailsTabsFromSessionStorage = this.mainService.getAcnTabDataFromSessionStorage();
          if (detailsTabsFromSessionStorage != null) {
            if (detailsTabsFromSessionStorage.length > 0) {
              const foundExistingTabData = detailsTabsFromSessionStorage.find((tab: any) => tab?.name === `ACN - ${this.acn}`);
              if (!foundExistingTabData) {
                if (this.acn != "" && this.acn != null) {
                  const isNewTabFound = detailsTabsFromSessionStorage.some((tab: any) => tab?.name === 'New Tab');
                  if (isNewTabFound) {
                    this.mainService.setAcnNameForTabInSessionStorage(this.acn, response[0].eventID.toString(), response[0].eventType);
                    this.maintenanceEventListService.setUserAddedEventListAcnDatainStorage({acn:this.acn, eventId: response[0].eventID.toString(), eventType: response[0].eventType });
                  } else {
                    this.maintenanceEventListService.setUserAddedEventListAcnDatainStorage({ acn: this.acn, eventId: response[0].eventID.toString(), eventType: response[0].eventType });
                    this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: true });
                    this.mainService.setSelectedTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: true });
                  }
                }
              } else {
                this.mainService.setSelectedTabDataToSessionStorage(foundExistingTabData);
              }
            } else {
              this.maintenanceEventListService.setUserAddedEventListAcnDatainStorage({ acn: this.acn, eventId: response[0].eventID.toString(), eventType: response[0].eventType });
              this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: false });
              this.mainService.setSelectedTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: false });
            }
          } else {
            this.maintenanceEventListService.setUserAddedEventListAcnDatainStorage({ acn: this.acn, eventId: response[0].eventID.toString(), eventType: response[0].eventType });
            this.mainService.addAcnTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: false });
            this.mainService.setSelectedTabDataToSessionStorage({ name: `ACN - ${this.acn}`, eventId: response[0].eventID.toString(), eventType: response[0].eventType, selected: true, userAdded: false });
          }
          this.detailViewData = (response[0]) as DetailViewResponseDao;
          this.initialMgrNotes = this.detailViewData.managerNote ? decodeURIComponent(this.detailViewData.managerNote) : '';
          this.mgrNotes = this.initialMgrNotes;
          this.eventId = String(this.detailViewData.eventID);
          this.mainService.refreshDetailsAcnTabData(true);
          this.getAffectedOutboundFlightList();
          this.getFlightEticInfo();
        },
        error: (error: any) => {
        }
      });
    });
  }

  private applyBackgroundColor(): void {
    if (!this.emrStatusContent || !this.emrStatusContent.nativeElement) {
      return;
    }
    if (!this.aircraftInfoChecks || !this.aircraftInfoChecks.statusCode) {
      this.renderer.removeStyle(this.emrStatusContent.nativeElement, 'background-color');
      return;
    }
    const color = this.colorMap.get(this.aircraftInfoChecks.statusCode) || '#ff0000';
    const textColor = this.textColorMap.get(color.toLowerCase()) || '#000000';
    this.renderer.setStyle(this.emrStatusContent.nativeElement, 'background-color', color);
    this.renderer.setStyle(this.emrStatusContent.nativeElement, 'color', textColor);
  }

  getAffectedOutboundFlightList(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.stationsList = [];
      this.upcomingFltList = [];
      this.inboundFltList = [];
      this.addEventService.getFlightLegDetails(parseInt(this.acn)).subscribe({
        next: (result) => {
          result['upcomingFlights'].forEach((i: any, index: any) => {
            if (index < 2) {
              this.stationsList.push(i['legDestination']);
              let flight: FlightLeg = {
                flightNumber: i['flightNumber'],
                flightDate: this.toDateFormat(i['flightDate'], false),
                legNumber: this.formatLegNbr(i['legNumber']),
                legOrigin: i['legOrigin'],
                legDestination: i['legDestination'],
                legDepartureTime: this.formatDateTimeToDDMMMYYYY_HHMM(i['legDepartureTime']).split(' ')[1],
                legArrivalTime: this.formatDateTimeToDDMMMYYYY_HHMM(i['legArrivalTime']).split(' ')[1],
              };
              this.upcomingFltList.push(flight);
            }
          });
          result['pastFlights'].forEach((i: any, index: any) => {
            if (index < 3) {
              this.stationsList.push(i['legDestination']);
              let flight: FlightLeg = {
                flightNumber: i['flightNumber'],
                flightDate: this.toDateFormat(i['flightDate'], false),
                legNumber: this.formatLegNbr(i['legNumber']),
                legOrigin: i['legOrigin'],
                legDestination: i['legDestination'],
                legDepartureTime: this.formatDateTimeToDDMMMYYYY_HHMM(i['legDepartureTime']).split(" ")[1],
                legArrivalTime: this.formatDateTimeToDDMMMYYYY_HHMM(i['legArrivalTime']).split(" ")[1]
              };
              this.inboundFltList.push(flight);
            }
            this.inboundFlight = this.inboundFltList
              .map(flight => ({
                ...flight,
                departureDateObj: this.parseFlightDateTime(flight.legDepartureTime)
              }))
              .sort((a, b) => a.departureDateObj.getTime() - b.departureDateObj.getTime())[2];
          });
          this.stationsList = [...new Set(this.stationsList)];
          this.station = this.stationsList.includes(result['currentFlight']['station']) ? result['currentFlight']['station'] : "";
          resolve();
        },
        error: (err) => {
          reject(err);
        }
      });
    });
  }

  getFlightEticInfo(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.maintenaceEventDetailsService.getEventFlightEticDetails(parseInt(this.eventId)).subscribe({
        next: (result) => {
          this.eventFlightEticDetails = result;
          this.eventFlightEticDetails.initialEtic = this.formatDate(this.eventFlightEticDetails.initialEtic);
          this.eventFlightEticDetails.arrival = this.formatDateTimeToDDMMMYYYY_HHMM(this.eventFlightEticDetails.arrival).split(" ")[1];
          this.eventFlightEticDetails.departure = this.formatDateTimeToDDMMMYYYY_HHMM(this.eventFlightEticDetails.departure).split(" ")[1];
          this.eventFlightEticDetails.scheduledDeparture = this.formatDate(this.eventFlightEticDetails.scheduledDeparture);
          this.eventFlightEticDetails.actualDeparture = this.formatDate(this.eventFlightEticDetails.actualDeparture);
          this.eventFlightEticDetails.flightDate = this.toDateFormat(this.eventFlightEticDetails.flightDate, false);
          this.cdRef.detectChanges();
          resolve();
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  }

  updateEventDetails(): void {
    this.isMgrNotesDisabled = true;
    const encodedMgrNotes = encodeURIComponent(this.mgrNotes);
    const detail_view_data: DetailViewResponseDao = {
      ...this.detailViewData,
      managerNote: encodedMgrNotes,
      contact: this.contactValue,
      eventStation: this.station
    };
    this.detailViewData.managerNote = this.mgrNotes;
    this.detailViewData = this.detailViewData;
    this.isUpdateButtonDisabled = true;
    this.maintenaceEventDetailsService.updateMainDetailScreenEventDetails(detail_view_data).subscribe({
      next: (response: any) => {
        this.detailViewData = (response.data.DETAIL_VIEW_OBJECT) as DetailViewResponseDao;
        this.initialMgrNotes = this.detailViewData.managerNote ? decodeURIComponent(this.detailViewData.managerNote) : '';
        this.mgrNotes = this.initialMgrNotes;
      },
      error: (error: any) => {
      }
    });
  }

  goToPage(page: number): void {
    this.currentPage = page;
  }

  onStationChange(): void {
    if (this.station !== this.initialStationValue) {
      this.isUpdateButtonDisabled = false;
    } else {
      this.isUpdateButtonDisabled = true;
    }
    this.station = this.station;
  }

  openPanel(panel: 'fs' | 'ers' | 'parts') {
    this.activePanel = this.activePanel === panel ? null : panel;
  }

  getMaintanenceShortageNoticeData(acn: string) {
    this.maintenaceEventDetailsService.getMsnTableData(parseInt(acn)).subscribe({
      next: (response: any) => {
        this.partsList = response;
      },
      error: (error: any) => {
      }
    });
  }

  onRowClick(row: any) {
    if (!this.selectedPart || this.selectedPart.msn !== row.msn) {
      this.selectedPart = row;
      this.openMsnDetailedInfo(row);
    } else {
      this.selectedPart = null;
    }
  }

  openMsnDetailedInfo(selectedPart: any): void {
    const dialogRef = this.dialog.open(MsnDetailsInfoComponent, {
      data: { msnTableData: this.partsList, selectedPart: selectedPart },
      disableClose: true,
      autoFocus: false,
      width: '90%',
      maxWidth: '90vw',
      height: '90%',
      panelClass: 'custom-msn-dialog'
    });

    dialogRef.afterClosed().subscribe(() => {
      this.selectedPart = null;
      this.cdRef.detectChanges();
    });
  }

  openEditor() {
    this.dialog.open(TfEditorComponent, {
      width: '65%',
      maxWidth: '100vw',
      data: {
        editorTitle: 'Add Tub File Note',
        userInfoDetail: this.userInfo,
        eventId: this.detailViewData.eventID,
        linkedDiscs: this.linkedDiscrepancies
      },
      panelClass: 'custom-tub-dialog'
    });
  }

  openEditDialog() {
    this.dialog.open(TfEditorComponent, {
      width: '65%',
      maxWidth: '100vw',
      panelClass: 'custom-tub-dialog',
      data: {
        tfNote: this.selectedNotes, editorTitle: 'Edit Tub File Note', 
        tfNoteDate: this.tfDateTime, 
        userInfoDetail: this.userInfo,
        eventId: this.detailViewData.eventID
      }
    });
  }

  openEmailFlow() {
    const dialogRef = this.dialog.open(TfEmailNoteComponent, {
      width: '400px',
      panelClass: 'custom-note-dialog'
    });

    dialogRef.afterClosed().subscribe((selection: 'all' | 'selected') => {
      if (selection) {
        if (selection == 'all') {
          this.dialog.open(TfEmailComponent, {
            width: '65%',
            maxWidth: '100vw',
            height: '85%',
            panelClass: 'custom-tub-email-dialog',
            data: { noteSelection: selection, mailId: this.userMail, tfNote: this.allNotes, acn: this.detailViewData.eventACN }
          });
        } else {
          this.dialog.open(TfEmailComponent, {
            width: '65%',
            maxWidth: '100vw',
            height: '85%',
            panelClass: 'custom-tub-email-dialog',
            data: { noteSelection: selection, mailId: this.userMail, tfNote: this.formatedSelectedNote, acn: this.detailViewData.eventACN }
          });
        }
      }
    });
  }

  updateDiscrepanciesButtons($event: any) {
    this.isDiscrepanciesViewEnabled = $event.rowSelected;
    this.isDiscrepanciesUpdateEnabled = $event.updateEnabled;
  }

  updateIntakeFormsButtons($event: any) {
    this.isIntakeFormUpdateEnabled = $event.enableUpdateButton;
  }

  updateDiscrepancies() {
    this.discrepanciesComponent.updateDiscrepancies();
  }

  viewDiscrepancies() {
    this.discrepanciesComponent.openDiscrepanciesDetailDialog();
  }

  updateIntakeForm() {
    this.intakeFormsComponent.onFormSubmit();
  }

  cancelIntakeFormsUpdate() {
    this.intakeFormsComponent.cancelFormUpdate();
  }

  onNoteSelected(note: any) {
    this.isNoteSelected = note ? true : false;
    this.selectedNotes = note ? note.notes.length > 1 ? note.notes[1].tfNote : note.notes[0].tfNote : '';
    this.formatSelectedTfNote(note);
    this.tfDateTime = note.eventTfNotesPk.tfDtTm;
  }

  formatSelectedTfNote(note: any) {
    if (!note || !note.notes || note.notes.length === 0) {
      this.formatedSelectedNote = '';
      return;
    }
    const notesText = note.notes.map((note: TubFileNotesResponseDto) => note.safeTfNote).join('<br>');
    const empName = note.empName || 'Unknown';
    const lastUpdate = new Date(note.lastUpdateDtTm).toLocaleString();
    const footerText = `Updated by: ${empName} | Last Updated: ${lastUpdate}`;
    this.formatedSelectedNote = `<br><b>TF Note:</b> ${notesText}<br><b>Updated by:</b> ${empName} | <b>Last Updated:</b> ${lastUpdate}<br>`;
  }

  onAllNotesReceived(allnote: any) {
    this.allNotes = allnote;
  }

  getVerticalHandlePosition(): string {
    return `calc(${this.verticalPosition}%  - 5px)`;
  }

  getSecondVerticalHandlePosition(): string {
    return `calc(${this.secondVerticalPosition}% - 5px)`;
  }

  getHorizontalHandlePosition(): string {
    return `calc(${this.horizontalPosition}% + 6px)`;
  }

  startResize(event: MouseEvent, direction: 'horizontal' | 'vertical' | 'vertical-second') {
    event.preventDefault();
    const container = direction === 'horizontal' ? this.secondQuadrant1.nativeElement.parentElement : 
                     (direction === 'vertical-second' ? this.secondQuadrant1.nativeElement.parentElement : this.quadrant1.nativeElement.parentElement);
    const containerRect = container.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    this.isResizing = true;
    this.resizeDirection = direction;
    this.startX = event.clientX;
    this.startY = event.clientY;

    const onMouseMove = (moveEvent: MouseEvent) => {
      if (!this.isResizing) return;

      if (direction === 'vertical' || direction === 'vertical-second') {
        const newX = moveEvent.clientX - containerRect.left;
        const newPosition = (newX / containerWidth) * 100;
        const boundedPosition = Math.max(20, Math.min(80, newPosition));
        if (direction === 'vertical') {
          this.verticalPosition = boundedPosition;
          this.gridTemplateColumns = `${boundedPosition}% ${100 - boundedPosition}%`;
        } else if (direction === 'vertical-second') {
          this.secondVerticalPosition = boundedPosition;
          this.secondScreenGridColumns = `${boundedPosition}% ${100 - boundedPosition}%`;
        }
      } else if (direction === 'horizontal') {
        const newY = moveEvent.clientY - containerRect.top;
        const newPosition = (newY / containerHeight) * 100;
        const boundedPosition = Math.max(20, Math.min(80, newPosition));
        this.horizontalPosition = boundedPosition;
        this.gridTemplateRows = `${boundedPosition}% ${100 - boundedPosition}%`;
      }
      this.cdRef.detectChanges();
    };

    const onMouseUp = () => {
      this.isResizing = false;
      this.resizeDirection = null;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  openManagerNotesCapture() {
    const dialogRef = this.dialog.open(MgrCaptureDialogComponent, {
      minWidth: '80%',
      height: '80%'
    });

    dialogRef.afterClosed().subscribe((result) => {
      const discrepancyTexts = result.selectedDiscrepancyTxts;
      const tfNotes = result.selectedTfNotes;
      if (discrepancyTexts || tfNotes) {
        this.isMgrNotesDisabled = false;
        this.setUpdatedTxtToManagerNotes(discrepancyTexts, tfNotes);
      }
    });
  }

  setUpdatedTxtToManagerNotes(result: DiscrepancySelectedTexts[], tfNotes: TubFileNotesResponseDto[]) {
    this.selectedDiscrepancyTxts = result;
    this.selectedTfNotes = tfNotes;
    this.initialMgrNotes = this.mgrNotes;
    this.selectedDiscrepancyTxts?.forEach((item: DiscrepancySelectedTexts) => {
      if (item.selectedFormattedTexts.length > 0) {
        item.selectedFormattedTexts.forEach((text: string) => {
          this.mgrNotes += `\n ata/DiscNum: ${item.discrepancy.ata}/${item.discrepancy.number} \n ${text}\n \n`;
        });
      }
    });
    if (this.selectedTfNotes.length > 0) {
      this.mgrNotes += `\n Tub File Notes: \n`;
      this.selectedTfNotes.forEach((tfNote: TubFileNotesResponseDto) => {
        if (tfNote.tfNote && tfNote.tfNote.length > 0) {
          const formattedDate = new Date(tfNote.lastUpdateDtTm).toLocaleDateString();
          this.mgrNotes += `${tfNote.tfNote} - ${formattedDate} \n`;
        }
      });
    }
  }

  addManagerNote(fireSwal: boolean) {
    this.isMgrNotesDisabled = false;
    if (fireSwal && this.parsemNagerNotes()) {
      Swal.fire({
        title: 'Manager Notes',
        text: 'Would you like to automatically include Discrepancy data?',
        icon: 'info',
        showCancelButton: true,
        confirmButtonColor: '#6c49b9',
        cancelButtonColor: '#ff6600',
        confirmButtonText: 'Submit'
      }).then((result: any) => {
        if (result.isConfirmed) {
          this.maintenanceEventDetailsSharedService.linkedDiscrepancies$.subscribe((data) => {
            this.linkedDiscrepancies = data;
            const managerCompare = JSON.parse(JSON.stringify(this.mgrNotes));
            for (const dscrp of this.linkedDiscrepancies) {
              if (!managerCompare.toLowerCase().includes(dscrp.text.join(', ').toLowerCase())) {
                if (dscrp.discType) {
                  this.mgrNotes += `\n${dscrp.discType} -  `;
                }
                if (dscrp.text && dscrp.text.length > 0) {
                  this.mgrNotes += `${dscrp.text.join(', ')}\n`;
                }
                if (dscrp.discType.toUpperCase() === 'SMIS' && dscrp.timeRemaining) {
                  this.mgrNotes += `T/REM: ${dscrp.timeRemaining} \n`;
                }
              }
            }
          });
        }
      });
    }
  }

  parsemNagerNotes(): Boolean {
    if (this.linkedDiscrepancies.length > 0) {
      const managerCompare = JSON.parse(JSON.stringify(this.mgrNotes));
      for (const dscrp of this.linkedDiscrepancies) {
        if (dscrp.text && dscrp.text.length > 0) {
          const textToCheck = dscrp.text.join(', ');
          if (managerCompare.toLowerCase().includes(textToCheck.toLowerCase())) {
            return false;
          }
        }
      }
    }
    return true;
  }

  disableManagerNote() {
    this.mgrNotes = this.initialMgrNotes;
    this.isMgrNotesDisabled = true;
  }

  toDateFormat(dateString: any, yearneeded: boolean) {
    const date = new Date(dateString);
    const day = date.getUTCDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' }).toUpperCase();
    const year = date.getUTCFullYear();
    if (yearneeded == true) {
      return `${day}${month}${year}`;
    }
    return `${day}${month}`;
  }

  formatLegNbr(val: any) {
    if (val?.toString().length == 1) {
      return '0' + val;
    }
    return val;
  }

  formatDateTimeToDDMMMYYYY_HHMM(dateString: string) {
    const date = new Date(dateString);
    const day = date.getUTCDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' }).toUpperCase();
    const year = date.getUTCFullYear();
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `${day}${month} ${hours}${minutes}`;
  }

  parseFlightDateTime(dateStr: string): Date {
    const currentYear = new Date().getFullYear();
    const formatted = dateStr.replace(/(\d{2})([A-Z]{3})/, '$1 $2') + ` ${currentYear}`;
    return new Date(formatted);
  }

  formatDate(dateString: string): string {
    if (dateString == null || dateString == "null") {
      return '';
    }
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const dateObj = new Date(dateString);
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = months[dateObj.getMonth()];
    const year = dateObj.getFullYear().toString().slice(-2);
    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');
    return `${day}${month}${year} ${hours}:${minutes}`;
  }

  // onMgrNotesChange(): void {
  //   if (this.mgrNotes !== this.initialMgrNotes) {
  //     this.isUpdateButtonDisabled = false; // Enable button if values are different
  //   } else {
  //     this.isUpdateButtonDisabled = true; // Disable button if values are the same
  //   }
  //   this.mgrNotes = this.mgrNotes;
  // }
}
