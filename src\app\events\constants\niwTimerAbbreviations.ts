export class NIWTimerAbbreviations {
    static readonly Airport_Issues = 'APT';
    static readonly Avionics = 'AVI';
    static readonly Composite = 'COMP';
    static readonly Engine_Run = 'ERUN';
    static readonly Engr_EA = 'ENGR';
    static readonly Facility = 'FACL';
    static readonly Fueling = 'FUEL';
    static readonly GSE = 'GSE';
    static readonly Hangar_10 = 'HGR10';
    static readonly Hangar_Space = 'HGR';
    static readonly HMX_Induction = 'HMXIND';
    static readonly HUB_Support = 'HUB';
    static readonly IT_Computer = 'IT';
    static readonly Manpower = 'MP';
    static readonly Other = 'OTHR';
    static readonly Parts_AOG = 'PTS';
    static readonly QA = 'QA';
    static readonly Sheet_Metal = 'SMS';
    static readonly Tooling = 'TLS';
    static readonly Vendor = 'VNDR';
    static readonly Weather = 'WX';

    static getAbbreviation(timerName: string): string {
        const abbreviationMap: { [key: string]: string } = {
            'Airport_Issues': NIWTimerAbbreviations.Airport_Issues,
            'Avionics': NIWTimerAbbreviations.Avionics,
            'Component': NIWTimerAbbreviations.Composite,
            'Engine_Run': NIWTimerAbbreviations.Engine_Run,
            'Engr_EA': NIWTimerAbbreviations.Engr_EA,
            'Facility': NIWTimerAbbreviations.Facility,
            'Fueling': NIWTimerAbbreviations.Fueling,
            'GSE': NIWTimerAbbreviations.GSE,
            'Hangar_10': NIWTimerAbbreviations.Hangar_10,
            'Hangar_Space': NIWTimerAbbreviations.Hangar_Space,
            'HMX_Induction': NIWTimerAbbreviations.HMX_Induction,
            'HUB_Support': NIWTimerAbbreviations.HUB_Support,
            'IT_Computer': NIWTimerAbbreviations.IT_Computer,
            'Manpower': NIWTimerAbbreviations.Manpower,
            'Other': NIWTimerAbbreviations.Other,
            'Parts_AOG': NIWTimerAbbreviations.Parts_AOG,
            'QA': NIWTimerAbbreviations.QA,
            'Sheet_Metal': NIWTimerAbbreviations.Sheet_Metal,
            'Tooling': NIWTimerAbbreviations.Tooling,
            'Vendor': NIWTimerAbbreviations.Vendor,
            'Weather': NIWTimerAbbreviations.Weather
        };

        return abbreviationMap[timerName] || timerName;
    }

    static getAllTimerNames(): string[] {
        return [
            'Airport_Issues',
            'Avionics',
            'Composite',
            'Engine_Run',
            'Engr_EA',
            'Facility',
            'Fueling',
            'GSE',
            'Hangar_10',
            'Hangar_Space',
            'HMX_Induction',
            'HUB_Support',
            'IT_Computer',
            'Manpower',
            'Other',
            'Parts_AOG',
            'QA',
            'Sheet_Metal',
            'Tooling',
            'Vendor',
            'Weather'
        ];
    }

    static statusToTimersMap: { [key: string]: string[] } = {
      'DWN': ['Airport_Issues', 'Avionics', 'Composite', 'Engine_Run', 'Engr_EA', 'Facility', 'Fueling', 'GSE', 'Hangar_10', 'Hangar_Space', 'HUB_Support', 'IT_Computer', 'Manpower', 'Other', 'Parts_AOG', 'QA', 'Sheet_Metal', 'Tooling', 'Vendor', 'Weather'],
      'HMX': ['Airport_Issues', 'Avionics', 'Composite', 'Engine_Run', 'Engr_EA', 'Facility', 'Fueling', 'GSE', 'Hangar_Space', 'HMX_Induction', 'HUB_Support', 'IT_Computer', 'Manpower', 'Other', 'Parts_AOG', 'QA', 'Sheet_Metal', 'Tooling', 'Vendor', 'Weather'],
      'HMD': ['Airport_Issues', 'Avionics', 'Composite', 'Engine_Run', 'Engr_EA', 'Facility', 'Fueling', 'GSE', 'Hangar_10', 'Hangar_Space', 'HUB_Support', 'IT_Computer', 'Manpower', 'Other', 'Parts_AOG', 'QA', 'Sheet_Metal', 'Tooling', 'Vendor', 'Weather'],
      'HMO': ['Airport_Issues', 'Avionics', 'Composite', 'Engine_Run', 'Engr_EA', 'Facility', 'Fueling', 'GSE', 'Hangar_Space', 'HMX_Induction', 'HUB_Support', 'IT_Computer', 'Manpower', 'Other', 'Parts_AOG', 'QA', 'Sheet_Metal', 'Tooling', 'Vendor', 'Weather'],
      'AOG': ['Parts_AOG']
    };
}