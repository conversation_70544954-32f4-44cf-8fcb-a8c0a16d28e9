import { Component } from '@angular/core';
import { questions } from '../../../dto/questionsDto';
import { UserIntakeForm } from '../../../dto/UserIntakeFormDto';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AdministrativeService } from '../../../services/administrative.service';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { CreateQuestionDialogComponent } from '../create-question-dialog/create-question-dialog.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-create-intake-form',
  standalone: false,
  templateUrl: './create-intake-form.component.html',
  styleUrl: './create-intake-form.component.scss'
})
export class CreateIntakeFormComponent {

 userIntakeForm: UserIntakeForm = new UserIntakeForm();
  questions: Array<questions> = [];
  todo: Array<questions> = [];
  title = '';
  done: Array<questions> = [];
  filteredQuestions: Array<questions> = [];
  searchTerm: string = '';
  panels: Array<{ title: string, content: Array<questions> }> = [];
  doneMap: Map<number, number> = new Map();

  constructor(
    private route: ActivatedRoute,
    public dialog: MatDialog,
    public router: Router,
    public administrativeService: AdministrativeService
  ) {}

  ngOnInit(): void {
    this.route.queryParamMap.subscribe(params => {
      console.log('Query Params:', params.get('intakeFormDetails'));
      console.log('Query questions Params:', params.get('allQuestions'));
      const intakeFormDetails = params.get('intakeFormDetails');
      const allQuestions = params.get('allQuestions');
      if (intakeFormDetails) {
        this.userIntakeForm = JSON.parse(intakeFormDetails);
        this.title = this.userIntakeForm.intakeFormNm;
        console.log('intakeFormDetails are: ', this.userIntakeForm.roleId);
      } else {
        console.error('intakeFormDetails is null');
      }
      if (allQuestions) {
        this.questions = JSON.parse(allQuestions);
        this.done = [...this.questions];
        this.filteredQuestions = [...this.questions];
        console.log('allQuestions are: ', this.questions);

        this.panels = [
          { title: 'Panel 1', content: this.questions },
          { title: 'Panel 2', content: this.questions }
        ];
      } else {
        console.error('allQuestions is null');
      }
    });
  }

  filterQuestions(): void {
    try {
      // If search term is empty, show all questions
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.filteredQuestions = [...this.done];
        return;
      }

      // Convert search term to lowercase for case-insensitive comparison
      const searchTermLower = this.searchTerm.toLowerCase().trim();

      // Filter questions where question text includes the search term
      this.filteredQuestions = this.done.filter(question => {
        // Check if question and questionTxt exist
        if (!question || !question.questionTxt) return false;
        
        // Return true if question text includes search term
        return question.questionTxt.toLowerCase().includes(searchTermLower);
      });

    } catch (error) {
      console.error('Error filtering questions:', error);
      // Fallback to showing all questions if error occurs
      this.filteredQuestions = [...this.done];
    }
  }

  drop(event: CdkDragDrop<questions[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
      // After moving items, update the filtered questions if needed
      if (event.container === event.previousContainer) {
        this.filterQuestions();
      }
    }
  }

  addNewQuestion() {
    const dialogRef = this.dialog.open(CreateQuestionDialogComponent, {
      minWidth: '40%',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      panelClass: 'responsive-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.todo.push(result.question);
      }
    });
  }

  OnSubmit() {
    if (this.todo.length) {
      this.userIntakeForm.intakeForm.createdBy = 'Admin';
      this.userIntakeForm.intakeForm.questions.push(...this.todo);
      this.administrativeService.saveUserIntakeForm(this.userIntakeForm).subscribe({
        next: (response) => {
          if (response === true) {
        Swal.fire('Success', 'User Intake Form created successfully', 'success');
        this.router.navigate(['/mets-admininstrative']);
          } else {
        Swal.fire('Error', 'Failed to create User Intake Form', 'error');
         this.router.navigate(['/mets-admininstrative']);
          }
        },
        error: () => {
          Swal.fire('Error', 'Failed to create User Intake Form', 'error');
           this.router.navigate(['/mets-admininstrative']);
        }
      });
    }
  }

  OnCancel() {
    this.router.navigate(['/mets-admininstrative']);
  }

  editQuestion(question: questions) {
    console.log('Opening User Intake Form Dialog' + question);
  }

  deleteQuestion(question: questions) {
    this.todo = this.todo.filter(q => q !== question);
    this.questions = this.questions.filter(q => q !== question);
    this.done.push(question);
    this.filterQuestions(); // Update filtered list after deletion
  }

  getAnswersText(item: questions): string {
    return item.answers.map(a => a.answerTxt).join(', ') || 'N/A';
  }

}