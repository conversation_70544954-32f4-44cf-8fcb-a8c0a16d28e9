import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { questions } from '../../../dto/questionsDto';
import { MatDialog } from '@angular/material/dialog';
import { AdministrativeService } from '../../../services/administrative.service';
import { CreateQuestionDialogComponent } from '../create-question-dialog/create-question-dialog.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-questions-list',
  standalone: false,
  templateUrl: './questions-list.component.html',
  styleUrl: './questions-list.component.scss'
})
export class QuestionsListComponent {

   questions: Array<questions> = [];

  constructor(private route: ActivatedRoute,
     public dialog: MatDialog,
   public administrativeService: AdministrativeService,
   private router: Router
  ) {

  }
  ngOnInit(): void {
    this.route.queryParamMap.subscribe(params => {
      const allQuestions = params.get('allQuestions');
      if (allQuestions) {
        this.questions = JSON.parse(allQuestions);
        console.log('allQuestions are: ', this.questions);

      } else {
        console.error('allQuestions is null');
      }
    }
    );
  }

editQuestion(question: questions) {
    console.log('Opening User Intake Form Dialog');
    const dialogRef = this.dialog.open(CreateQuestionDialogComponent, {
      width: '80%',
      data: { editedQuestion: question, isEdit: true }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const questionIndex = this.questions.findIndex(q => q.questionId === question.questionId);
        this.questions[questionIndex] = result.question;
        console.log('The dialog was closed***', result.question);
      }
    });
  }

  deleteQuestion(question: questions) {
    Swal.fire({
          title: 'Are you sure?',
          text: 'You won\'t be able to revert this!',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
          if (result.isConfirmed) {
            if (this.questions) {
              this.administrativeService.deleteQuestion(question.questionId).subscribe({
                next: () => {
                  Swal.fire('Deleted!', 'User Intake Form deleted successfully.', 'success');
                  this.router.navigate(['/mets-admininstrative']);
                },
                error: (error) => {
                  console.error('Error deleting user intake form:', error);
                  Swal.fire('Error!', 'Failed to delete User Intake Form.', 'error');
                }
              });
    
            }
          }
        });
  }

}
