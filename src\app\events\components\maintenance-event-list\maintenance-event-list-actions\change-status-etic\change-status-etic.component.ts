import { Compo<PERSON>, Inject, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MaintenanceEventListStatusDto } from '../../../../dto/maintenance-event-ListStatusDto';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { NgForm } from '@angular/forms';
import { DateAdapter } from '@angular/material/core';
import { TubFileNotesResponseDto } from '../../../../dto/TubFileNotesResponseDto';
import { NiwTimerResponse, TimerData } from '../../../../dao/niwTimerDao';
import { ChangeStatusRequestDTO } from '../../../../dto/changeStatusRequestDto';
import { UserDto } from '../../../../dto/UserInfo';
import { MaintenanceEventListService } from '../../../../services/maintenance-event-list.service';
import { DiscrepanciesList, OpenDiscrepanciesResponseDao } from '../../../../dao/discrepancies-listDao';
import { NIWTimerAbbreviations } from '../../../../constants/niwTimerAbbreviations';

// Custom date format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

interface ReasonNode {
  name: string;
  children?: ReasonNode[];
}

const REASON_DATA: ReasonNode[] = [
  {
    name: 'Engineering',
    children: [
      { name: 'Eng - Allowable Limits' },
      { name: 'Eng - OEM' },
      { name: 'Eng - Tech Documents/Effectivity' },
      { name: 'Eng - Troubleshooting' }
    ]
  },
  {
    name: 'Fueling',
    children: []
  },
  {
    name: 'GSE',
    children: []
  },
  {
    name: 'Manpower',
    children: [
      { name: 'Mp - Machine Shop' },
      { name: 'Mp - Avionics' },
      { name: 'Mp - Composite' },
      { name: 'Mp - Sheet Metal' },
      { name: 'Mp - Welder' },
      { name: 'Mp - Vendor' }
    ]
  },
  {
    name: 'Others',
    children: []
  },
  {
    name: 'Called UP Early',
    children: [
      { name: 'UP Early - Overestimated Job' },
      { name: 'UP Early - GOC Needs Aircraft' }
    ]
  },
  {
    name: 'Parts In Hand - Setting ETIC',
    children: []
  },
  {
    name: 'QA/QC',
    children: []
  },
  {
    name: 'Tooling',
    children: [
      { name: 'Tooling - Tooling in Hand' }
    ]
  },
  {
    name: 'Troubleshooting',
    children: []
  },
  {
    name: 'TSI',
    children: [
      { name: 'TSI - Line Maintenance request' },
      { name: 'TSI - (Updated MOCC' }
    ]
  },
  {
    name: 'MOC',
    children: [
      { name: 'MOC - WRIs Added' },
      { name: 'MOC - Aircraft Held Down' }
    ]
  },
  {
    name: 'Underestimated Job',
    children: []
  },
  {
    name: 'Weather',
    children: []
  },
  {
    name: 'Aircraft Damage',
    children: []
  },
  {
    name: 'Aircraft Movement',
    children: []
  }
];

const HMO_HMX_REASON_DATA: ReasonNode[] = [
  {
    name: 'A/C Condition',
    children: []
  },
  {
    name: 'A/C Damage',
    children: []
  },
  {
    name: 'Cannibalized Material',
    children: []
  },
  {
    name: 'Engineering',
    children: []
  },
  {
    name: 'Ground Check Findings',
    children: []
  },
  {
    name: 'Material',
    children: []
  },
  {
    name: 'Mod Prototype',
    children: []
  },
  {
    name: 'OEM/Other External Org',
    children: []
  },
  {
    name: 'Planning',
    children: []
  },
  {
    name: 'Prior to Induction',
    children: []
  },
  {
    name: 'Production',
    children: []
  },
  {
    name: 'Propulsion',
    children: []
  },
  {
    name: 'QA/QC',
    children: []
  },
  {
    name: 'Test Flight',
    children: []
  },
  {
    name: 'Tooling',
    children: []
  },
  {
    name: 'Training',
    children: []
  },
  {
    name: 'Weather',
    children: []
  }
];

@Component({
  selector: 'app-change-status-etic',
  templateUrl: './change-status-etic.component.html',
  styleUrl: './change-status-etic.component.scss',
  standalone: false
})
export class ChangeStatusETICComponent implements OnInit, OnDestroy {

  @ViewChild('statusForm') statusForm: NgForm | undefined;
  @ViewChild('tubFileForm') tubFileForm: NgForm | undefined;
  @ViewChild('niwTimerForm') niwTimerForm: NgForm | undefined;

  options = ['AOG', 'DOA', 'DWN', 'HMD', 'HMO', 'HMX', 'NAS', 'NOS', 'TRK'];
  newStatus: string = '';
  Info: string = '';
  selectedReason: string = '';
  comment: string = '';
  currentComment: string = '';
  newComment: string = '';
  addTubFileNote: boolean = true;
  previousETICinError: boolean = false;
  newOST: boolean = false;
  tubFileNote: string = '';
  existingTubFileNotes: string = '';
  showSecondScreen: boolean = false;
  showThirdScreen: boolean = false;
  currentDate: Date = new Date();
  timeHour: string = '';
  timeMinute: string = '';
  timeValue: string = ''; // New merged time field
  diableAddTubFileNotecheckbox: boolean = false;
  eticErrors: string[] = [];

  // NIW Timers properties
  niwStartDate: Date = new Date();
  niwTimeHour: string = '';
  niwTimeMinute: string = '';
  niwTimerResponse!: NiwTimerResponse;
  niwTimerOptions: string[] = [];
  selectedNiwTimer: string = ''; // Default to empty
  niwTableColumns: string[] = ['timerName'];
  niwHours: string[] = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  niwMinutes: string[] = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  minDate: Date = new Date();
  hours: string[] = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  minutes: string[] = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  filteredHours: string[] = [];
  filteredMinutes: string[] = [];

  sortOrder: 'asc' | 'desc' = 'desc'; // Default to newest first
  tubfilenotesresponse!: TubFileNotesResponseDto[];
  groupedNotes: any[] = [];
  linkedDiscrepancies: string[] = [];
  selectedDisc: any = "";

  reasonData: ReasonNode[] = REASON_DATA;
  selectedRow: any;
  alreadyExistsChangeRequest: boolean = false;
  private timeInterval: any;

  // New ETIC fields
  eticTypeList: string[] = ['FIRM', 'WA', 'Not In Work (NIW)'];
  filteredEticTypeList: string[] = ['FIRM', 'WA', 'Not In Work (NIW)'];
  selectedEticType: string = '';
  niwReasonList: string[] = [];
  filteredNiwReasonList: string[] = [];
  selectedNiwReason: string = '';
  timeNeeded: number = 0;
  timeUnitList: string[] = ['Hours', 'Days'];
  selectedTimeUnit: string = 'Hours';

  changeStatusEticObject!: any;

  changeStatusRequestDTO!: ChangeStatusRequestDTO;

  constructor(
    public dialogRef: MatDialogRef<ChangeStatusETICComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { selectedRow: any, alreadyExistsChangeRequest: boolean, existingTubfilenotes: TubFileNotesResponseDto[] },
    private dialog: MatDialog,
    private maintenanceEventListService: MaintenanceEventListService,
    private maintenanceEventDetailsService: MaintenanceEventDetailsService,
    private dateAdapter: DateAdapter<Date>
  ) {
    this.selectedRow = data.selectedRow || null;
    this.alreadyExistsChangeRequest = data.alreadyExistsChangeRequest || false;
    this.tubfilenotesresponse = data.existingTubfilenotes || [];
    this.dateAdapter.setLocale('en-US');
  }

  ngOnInit(): void {
    this.initializeForm();
    this.existingTubFileNotes = '';
    this.startTimeValidation();
    this.updateReasonData();
    this.validateTubFileCheckbox();
    this.getTubFileNotes();
    this.getAogNiwTimers();
    this.getLinkedDiscrepanciedList();

    const utcNow = new Date();
    this.minDate = new Date(Date.UTC(utcNow.getUTCFullYear(), utcNow.getUTCMonth(), utcNow.getUTCDate()));
    this.initializeNiwTimerFields();
    this.updateEticErrors();

    // Initialize NIW reason list
    this.niwReasonList = NIWTimerAbbreviations.getAllTimerNames();
    this.filteredNiwReasonList = [...this.niwReasonList];

    // Apply initial filtering based on current status
    if (this.newStatus) {
      this.filterEticTypeList(this.newStatus);
      this.filterNiwReasonList(this.newStatus);
    }
  }

  ngOnDestroy(): void {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  }

  initializeForm(): void {
    this.currentComment = this.selectedRow?.curComment || '';
    this.newComment = this.selectedRow?.newEticComment || '';
    this.newStatus = this.selectedRow?.newStatus ? this.selectedRow.newStatus : '';
    this.Info = this.selectedRow?.newEticText || '';
    this.newOST = this.selectedRow?.newOST != null ? this.selectedRow?.newOST === 'Y' : false;
    this.selectedReason = this.selectedRow?.newEticReasonCd || '';
    this.comment = this.selectedRow?.newEticRsnComments || '';

    if (this.alreadyExistsChangeRequest) {
      this.initializeNewEticFields();
    } else {
      const eticDateTime = this.parseEticDateTime(this.selectedRow.eticDateTime);
      this.currentDate = new Date(Date.UTC(eticDateTime.date.getFullYear(), eticDateTime.date.getMonth(), eticDateTime.date.getDate()));
      this.timeHour = eticDateTime.hour;
      this.timeMinute = eticDateTime.minute;
      this.syncTimeValue(); // Sync the merged time field
    }

    this.minDate = new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate()));
    this.updateTimeOptions();
  }

  initializeNewEticFields(): void {
    if (this.selectedRow?.newEticDateTime && this.isValidEticFormat(this.selectedRow.newEticDateTime)) {
      const eticDateTime = this.parseEticDateTime(this.selectedRow.newEticDateTime);
      this.currentDate = new Date(Date.UTC(eticDateTime.date.getFullYear(), eticDateTime.date.getMonth(), eticDateTime.date.getDate()));
      this.timeHour = eticDateTime.hour;
      this.timeMinute = eticDateTime.minute;
      this.syncTimeValue(); // Sync the merged time field
    } else {
      const eticDateTime = this.parseEticDateTime(this.selectedRow.eticDateTime);
      this.currentDate = new Date(Date.UTC(eticDateTime.date.getFullYear(), eticDateTime.date.getMonth(), eticDateTime.date.getDate()));
      this.timeHour = eticDateTime.hour;
      this.timeMinute = eticDateTime.minute;
      this.syncTimeValue(); // Sync the merged time field
    }
  }

  initializeNiwTimerFields(): void {
    const now = new Date();
    this.niwStartDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
    this.niwTimeHour = now.getUTCHours().toString().padStart(2, '0');
    this.niwTimeMinute = now.getUTCMinutes().toString().padStart(2, '0');
    this.selectedNiwTimer = this.niwTimerOptions[0] || '';
  }

  isValidEticFormat(eticDateTime: string): boolean {
    const regex = /^\d{4}\/\d{6}$/;
    return regex.test(eticDateTime);
  }

  parseEticDateTime(eticDateTime: string): { date: Date, hour: string, minute: string } {
    try {
      const [time, date] = eticDateTime.split('/');
      const hour = time.substring(0, 2);
      const minute = time.substring(2, 4);
      const month = parseInt(date.substring(0, 2), 10) - 1;
      const day = parseInt(date.substring(2, 4), 10);
      const year = parseInt(date.substring(4, 6), 10) + 2000;

      const parsedDate = new Date(Date.UTC(year, month, day));
      if (isNaN(parsedDate.getTime())) {
        throw new Error('Invalid date');
      }

      return { date: parsedDate, hour, minute };
    } catch (error) {
      console.warn(`Failed to parse newEticDateTime: ${eticDateTime}. Falling back to current UTC date.`, error);
      const now = new Date();
      return {
        date: new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate())),
        hour: now.getUTCHours().toString().padStart(2, '0'),
        minute: now.getUTCMinutes().toString().padStart(2, '0')
      };
    }
  }

  startTimeValidation(): void {
    const now = new Date();
    const msUntilNextUtcMinute = 60000 - (now.getUTCSeconds() * 1000 + now.getUTCMilliseconds());

    setTimeout(() => {
      this.updateCurrentTime();
      this.timeInterval = setInterval(() => {
        this.updateCurrentTime();
      }, 60000);
    }, msUntilNextUtcMinute);
  }

  updateCurrentTime(): void {
    if (this.showSecondScreen || this.showThirdScreen) {
      return; // Do not update time fields on second or third screen
    }

    const now = new Date();
    const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));

    // Update ETIC time
    const selectedDate = new Date(this.currentDate);
    const selectedUTC = new Date(Date.UTC(
      selectedDate.getUTCFullYear(),
      selectedDate.getUTCMonth(),
      selectedDate.getUTCDate()
    ));

    if (this.isSameDayUTC(selectedUTC, nowUTC)) {
      this.updateTimeOptions();
    }

    // Update NIW Timer time孩

    // Update NIW Timer time
    const niwSelectedDate = new Date(this.niwStartDate);
    const niwSelectedUTC = new Date(Date.UTC(
      niwSelectedDate.getUTCFullYear(),
      niwSelectedDate.getUTCMonth(),
      niwSelectedDate.getUTCDate()
    ));

    if (this.isSameDayUTC(niwSelectedUTC, nowUTC)) {
      this.niwTimeHour = now.getUTCHours().toString().padStart(2, '0');
      this.niwTimeMinute = now.getUTCMinutes().toString().padStart(2, '0');
    }

    this.updateEticErrors();
  }

  isSameDayUTC(date1: Date, date2: Date): boolean {
    return (
      date1.getUTCFullYear() === date2.getUTCFullYear() &&
      date1.getUTCMonth() === date2.getUTCMonth() &&
      date1.getUTCDate() === date2.getUTCDate()
    );
  }

  onDateChange(event: any): void {
    const selectedDate = new Date(event.value);
    this.currentDate = new Date(Date.UTC(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      selectedDate.getDate()
    ));
    const nowUTC = new Date();
    const todayUTC = new Date(Date.UTC(nowUTC.getUTCFullYear(), nowUTC.getUTCMonth(), nowUTC.getUTCDate()));

    if (this.isSameDayUTC(this.currentDate, todayUTC)) {
      const nowHours = nowUTC.getUTCHours();
      const nowMinutes = nowUTC.getUTCMinutes();
      this.timeHour = nowHours.toString().padStart(2, '0');
      this.timeMinute = nowMinutes.toString().padStart(2, '0');
      this.syncTimeValue(); // Sync the merged time field
    }

    this.updateTimeOptions();
    this.updateEticErrors();
  }

  onNiwDateChange(event: any): void {
    const selectedDate = new Date(event.value);
    this.niwStartDate = new Date(Date.UTC(
      selectedDate.getUTCFullYear(),
      selectedDate.getUTCMonth(),
      selectedDate.getUTCDate()
    ));
    this.updateNiwTimeOptions();
  }

  updateTimeOptions(): void {
    const nowUTC = new Date();
    const selected = new Date(this.currentDate);
    const selectedUTC = new Date(Date.UTC(selected.getUTCFullYear(), selected.getUTCMonth(), selected.getUTCDate()));
    const isToday = this.isSameDayUTC(selectedUTC, nowUTC);
    const currentUTC_Hour = nowUTC.getUTCHours();
    const currentUTC_Minute = nowUTC.getUTCMinutes();

    this.filteredHours = isToday
      ? this.hours.filter(h => parseInt(h) >= currentUTC_Hour)
      : [...this.hours];

    if (this.timeHour && isToday) {
      const selectedHour = parseInt(this.timeHour);
      if (selectedHour === currentUTC_Hour) {
        this.filteredMinutes = this.minutes.filter(m => parseInt(m) > currentUTC_Minute);
      } else {
        this.filteredMinutes = [...this.minutes];
      }

      // Only clear time if selected time is in the past
      if (selectedHour < currentUTC_Hour || (selectedHour === currentUTC_Hour && parseInt(this.timeMinute) <= currentUTC_Minute)) {
        this.timeHour = this.filteredHours[0] || '';
        this.timeMinute = this.filteredMinutes[0] || '';
        this.syncTimeValue(); // Sync the merged time field
      }
    } else {
      this.filteredMinutes = isToday
        ? this.minutes.filter(m => parseInt(m) > currentUTC_Minute)
        : [...this.minutes];
      // Only reset if time is invalid and on current date
      if (isToday && this.timeHour && parseInt(this.timeHour) < currentUTC_Hour) {
        this.timeHour = this.filteredHours[0] || '';
        this.timeMinute = this.filteredMinutes[0] || '';
        this.syncTimeValue(); // Sync the merged time field
      } else if (!this.timeHour) {
        this.timeHour = this.filteredHours[0] || '';
        this.timeMinute = this.filteredMinutes[0] || '';
        this.syncTimeValue(); // Sync the merged time field
      }
    }

    this.updateEticErrors();
  }

  updateNiwTimeOptions(): void {
    const nowUTC = new Date();
    this.niwHours = [...this.hours];
    this.niwMinutes = [...this.minutes];

    const selected = new Date(this.niwStartDate);
    const selectedUTC = new Date(Date.UTC(selected.getUTCFullYear(), selected.getUTCMonth(), selected.getUTCDate()));
    const isToday = this.isSameDayUTC(selectedUTC, nowUTC);

    if (isToday) {
      this.niwTimeHour = nowUTC.getUTCHours().toString().padStart(2, '0');
      this.niwTimeMinute = nowUTC.getUTCMinutes().toString().padStart(2, '0');
    }
  }

  // Method to handle time input change from merged time field
  onTimeInputChange(): void {
    if (this.timeValue) {
      const [hour, minute] = this.timeValue.split(':');
      this.timeHour = hour;
      this.timeMinute = minute;
      this.updateEticErrors();
    }
  }

  // Method to sync the merged time field with hour and minute values
  syncTimeValue(): void {
    if (this.timeHour && this.timeMinute) {
      this.timeValue = `${this.timeHour.padStart(2, '0')}:${this.timeMinute.padStart(2, '0')}`;
    }
  }

  onStatusChange(): void {
    this.updateReasonData();
    if (this.alreadyExistsChangeRequest && this.selectedReason === 'ETIC Improved') {
      this.comment = '';
    } else {
      this.selectedReason = '';
      this.comment = '';
    }
    this.statusForm?.controls['reason']?.updateValueAndValidity();
    this.statusForm?.controls['comment']?.updateValueAndValidity();

    // Filter ETIC type and NIW reason lists based on new status
    this.filterEticTypeList(this.newStatus);
    this.filterNiwReasonList(this.newStatus);

    this.updateEticErrors();
  }

  updateReasonData(): void {
    this.reasonData = (this.newStatus === 'HMO' || this.newStatus === 'HMX') ? HMO_HMX_REASON_DATA : REASON_DATA;
  }

  filterEticTypeList(status: string): void {
    const allowAllEticTypes = ['AOG', 'DWN', 'HMX', 'HMD', 'HMO'];

    if (allowAllEticTypes.includes(status)) {
      this.filteredEticTypeList = [...this.eticTypeList];
    } else {
      this.filteredEticTypeList = ['FIRM', 'WA'];

      // Clear selected ETIC type if it's not in the filtered list
      if (this.selectedEticType && !this.filteredEticTypeList.includes(this.selectedEticType)) {
        this.selectedEticType = '';
        this.onEticTypeChange('');
      }
    }
  }

  filterNiwReasonList(status: string): void {
    const statusToTimersMap: { [key: string]: string[] } = NIWTimerAbbreviations.statusToTimersMap;

    if (statusToTimersMap[status]) {
      this.filteredNiwReasonList = statusToTimersMap[status];
    } else {
      this.filteredNiwReasonList = [...this.niwReasonList];
    }

    // Clear selected reason if it's not in the filtered list
    if (this.selectedNiwReason && !this.filteredNiwReasonList.includes(this.selectedNiwReason)) {
      this.selectedNiwReason = '';
    }
  }

  onEticTypeChange(eticType: string): void {
    this.selectedEticType = eticType;
    const isFirmEticOrWillAdvise = eticType === 'FIRM' || eticType === 'WA';

    if (isFirmEticOrWillAdvise) {
      // Clear and reset NIW-related fields
      this.selectedNiwReason = '';
      this.timeNeeded = 0;
      this.selectedTimeUnit = 'Hours';
    } else if (eticType && eticType !== '') {
      // Clear date/time fields for NIW types
      // Date and time fields will be hidden via template conditions
    }
    this.updateEticErrors();
  }

  preventNegativeInput(event: KeyboardEvent): void {
    if (event.key === '-' || event.key === '+' || event.key === 'e' || event.key === 'E') {
      event.preventDefault();
    }
  }

  onTimeNeededInput(event: any): void {
    const value = event.target.value;
    if (value < 0) {
      this.timeNeeded = 0;
    }
  }

  validateTubFileCheckbox(): void {
    if (this.alreadyExistsChangeRequest) {
      this.diableAddTubFileNotecheckbox = this.selectedRow?.eticDateTime !== this.selectedRow?.newEticDateTime;
    }
  }

  updateEticErrors(): void {
    this.eticErrors = [];

    if (this.statusForm?.controls['newStatus']?.touched && this.statusForm?.controls['newStatus']?.invalid) {
      this.eticErrors.push('Status is required');
    }

    if (this.statusForm?.controls['currentDate']?.touched && this.statusForm?.controls['currentDate']?.invalid) {
      this.eticErrors.push(
        this.statusForm.controls['currentDate'].errors?.['required']
          ? 'Date is required'
          : 'Please select a valid UTC date'
      );
    }

    if (this.statusForm?.controls['currentDate']?.touched && this.isDateInPast() && this.selectedEticType !== 'Not In Work (NIW)') {
      this.eticErrors.push('Selected date is in the past');
    } else if (this.isDateInPast() && this.selectedEticType !== 'Not In Work (NIW)') {
      this.eticErrors.push('ETIC date cannot be in the past');
    }

    // if (this.statusForm?.controls['timeHour']?.touched && this.statusForm?.controls['timeHour']?.invalid) {
    //   this.eticErrors.push('Hour is required');
    // }

    // if (this.statusForm?.controls['timeMinute']?.touched && this.statusForm?.controls['timeMinute']?.invalid) {
    //   this.eticErrors.push('Minute is required');
    // }

    if (this.statusForm?.controls['reason']?.touched && this.statusForm?.controls['reason']?.invalid && this.requiresReason()) {
      this.eticErrors.push('Reason is required');
    } else if (this.requiresReason() && (this.selectedReason === '' || this.selectedReason === null)) {
      this.eticErrors.push('Reason is required');
    } else {
      if (this.selectedReason === 'Others' && !this.comment) {
        this.eticErrors.push('Please specify the reason in the comment field');
      }
    }

    if (this.statusForm?.controls['comment']?.touched && this.statusForm?.controls['comment']?.invalid && this.requiresComment()) {
      this.eticErrors.push('Reason comments are required');
    } else if (this.requiresComment() && (this.comment === '' || this.comment === null)) {
      this.eticErrors.push('Reason comments are required');
    }

    if (this.showThirdScreen) {
      if (this.niwTimerForm?.controls['niwStartDate']?.touched && this.niwTimerForm?.controls['niwStartDate']?.invalid) {
        this.eticErrors.push('NIW start date is required');
      }

      if (this.niwTimerForm?.controls['niwStartDate']?.touched && this.isNiwDateInPast()) {
        this.eticErrors.push('NIW selected date is in the past');
      }

      if (this.niwTimerForm?.controls['niwTimeHour']?.touched && this.niwTimerForm?.controls['niwTimeHour']?.invalid) {
        this.eticErrors.push('NIW hour is required');
      }

      if (this.niwTimerForm?.controls['niwTimeMinute']?.touched && this.niwTimerForm?.controls['niwTimeMinute']?.invalid) {
        this.eticErrors.push('NIW minute is required');
      }

      if (this.niwTimerForm?.touched && !this.selectedNiwTimer) {
        this.eticErrors.push('Please select an NIW timer');
      }
    }

    if (this.showSecondScreen && this.tubFileForm?.controls['tubFileNote']?.touched && this.tubFileForm?.controls['tubFileNote']?.invalid) {
      this.eticErrors.push('Tub file note is required');
    }
  }

  isNiwDateInPast(): boolean {
    const nowUTC = new Date();
    const selectedUTC = new Date(Date.UTC(
      this.niwStartDate.getUTCFullYear(),
      this.niwStartDate.getUTCMonth(),
      this.niwStartDate.getUTCDate(),
      parseInt(this.niwTimeHour || '0'),
      parseInt(this.niwTimeMinute || '0')
    ));
    return selectedUTC < nowUTC;
  }

  isEticChanged(): boolean {
    if (!this.alreadyExistsChangeRequest) {
      const originalEtic = this.selectedRow?.eticDateTime || '';
      const newEtic = this.formatEticDateTime();
      return originalEtic !== newEtic;
    }
    return false;
  }

  isStatusChanged(): boolean {
    return this.newStatus !== this.selectedRow?.status && this.newStatus !== '';
  }

  isFormValid(): boolean {
    if (!this.statusForm?.valid) return false;

    if (this.isDateInPast()) return false;

    if (this.alreadyExistsChangeRequest) {
      return this.isChangeRequestValid();
    } else {
      return this.isNewRequestValid();
    }
  }

  isNewRequestValid(): boolean {
    const isStatusChanged = this.isStatusChanged();
    const isEticChanged = this.isEticChanged();

    if (!isStatusChanged && !isEticChanged) {
      return false;
    }

    if (isStatusChanged && !isEticChanged) {
      return true;
    }

    if (isEticChanged) {
      if (['AOG', 'DWN', 'HMD', 'NAS', 'NOS'].includes(this.newStatus)) {
        return !!this.selectedReason;
      } else if (['HMO', 'HMX'].includes(this.newStatus)) {
        return !!this.selectedReason && !!this.comment;
      }
    }

    return false;
  }

  isChangeRequestValid(): boolean {
    if (['AOG', 'DWN', 'HMD', 'NAS', 'NOS'].includes(this.newStatus)) {
      return !!this.selectedReason;
    } else if (['HMO', 'HMX'].includes(this.newStatus)) {
      return !!this.selectedReason && !!this.comment;
    }
    return false;
  }

  requiresReason(): boolean {
    return ['AOG', 'DWN', 'HMD', 'NAS', 'NOS', 'HMO', 'HMX'].includes(this.newStatus);
  }

  requiresComment(): boolean {
    return ['HMO', 'HMX'].includes(this.newStatus) || this.selectedReason === 'Others';
  }

  isDateInPast(): boolean {
    const selectedDate = new Date(this.currentDate);
    const selectedUTC = new Date(Date.UTC(
      selectedDate.getUTCFullYear(),
      selectedDate.getUTCMonth(),
      selectedDate.getUTCDate()
    ));
    const nowUTC = new Date();
    const todayUTC = new Date(Date.UTC(nowUTC.getUTCFullYear(), nowUTC.getUTCMonth(), nowUTC.getUTCDate()));

    return selectedUTC < todayUTC;
  }

  selectNiwTimer(timer: string): void {
    this.selectedNiwTimer = timer;
    this.niwTimerForm?.form.markAsTouched();
  }

  onNextOrSubmit(): void {
    if (!this.showSecondScreen && !this.showThirdScreen) {
      // On First Screen
      if (!this.isFormValid() || !this.newComment) {
        this.updateEticErrors();
        return;
      }
      if (this.addTubFileNote) {
        this.showSecondScreen = true;
      } else if (this.newStatus === 'AOG') {
        this.showThirdScreen = true;
      } else {
        this.onSubmit();
      }
    } else if (this.showSecondScreen) {
      // On Second Screen (Tub File Notes)
      if (!this.tubFileForm?.valid) {
        return;
      }
      if (this.newStatus === 'AOG') {
        this.showSecondScreen = false;
        this.showThirdScreen = true;
      } else {
        this.onSubmit();
      }
    } else if (this.showThirdScreen) {
      // On Third Screen (NIW Timers)
      if (!this.niwTimerForm?.valid || !this.selectedNiwTimer) {
        return;
      }
      this.onSubmit();
    }
  }

  onSubmit(): void {
    this.maintenanceEventDetailsService.getUserInfo().subscribe((userInfo: UserDto) => {
      this.changeStatusRequestDTO = {
        eventId: parseInt(this.selectedRow.eventID),
        changeType: this.selectedRow.changeType,
        accessLevel: "80",
        acn: this.selectedRow.acn,
        status: this.selectedRow.status,
        empDepartment: userInfo.departmentname,
        resMgrId: this.selectedRow.resMgrId,
        memDeskContact: this.selectedRow.memDeskContact,
        // eticDateTime: this.selectedRow.eticDateTime != null && this.selectedRow.eticDateTime !== undefined ? this.convertCustomDateToISO(this.selectedRow.eticDateTime) : null,
        eticDateTime: this.selectedEticType != 'Not In Work (NIW)' ? this.convertDateTimeToISO(
          this.currentDate, this.timeHour, this.timeMinute
        ) : null,
        eticInfo: this.getEticInfoText(),
        eticComment: this.selectedRow.curComment,
        userId: userInfo.uid,
        employeeName: userInfo.name,
        tokenId: "Bearer " + "eyJraWQiOiJmSGxIQ3JYTjV2dWlpd0d0bVFvNW1aUUU1ZEt6U1lDSXQwdURmMmJTOTIwIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pquwjFsAcOUjJKRSakMKsCrErLm33G6F0u82KWB-o_SlaLsS9jZkYJ6SE4lOMLtq7MDEQWuFz7EYL95fqrp0CA2KJK2zrJI-Tx_c-UCH2BK_VlgGKv20-wsVVggZBQB8m-bfBBdLmztrv7N63ALYRhX8ygf7FALtIs4LbHF0mfc5-r1Ma3anSEkseiQt8vLUILdbztNQlElnjnhcgp9kB6AwLL-6ECzUSK9w-BQCfisDUNfib3cjMPTqciW7NLJH7uX4zZZaitSR5tSl_3cIVNZBlTi7p80uThQlS7JlB1i7u2LXEt-rNNwKSOwMzMU2_BI7eDpqDiys8wXBbxaVdQ",
        newStatus: this.newStatus,
        newEticDateTime: this.currentDate ? this.convertselectedNewETicDateTimeToISO() : null,
        newEticInfo: this.Info,
        newEticComment: this.newComment,
        OST: this.selectedRow.ost,
        newOST: this.newOST ? 'Y' : 'N',
        eticRsnCd: this.selectedRow.eticReasonCd,
        newEticRsnCd: this.selectedReason,
        eticRsnComment: this.selectedRow.eticRsnComments,
        newEticRsnComment: this.comment,
        changeRequestLastUpdated: this.selectedRow.changeRequestLastUpdateDtTime.split(".")[0],
        requestStatus: this.selectedRow.requestStatus,
        activeTimerId: this.newStatus === 'AOG' ? (this.niwTimerResponse.eventActiveTimerDataList.length > 0 ? this.niwTimerResponse.eventActiveTimerDataList[0].timerId : null) : null,
        timerId: this.newStatus === 'AOG' ? (this.niwTimerResponse.timerDataList.find(timer => timer.timerName === this.selectedNiwTimer)?.timerId || null) : null,
        timerName: this.newStatus === 'AOG' ? this.selectedNiwTimer : null,
        timerLastUpdated: this.newStatus === 'AOG' ? this.niwTimerResponse.eventActiveTimerDataList.length > 0 ? this.convertTimestampToUTCString(this.niwTimerResponse.eventActiveTimerDataList[0].lastUpdateDtTm) : null : null,
        timerCreatedDateTime: this.newStatus === 'AOG' ? this.niwTimerResponse.eventActiveTimerDataList.length > 0 ? this.convertTimestampToUTCString(this.niwTimerResponse.eventActiveTimerDataList[0].eventTimersPk.creationDtTm) : null : null,
        timerStartDateTime: this.newStatus === 'AOG' ? this.formatDate(this.niwStartDate) + 'T' + this.niwTimeHour + ':' + this.niwTimeMinute + ':00' : null,
        timerStopDateTime: null,
        isPendingRequest: this.selectedRow.requestStatus === 'S' ? true : false,
        eticEnteredInError: this.previousETICinError,
        modifyPendingEvent: this.alreadyExistsChangeRequest ? true : false,
        cancelPendingEvent: false,
        statusModified: (this.selectedRow?.status ?? '') !== this.newStatus,
        eticModified: (this.selectedRow?.eticDateTime ?? '') !== this.formatEticDateTime(),
        commentModified: (this.selectedRow?.curComment ?? '') !== this.newComment,
        ostModified: (this.selectedRow?.ost ?? '') !== (this.newOST ? 'Y' : 'N'),
        eticRsnCdModified: (this.selectedRow?.eticReasonCd ?? '') !== this.selectedReason,
        eticRsnCommentModified: (this.selectedRow?.eticRsnComments ?? '') !== this.comment,
        tfNotesList: [this.tubFileNote],
        createdDateTime: this.getCurrentUTCDateTime(),
        changedDateTime: this.getCurrentUTCDateTime(),
      };
      this.maintenanceEventListService.changeStatusOrEticRequest(this.changeStatusRequestDTO).subscribe((response: any) => {
        const isSuccess = !!response.data.CHANGE_EVENT;
        const message = isSuccess
          ? `Successfully updated etic / status request for ACN - ${this.selectedRow.acn}`
          : response.data.ERROR;
        this.dialogRef.close({ isSuccess: isSuccess, message: message });
      });
    });
  }

  convertDateTimeToISO(date: any, hour: any, minute: any): string | null {
    if (!date || !hour || !minute) {
      return null;
    }
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    const year = date.getUTCFullYear();
    return `${year}-${month}-${day}T${hour}:${minute}:00`;
  }

  getEticInfoText() {
    if(this.selectedEticType == 'FIRM' || this.selectedEticType == 'WA') {
      return this.selectedEticType;
    } else {
      const tu = (this.selectedTimeUnit == 'Hours') ? 'H' : 'D';
      return NIWTimerAbbreviations.getAbbreviation(this.selectedNiwReason) + '+' + this.timeNeeded + tu;
    }
    return '';
  }

  goToPrevious(): void {
    if (this.showThirdScreen) {
      this.showThirdScreen = false;
      if (this.addTubFileNote) {
        this.showSecondScreen = true;
      }
    } else if (this.showSecondScreen) {
      this.showSecondScreen = false;
    }
  }

  goBack(): void {
    this.showSecondScreen = false;
    this.showThirdScreen = false;
    this.dialogRef.close();
  }

  validateDate(event: any): void {
    this.onDateChange(event);
    this.selectedReason = '';
    this.comment = '';
    this.updateEticErrors();
  }

  formatDate(date: Date): string {
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    const year = date.getUTCFullYear();
    return `${year}-${month}-${day}`;
  }

  formatEticDateTime(): string {
    const month = (this.currentDate.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = this.currentDate.getUTCDate().toString().padStart(2, '0');
    const year = this.currentDate.getUTCFullYear().toString();
    return `${this.timeHour}${this.timeMinute}/${month}${day}${year.slice(-2)}`;
  }

  convertCustomDateToISO(input: string): string | null {
    const [timeStr, dateStr] = input.split('/');
    if (!timeStr || !dateStr || timeStr.length !== 4 || dateStr.length !== 6) {
      return null; // Invalid format
    }

    const hour = timeStr.slice(0, 2);
    const minute = timeStr.slice(2, 4);
    const month = dateStr.slice(0, 2);
    const day = dateStr.slice(2, 4);
    const year = '20' + dateStr.slice(4, 6); // Assuming 20xx century

    const isoString = `${year}-${month}-${day}T${hour}:${minute}:00`;
    return isoString;
  }

  convertselectedNewETicDateTimeToISO(): string | null {
    if (!this.currentDate || !this.timeHour || !this.timeMinute) {
      return null;
    }
    const month = (this.currentDate.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = this.currentDate.getUTCDate().toString().padStart(2, '0');
    const year = this.currentDate.getUTCFullYear();
    return `${year}-${month}-${day}T${this.timeHour}:${this.timeMinute}:00`;
  }

  getCurrentUTCDateTime(): string {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  convertTimestampToUTCString(timestamp: number): string {
    const date = new Date(timestamp);
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    const hours = String(date.getUTCHours()).padStart(2, '0');
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');
    const seconds = String(date.getUTCSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  onUppercaseChange(field: 'comment' | 'currentComment' | 'newComment', value: string): void {
    this[field] = value.toUpperCase();
    if (field === 'comment' || field === 'newComment') {
      this.updateEticErrors();
    }
  }


  getTubFileNotes(): void {
    this.maintenanceEventDetailsService.getTubfileNotes(parseInt(this.selectedRow.eventID)).subscribe(response => {
      this.tubfilenotesresponse = response || [];
      // Group notes by noteId
      const groupedByNoteId: { [key: number]: TubFileNotesResponseDto[] } = {};
      this.tubfilenotesresponse.forEach(note => {
        note.safeTfNote = note.tfNote; // Handle null tfNote
        const noteId = note.noteId;
        if (!groupedByNoteId[noteId]) {
          groupedByNoteId[noteId] = [];
        }
        groupedByNoteId[noteId].push(note);
      });

      // Convert grouped notes to groupedNotes array
      this.groupedNotes = Object.values(groupedByNoteId).map(group => ({
        lastUpdateDtTm: group[0].lastUpdateDtTm,
        empName: group[0].empName,
        notes: group,
        eventTfNotesPk: group[0].eventTfNotesPk,
        noteId: group[0].noteId
      }));

      // Sort grouped notes by lastUpdateDtTm
      this.groupedNotes = this.groupedNotes.sort((a, b) => new Date(b.lastUpdateDtTm).getTime() - new Date(a.lastUpdateDtTm).getTime());

      // Sort grouped notes by lastUpdateDtTm
      this.sortNotes();

    });
  }

  getAogNiwTimers(): void {
    this.maintenanceEventDetailsService.getAogNiwTimers(parseInt(this.selectedRow.eventID)).subscribe((response: NiwTimerResponse) => {
      this.niwTimerResponse = response;
      this.niwTimerOptions = response.timerDataList
        .filter((timer: TimerData) => timer.timerName.startsWith('Parts'))
        .map((timer: TimerData) => timer.timerName);
      this.selectedNiwTimer = this.niwTimerOptions[0] || '';
    });
  }

  sortNotes(): void {
    if (this.groupedNotes) {
      this.groupedNotes = [...this.groupedNotes].sort((a, b) => {
        const dateA = new Date(a.lastUpdateDtTm).getTime();
        const dateB = new Date(b.lastUpdateDtTm).getTime();
        return this.sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      });
    }
  }

  onNoteInputChange(): void {
    if (this.tubFileNote) {
      this.tubFileNote = this.tubFileNote.toLowerCase();
    }
  }

  getLinkedDiscrepanciedList(): void {
    this.maintenanceEventDetailsService.getDiscrepanciesTableList(parseInt(this.selectedRow?.acn), this.selectedRow?.eventID).subscribe({
      next: (response: OpenDiscrepanciesResponseDao) => {
        if (response.linkedDiscrepancyList.length > 0) {
          response.linkedDiscrepancyList.forEach((discrepancy: DiscrepanciesList) => {
            const combinedDiscrepancyText = discrepancy.text?.length >= 0 ? discrepancy.text.join(' ') : "";
            this.linkedDiscrepancies.push(discrepancy.discType + ' / ' + discrepancy.ata+' / '+ discrepancy.number + ' ('+combinedDiscrepancyText+')');
          });
        }
        this.linkedDiscrepancies.push('General Notes');
      },
      error: (error: any) => {
        console.error('Error fetching linked discrepancies:', error);
      }
    });
  }

  onDiscChange(event:any){
    this.tubFileNote=event+':';
  }

}
