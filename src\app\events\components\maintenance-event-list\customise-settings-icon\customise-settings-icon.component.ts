import { Component, EventEmitter, Output } from '@angular/core';
import { IHeaderParams } from 'ag-grid-community';

@Component({
  selector: 'app-customise-settings-icon',
  templateUrl: './customise-settings-icon.component.html',
  styleUrl: './customise-settings-icon.component.scss',
  standalone: false
})
export class CustomiseSettingsIconComponent {

  params: any; // Ag-Grid injects params here

  agInit(params: any): void {
    this.params = params;
  }

  handleClick() {
    if (this.params?.context?.onSettingsClick) {
      this.params.context.onSettingsClick();
    }
  }

}
