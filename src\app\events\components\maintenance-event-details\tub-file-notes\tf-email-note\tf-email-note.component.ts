import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-tf-email-note',
  standalone: false,
  templateUrl: './tf-email-note.component.html',
  styleUrl: './tf-email-note.component.scss'
})
export class TfEmailNoteComponent {
  selectedOption: 'all' | 'selected' = 'selected';

  constructor(private dialogRef: MatDialogRef<TfEmailNoteComponent>) {}

  onOk() {
    this.dialogRef.close(this.selectedOption);
  }
}
