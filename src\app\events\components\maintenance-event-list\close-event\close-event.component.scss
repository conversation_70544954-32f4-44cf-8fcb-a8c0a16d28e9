.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f4f7fc;
  font-family: 'Roboto', sans-serif;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  overflow: visible;
  padding: 16px;
  box-sizing: border-box;

  @media (max-width: 768px) {
    width: 90vw;
    height: 70vh;
    min-width: 400px;
  }
}

.dialog-header {
  text-align: center;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 8px 16px;
  color: #ffffff;
  background: linear-gradient(135deg, #2a2a72, #6a5acd);
  border-radius: 12px 12px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  &:hover {
    background: linear-gradient(135deg, #1c1c54, #5a4ab5);
  }

  .close-button {
    position: absolute;
    right: 8px;
    color: #ffffff;

    &:hover {
      color: #ff8c00;
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.dialog-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  min-height: 0;
}

.stepper-container {
  width: 100%;
  flex: 1;
  background: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  min-height: 0;

  ::ng-deep .mat-horizontal-stepper-header-container {
    padding: 0.2rem 0.5rem;
    position: relative;
    background: linear-gradient(135deg, #f8f7ff, #ffffff);
    border-bottom: 1px solid #e0e0e0;
    flex-shrink: 0;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    min-height: 50px;
    max-height: 50px;
  }

  ::ng-deep .mat-horizontal-content-container {
    padding: 0 !important;
  }

  ::ng-deep .mat-step-header {
    position: relative;
    padding: 2px 4px;
    height: 24px;
    flex: 1;
    min-width: 0;
    margin: 0 1px;
    max-width: calc(20% - 2px);

    &.mat-step-header[aria-selected="true"] {
      background: #B7BBE3;
      border-radius: 6px;
    }

    &:hover {
      background: #B7BBE3;
      transform: translateY(-1px);
    }

    .mat-step-icon {
      background: #6c49b9;
      color: #ffffff;
      width: 18px;
      height: 18px;
      font-size: 0.7rem;
      font-weight: bold;
      line-height: 18px;
      box-shadow: 0 2px 6px rgba(63, 40, 118, 0.2);
    }

    .mat-step-label {
      font-size: 0.65rem;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;

      @media (max-width: 1200px) {
        font-size: 0.6rem;
      }

      @media (max-width: 1024px) {
        font-size: 0.55rem;
      }

      @media (max-width: 768px) {
        font-size: 0.5rem;
      }
    }
  }

  ::ng-deep .mat-step-header.cdk-keyboard-focused,
  ::ng-deep .mat-step-header.cdk-program-focused {
    background: #B7BBE3;
  }
}

.scroll-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 4px 12px 4px;
  background: #fafafa;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
}

.step-content {
  min-height: 250px;
  margin: 0 8px 20px 8px;
  background: white;
  border-radius: 8px;
  padding: 16px 20px; 
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: calc(100% - 16px);
  box-sizing: border-box;

  &:last-child {
    margin-bottom: 0;
  }
}

.step-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;

  h3 {
    color: #2a2a72;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 6px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  p {
    color: #666;
    font-size: 12px;
    margin: 0;
  }
}

.form-section {
  padding: 0 4px;

  .form-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    width: 100%;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-field {
    flex: 1;
    min-width: 0;

    &.full-width {
      width: 100%;
    }

    ::ng-deep .mat-mdc-form-field {
      width: 100%;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      width: 100%;
    }
  }
}

.confirmation-section {
  .warning-box {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin-bottom: 24px;

    mat-icon {
      color: #856404;
      margin-top: 2px;
    }

    .warning-content {
      flex: 1;

      h4 {
        color: #856404;
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      p {
        color: #856404;
        font-size: 14px;
        margin: 0;
      }
    }
  }

  .confirmation-checkbox {
    margin-bottom: 16px;

    ::ng-deep .mat-checkbox-label {
      font-size: 14px;
      line-height: 1.4;
    }
  }
}

.content-container {
  width: calc(100% - 10px);
  margin: 5px;
  padding: 0.5rem;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-color: #6c49b9 #f8f7ff;
  border-radius: 10px;
  border: 2px solid #B7BBE3;
  background: #f8f7ff;
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 0;

  &::-webkit-scrollbar {
    width: 14px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f7ff;
    border-radius: 12px;
  }

  &::-webkit-scrollbar-thumb {
    background: #6c49b9;
    border-radius: 12px;
    border: 3px solid #f8f7ff;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #3F2876;
  }
}

.form-container {
  width: 100%;
  margin: 0.5rem;
}

.step-section {
  margin-bottom: 2rem;
}

.step-title {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3F2876, #6c49b9);
  color: #ffffff;
  border-radius: 12px;
  font-weight: 700;
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 20px rgba(63, 40, 118, 0.35);
  position: relative;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
  }
}

.form-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 6px 16px rgba(63, 40, 118, 0.2);
  margin-bottom: 1rem;
}

.step-header {
  margin-bottom: 1.5rem;

  h3 {
    color: #3F2876;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }
}

.form-section {
  margin-bottom: 1.5rem;

  .section-subtitle {
    color: #3F2876;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0;
  }
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-field {
  flex: 1;
  transition: all 0.3s ease;

  &.full-width {
    width: 100%;
  }

  ::ng-deep .mat-form-field-wrapper {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
  }

  &:hover ::ng-deep .mat-form-field-wrapper {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
  }

  ::ng-deep .mat-form-field-outline {
    border: 1px solid rgba(106, 90, 205, 0.25) !important;
    border-radius: 8px;
  }

  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(106, 90, 205, 0.25) !important;
        border-width: 1px !important;
      }
    }

    .mat-mdc-form-field-focus-overlay {
      background-color: transparent;
    }
  }

  &:hover ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
    }
  }

  ::ng-deep .mat-form-field-infix {
    padding: 6px 0;
  }

  ::ng-deep .mat-select-value,
  ::ng-deep input,
  ::ng-deep textarea {
    background-color: lavender !important;
    color: #6a5acd !important;
    font-size: 0.85rem !important;
    padding: 3px 0 !important;
    border-radius: 6px;
    font-weight: 500;
    text-align: center !important;
  }

  ::ng-deep .mat-form-field-label,
  ::ng-deep .mat-mdc-form-field .mat-mdc-floating-label {
    color: #475569 !important;
    font-weight: 500;
    font-size: 0.8rem !important;
  }

  &:hover ::ng-deep .mat-form-field-label,
  &:hover ::ng-deep .mat-mdc-form-field .mat-mdc-floating-label {
    color: #6a5acd !important;
  }

  ::ng-deep .mat-select-arrow {
    color: #6a5acd;
  }

  ::ng-deep .mat-datepicker-toggle .mat-icon-button {
    color: #6a5acd;
  }

  ::ng-deep .mat-datepicker-toggle:hover .mat-icon-button {
    color: #ff8c00;
  }

  ::ng-deep .mat-form-field.mat-focused {
    .mat-form-field-outline,
    .mdc-notched-outline .mdc-notched-outline__leading,
    .mdc-notched-outline .mdc-notched-outline__notch,
    .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: #6a5acd !important;
      border-width: 2px !important;
    }
  }

  &:hover ::ng-deep .mat-form-field:not(.mat-focused) {
    .mat-form-field-outline,
    .mdc-notched-outline .mdc-notched-outline__leading,
    .mdc-notched-outline .mdc-notched-outline__notch,
    .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: rgba(106, 90, 205, 0.5) !important;
    }
  }

  ::ng-deep .mat-mdc-form-field {
    width: 100%;

    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }
  }

  ::ng-deep .purple-text {
    background-color: lavender !important;
    color: #6a5acd !important;
    height: 100%;
    border-radius: 8px;
    font-size: 0.85rem !important;
    font-weight: 500;
    padding: 3px 0 !important;
    text-align: center !important;
  }

  ::ng-deep textarea.purple-text {
    text-align: left !important;
    vertical-align: top !important;
    padding: 8px !important;
    resize: vertical;
  }

  ::ng-deep input[readonly].purple-text {
    text-align: center !important;
    justify-content: center;
  }

  ::ng-deep input[readonly],
  ::ng-deep textarea[readonly] {
    background-color: lavender !important;
    color: #6a5acd !important;
    opacity: 0.8;
    cursor: default;
    font-weight: 500;
    pointer-events: none;
  }
}

.checkbox-row {
  margin-top: 1rem;
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
}

.form-checkbox {
  margin: 4px 0;

  ::ng-deep .mat-checkbox-checked .mat-checkbox-background {
    background-color: #ff8c00;
  }

  ::ng-deep .mat-checkbox-frame {
    border-color: rgba(106, 90, 205, 0.4);
  }

  &:hover ::ng-deep .mat-checkbox-frame {
    border-color: #6a5acd;
  }

  ::ng-deep .mat-checkbox-label {
    color: #3F2876;
    font-weight: 500;
    font-size: 0.9rem;
  }
}

.active-timers-info {
  width: 100%;
  margin-bottom: 1rem;

  .section-subtitle {
    color: #3F2876;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0;
  }
}

.timer-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.timer-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap; 

  .form-field {
    flex: 1;
    min-width: 140px;

    &:first-child {
      flex: 2;
      min-width: 200px;
    }

    &:nth-child(2), &:nth-child(3) {
      flex: 1.2;
      min-width: 120px;
    }

    &:nth-child(4), &:nth-child(5) {
      flex: 1.2;
      min-width: 120px;
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background: #ffffff;
  flex-shrink: 0;

  .cancel-button {
    color: #666;
    padding: 8px 16px;
  }

  .save-button {
    background: linear-gradient(135deg, #2a2a72, #6a5acd);
    color: white;
    padding: 8px 16px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1c1c54, #5a4ab5);
    }

    &:disabled {
      background: #ccc;
      color: #666;
    }
  }
}

::ng-deep .step-badge {
  .mat-badge-content {
    background: #4caf50;
    color: white;
    font-size: 10px;
    font-weight: bold;
  }
}

::ng-deep .close-event-dialog-container {
  .mat-mdc-dialog-container {
    padding: 16px;
    overflow: visible;
    border-radius: 12px;
    box-sizing: border-box;

    .mat-mdc-dialog-surface {
      border-radius: 12px;
      overflow: visible;
      padding: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }
  }

  .mat-horizontal-stepper-content {
    transform: none !important;
    transition: none !important;
    animation: none !important;
  }

  .mat-stepper-horizontal {
    .mat-horizontal-stepper-content {
      transform: translateX(0) !important;
      transition: none !important;
    }
  }

  .mat-horizontal-stepper-content-container {
    transform: none !important;
    transition: none !important;
  }
}

::ng-deep .mat-stepper-horizontal-line {
  flex: 1;
  min-width: 20px;
  margin: 0 4px;

  @media (max-width: 1200px) {
    min-width: 16px;
    margin: 0 2px;
  }

  @media (max-width: 1024px) {
    min-width: 12px;
    margin: 0 1px;
  }

  @media (max-width: 768px) {
    min-width: 8px;
    margin: 0;
  }
}

::ng-deep .close-event-dialog-container {
  .cdk-overlay-pane {
    max-width: 100vw !important;
    max-height: 100vh !important;
  }

  .mat-mdc-form-field {
    width: 100% !important;

    .mat-mdc-text-field-wrapper {
      width: 100% !important;
    }

    .mat-mdc-form-field-infix {
      width: 100% !important;
      min-width: 0 !important;
    }
  }

  .mat-mdc-select {
    width: 100% !important;
  }
}

// Discrepancies button container styling
.discrepancies-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem; // Space between buttons
  padding: 1rem 0;
  margin-top: 1rem;

  .discrepancies-button {
    // Match the exact styling of the Close Event button (.save-button)
    background: linear-gradient(135deg, #2a2a72, #6a5acd);
    border-radius: 50px;
    color: white;
    padding: 8px 16px;
    min-width: 120px;
    font-weight: 500;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1c1c54, #5a4ab5);
    }

    &:disabled {
      background: #ccc;
      color: #666;
    }

    mat-icon {
      margin-right: 0.5rem;
      font-size: 18px;
    }
  }
}
