import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MaintenanceEventListService } from '../../../services/maintenance-event-list.service';

@Component({
  selector: 'app-hide-button',
  templateUrl: './hide-button.component.html',
  styleUrl: './hide-button.component.scss',
  standalone: false
})
export class HideButtonComponent {

  options: { name: string, selected: boolean, displayName: string }[] = [];

  constructor(
    public dialogRef: MatDialogRef<HideButtonComponent>,
    private maintenanceEventListService: MaintenanceEventListService,
    @Inject(MAT_DIALOG_DATA) public data: { options: string[] }
  ) {
    // Retrieve stored column state
    const eventListColumnHideSessionStorage = this.maintenanceEventListService.getMaintenanceListSettingsOptionsFromStorage();
  
    if (eventListColumnHideSessionStorage != null && Array.isArray(eventListColumnHideSessionStorage)) {
      // Initialize options with stored values
      this.options = [...eventListColumnHideSessionStorage];
    } else {
      // Default initialization
      this.options = data.options.map(option => ({
        name: option,
        selected: true,
        displayName: this.getDisplayName(option)
      }));
    }
  }  

  drop(event: CdkDragDrop<{ name: string, selected: boolean }[]>) {
    moveItemInArray(this.options, event.previousIndex, event.currentIndex);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onApply(): void {
    this.dialogRef.close(this.options);
  }

  showAll(): void {
    this.options.forEach(option => option.selected = true);
    this.dialogRef.close(this.options);
  }

  getDisplayName(name: string): string {
    let displayName = "";
    switch (name) {
      case "ACN":
        displayName = "acn";
        break;
      case "Fleet":
        displayName = "fleetDesc";
        break;
      case "Sta":
        displayName = "station";
        break;
      case "Status":
        displayName = "status";
        break;
      case "Owner":
        displayName = "owner";
        break;
      case "Flt Out/Dt":
        displayName = "flightDetails";
        break;
      case "Comment":
        displayName = "curComment";
        break;
      case "Start":
        displayName = "startDateTime";
        break;
      case "Dept":
        displayName = "flightDepartureDetails";
        break;
      case "ETIC":
        displayName = "eticDateTime";
        break;
      case "OST":
        displayName = "ost";
        break;
      case "Duration":
        displayName = "durationData";
        break;
      case "Alerts":
        displayName = "alerts";
        break;
    }
    return displayName;
  }
  
}
