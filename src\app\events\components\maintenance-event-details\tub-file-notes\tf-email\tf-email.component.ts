import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { TfSendEmail } from '../../../../dto/TfSendEmail';
import { MaintenanceEventDetailsService } from '../../../../services/maintenance-event-details.service';
import { ToastrMessageService } from '../../../../../app-layout/services/toastr-message.service';

@Component({
  selector: 'app-tf-email',
  standalone: false,
  templateUrl: './tf-email.component.html',
  styleUrl: './tf-email.component.scss'
})
export class TfEmailComponent {
     email = {
    to: '',
    from: '',
    subject: '',
  };
  currentDateTime: string;
  safeBody: SafeHtml;
  body: string='';
  tfSendEmailDto !: TfSendEmail;
  @Input() detailViewData: any;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitizer: DomSanitizer,
    private service: MaintenanceEventDetailsService,
    private toastrMessageService: ToastrMessageService,
    private dialogRef: MatDialogRef<TfEmailComponent>) {
    const now = new Date();
    this.currentDateTime = now.toLocaleString();
    this.email.from=this.data.mailId.toString();
    this.email.to=this.data.mailId.toString();
    this.safeBody=this.sanitizer.bypassSecurityTrustHtml(this.data.tfNote);
    this.email.subject= `Tub File Notes - ${this.data.acn}`;
  }

  ngOnInit() {
    this.updateSafeBody();
  }

  updateSafeBody() {
    this.safeBody = this.sanitizer.bypassSecurityTrustHtml(this.body);
  }

  sendEmail() {
    this.tfSendEmailDto = new TfSendEmail();
    this.tfSendEmailDto.emailTo = this.email.to;
    this.tfSendEmailDto.emailFrom = this.email.from;
    this.tfSendEmailDto.emailSubject = this.email.subject;
    this.tfSendEmailDto.emailBody=this.data.tfNote;
      this.tfSendEmailDto.emailEventId= 445230;
    this.service.sendEmail(this.tfSendEmailDto ).subscribe( {
        next: (res) =>{
        if (res === true || res.success) {
          this.dialogRef.close(); 
          this.toastrMessageService.success('Email sent successfully!')
        } else {
          this.toastrMessageService.error('Failed to send email')
        }
    }});
  }

  onBodyChange(newValue: string) {
    this.body = newValue;
    this.updateSafeBody();
  }

  quillModules = {
    toolbar: {
      container: [
        ['attachment'] 
      ],
      handlers: {
        'attachment': () => {
        }
      }
    }
  };
}
