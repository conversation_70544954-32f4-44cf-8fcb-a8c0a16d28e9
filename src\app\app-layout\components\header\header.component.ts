import { Component, OnInit } from '@angular/core';
import { AppLayoutService } from '../../services/app-layout.service';
import { MainService } from '../../services/main.service';
import { UserLoginService } from '../../services/user-login.service';
import { WebsocketService } from '../../services/websocket.service';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrl: './header.component.scss',
    standalone: false
})
export class HeaderComponent implements OnInit {

  loggedInUserName: string = "";
  isSideNavClosed: boolean = false;
  showUserNameSection: boolean = false;

  notifications: any[] = [];
  hasUnreadNotifications = false;
  showNotificationPanel = false;
  
  constructor(private appLayoutService: AppLayoutService, private mainService: MainService, private userLoginService: UserLoginService,
    private websocketService: WebsocketService
  ) {}

  ngOnInit(): void {
    this.getLoggedInUserDetails();
    this.userLoginService.retrievedUserDetails$.subscribe((retrievedUserDetails: any) => {
      retrievedUserDetails != null && retrievedUserDetails != "" ? this.loggedInUserName = retrievedUserDetails.name.trim() : this.loggedInUserName = "";
    });
    this.appLayoutService.sideNavClosedObservable$.subscribe((isClosed) => {
      this.isSideNavClosed = isClosed !== null ? isClosed : false;
      isClosed ? setTimeout(() => {this.showUserNameSection = false}, 300) : this.showUserNameSection = true;
    });
    this.websocketService.notifications$.subscribe(notification => {
      this.notifications.unshift(notification); // Add to top
      this.notifications = this.notifications.slice(0, 10); // Limit to 10
      this.hasUnreadNotifications = true;
    });
  }

  onBellClick() {
    this.showNotificationPanel = !this.showNotificationPanel;
    if (this.showNotificationPanel) {
      this.hasUnreadNotifications = false;
    }
  }

  getLoggedInUserDetails() {
    const userDetails = this.mainService.getUserDetailsFromStorage();
    if (userDetails != null) {
      this.loggedInUserName = userDetails.name.trim();
    } else {
      this.loggedInUserName = "";
    }
  }
}
