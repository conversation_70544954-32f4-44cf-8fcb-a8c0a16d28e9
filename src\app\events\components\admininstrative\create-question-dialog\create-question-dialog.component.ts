import { Component, ElementRef, Inject, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { questions } from '../../../dto/questionsDto';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AdministrativeService } from '../../../services/administrative.service';

// Add SpeechRecognition types for TypeScript
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}
type SpeechRecognition = any;
type SpeechRecognitionEvent = any;
type SpeechRecognitionErrorEvent = any;

@Component({
  selector: 'app-create-question-dialog',
  standalone: false,
  templateUrl: './create-question-dialog.component.html',
  styleUrl: './create-question-dialog.component.scss'
})
export class CreateQuestionDialogComponent {

  questionText: string = '';
  questionGrp: string = 'TEXT';
  isRequired: boolean = false;
  answers: string = '';
  answerTiles: string[] = [];
  questionTxtFromSpeech: string = '';
  isListening: boolean = false;
  error: string | null = null;
  question: questions = new questions();
  private destroy$ = new Subject<void>();
  private recognition: SpeechRecognition | null = null;

  @ViewChild('questionTextArea') questionTextArea!: ElementRef<HTMLTextAreaElement>;

  constructor(
    public dialogRef: MatDialogRef<CreateQuestionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { editedQuestion: questions | null; isEdit: boolean },
    public administrativeService: AdministrativeService
  ) {
    const SpeechRecognitionConstructor =
      (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (SpeechRecognitionConstructor) {
      this.recognition = new SpeechRecognitionConstructor();
    } else {
      this.error = 'Speech recognition is not supported in this browser.';
    }
  }

  ngOnInit(): void {
    if (this.data?.editedQuestion) {
      this.question = { ...this.data.editedQuestion };
      this.questionText = this.question.questionTxt ?? '';
      this.questionGrp = this.question.questionGrp ?? 'TEXT';
      this.isRequired = this.question.required ?? false;
      this.answers = this.question.answers?.map((answer) => answer.answerTxt).join(', ') ?? '';
      this.answerTiles = this.question.answers?.map((answer) => answer.answerTxt) ?? [];
    }

    if (this.recognition) {
      this.setupSpeechRecognition();
    }
  }

  setupSpeechRecognition(): void {
    if (!this.recognition) return;

    this.recognition.lang = 'en-US';
    this.recognition.continuous = true;
    this.recognition.interimResults = true;

    this.recognition.onresult = (event: SpeechRecognitionEvent) => {
      let interimTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          this.questionTxtFromSpeech += event.results[i][0].transcript;
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
      this.questionTextArea.nativeElement.value = this.questionTxtFromSpeech + interimTranscript;
      this.questionText = this.questionTextArea.nativeElement.value;

      if (this.questionText.toLowerCase().includes('stop jarvis')) {
        this.stopSpeechRecognition();
        this.questionTextArea.nativeElement.value = this.questionTextArea.nativeElement.value
          .replace(/stop jarvis/i, '')
          .trim();
        this.questionText = this.questionTextArea.nativeElement.value;
      }
    };

    this.recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      this.error = `Speech recognition error: ${event.error}`;
      this.isListening = false;
    };

    this.recognition.onend = () => {
      this.isListening = false;
    };
  }

  startSpeechRecognition(): void {
    if (this.isListening || !this.recognition) return;
    this.isListening = true;
    this.error = null;
    this.questionTxtFromSpeech = this.questionTextArea.nativeElement.value;
    this.recognition.start();
  }

  stopSpeechRecognition(): void {
    if (this.recognition) {
      this.recognition.stop();
      this.isListening = false;
    }
  }

  addAnswers(): void {
    this.answerTiles = this.answers
      .split(',')
      .map((answer) => answer.trim())
      .filter((answer) => answer);
  }

  save(): void {
    this.question.questionTxt = this.questionText;
    this.question.questionGrp = this.questionGrp;
    this.question.required = this.isRequired;

    if (
      (this.questionGrp === 'TEXT' ||
        this.questionGrp === 'DATE' ||
        this.questionGrp === 'DATE_RANGE') &&
      (!this.data || !this.data.isEdit)
    ) {
      this.question.answers = [{ answerId: 0, answerTxt: '' }];
    } else {
      this.question.answers = this.answerTiles.map((answer, index) => ({
        answerId:
          this.question.answers && this.question.answers[index]
            ? this.question.answers[index].answerId
            : 0,
        answerTxt: answer || ''
      }));
    }

    if (this.data?.isEdit) {
      console.log('The updated question is', this.question);
      this.administrativeService.updateQuestion(this.question);
    }
    this.dialogRef.close({ question: this.question });
  }

  cancel(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    this.stopSpeechRecognition();
    this.destroy$.next();
    this.destroy$.complete();
  }
  
}
