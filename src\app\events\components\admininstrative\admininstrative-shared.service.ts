import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AdministrativeService } from '../../services/administrative.service';
import { UserIntakeForm } from '../../dto/UserIntakeFormDto';

@Injectable({
  providedIn: 'root'
})
export class AdmininstrativeSharedService {

  private userIntakeFormsSubject = new BehaviorSubject<UserIntakeForm[]>([]);
  userIntakeForms$ = this.userIntakeFormsSubject.asObservable();

  constructor(private administrativeService: AdministrativeService) {
      this.refreshUserIntakeForms();
  }

  refreshUserIntakeForms(): void {
    this.administrativeService.getAllUserIntakeForms().subscribe(
        data => {
            this.userIntakeFormsSubject.next(data);
        },
        error => {
            console.error('Error loading all intake forms: ', error);
        }
    );
}
}